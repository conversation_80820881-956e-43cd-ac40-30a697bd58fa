# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Versa Portal wireframe application built with React, TypeScript, and Vite. It's a multi-tenant admin dashboard system with role-based access for both root administrators and organization administrators. The system includes a comprehensive pipeline management platform for creating, managing, and orchestrating function libraries for satellite image processing workflows.

## Tech Stack

- **Frontend**: React 18 with TypeScript
- **Build Tool**: Vite
- **UI Components**: shadcn/ui + Radix UI primitives
- **Styling**: Tailwind CSS
- **State Management**: React Query (TanStack Query)
- **Routing**: React Router v6
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React

## Development Commands

```bash
# Install dependencies
npm i

# Start development server
npm run dev

# Build for production
npm run build

# Build for development
npm run build:dev

# Lint code
npm run lint

# Preview production build
npm run preview
```

## Testing

This project currently has no test framework configured. When adding tests, verify if one exists by checking package.json for test-related dependencies before suggesting a testing framework.

## Deployment

This is a Lovable.dev project with automatic deployment. Changes pushed to the repository are automatically deployed. Manual deployment can be triggered via the Lovable interface: Project > Share > Publish.

## Architecture

### Authentication System

The application uses a dummy authentication system with predefined users:
- **Alice Johnson** (<EMAIL>) - Root Administrator
- **John Smith** (<EMAIL>) - Organization Administrator
- **Sarah Davis** (<EMAIL>) - Project User

Authentication state is managed in `App.tsx` and determines which components are rendered. Login/logout flow controls access to the main application.

### Core Application Structure

- **App.tsx**: Authentication wrapper with user state management
- **pages/Login.tsx**: Authentication interface with demo user selection
- **pages/Index.tsx**: Main authenticated page with role-based section routing
- **components/**: Feature-specific components organized by functionality

### Role-Based Architecture

The application supports three distinct user roles with different capabilities:

**Root Administrator**:
- System-wide management (organizations, global users, system settings)
- Pipeline Management:
  - Job Libraries (create/manage function libraries)
  - Library Assignments (assign libraries to organizations)
  - Job Definitions (create function orchestration workflows)

**Organization Administrator**:
- Tenant-specific management (tenants, satellite image processing projects, users)
- Pipeline Management (job definitions for image processing workflows)
- Project Management (user assignments, tenant assignments, job assignments)
- Limited to organization-scoped operations

**Project User**:
- Access to assigned satellite image processing projects with granular role-based permissions:
  - **Admin Role**: Full project control including file upload, job triggering, data management
  - **Reader Role**: Read-only access with download permissions only
- **Project-Wise Data Segregation**: All features (files, jobs, analytics) filtered by selected project
- **RBAC-Controlled Features**:
  - File Management: Upload, delete, trigger processing (admin only)
  - Job Processing: Trigger manual jobs, re-run completed jobs (admin only)
  - Data Access: Download ADLS keys, Thick Client access, data portal access
- **Enhanced Navigation**: Project selection dropdowns with role visualization
- Limited to project-level operations with strict permission enforcement

### Pipeline Management System

The pipeline management system consists of components for both root and organization administrators:

**Root Administrator Components**:
1. **OrganizationsManagement.tsx**: CRUD operations for organizations with admin user assignment
2. **JobLibrariesManagement.tsx**: Function library management with code upload, ARM templates, and pre-built options
3. **LibraryAssignments.tsx**: Assignment system linking job libraries to organizations
4. **JobDefinitionsManagement.tsx**: Workflow orchestration defining function chains with triggers and error handling

**Organization Administrator Components**:
1. **OrgJobDefinitions.tsx**: Organization-scoped job definition management for satellite image processing
2. **ProjectJobAssignments.tsx**: Assign job definitions to projects with execution triggers and priority management

### Component Architecture Patterns

- **Composite Management Components**: Large management interfaces (e.g., `RootAdminManagement`) act as routers to specific feature components
- **Feature-Specific Components**: Each major feature has its own component with complete CRUD operations
- **State Management**: Local component state for UI, React Query for server state simulation
- **Modal-Driven Interfaces**: Complex forms use shadcn/ui Dialog components for create/edit operations
- **Table-Based Data Display**: Consistent use of shadcn/ui Table components for data listing

### Navigation System

Navigation is handled through a hierarchical sidebar system in `SideNavigation.tsx` for all user roles:
- Role-based menu items with expandable sections
- State-driven section changes rather than URL routing
- Section state managed in `pages/Index.tsx` with switch-based component rendering

**All user roles** now use the same sidebar navigation pattern for consistency across the application.

**Organization Admin Navigation Structure**:
- **Tenant Administration**: Tenants, Projects, Users
- **Pipeline Management**: Job Definitions
- **Project Management**: User Assignments, Tenant Assignments, Job Assignments
- **Notifications**: Notification Channels

**Root Admin Navigation Structure**:
- **Root Administration**: Organizations, Global Users, System Settings
- **Pipeline Management**: Job Libraries, Library Assignments, Job Definitions

**Project User Navigation Structure** (Contributors and Readers):
- **Dashboard**: Same ProjectDashboard component as admin users (unified experience)
- **Tools**: API token management and client downloads

**Note**: Contributors and Readers no longer have separate "Projects" navigation tab - they access projects through the unified Dashboard interface. All user roles now use the same ProjectDashboard component for consistency.

### Tenant Management for Satellite Image Storage

Tenants represent storage segregation mechanisms for satellite image data using Azure Data Lake Storage v2 (ADLSv2):

**Tenant Configuration**:
- **Azure Account Integration**: Subscription ID, Tenant ID, Resource Group specification
- **Storage Account Types**: New dedicated or existing shared ADLSv2 accounts
- **Azure Regions**: East US 2, West Europe, Southeast Asia, Australia East, Central India
- **Replication Types**: LRS, ZRS, GRS, RA-GRS for data redundancy
- **Access Tiers**: Hot, Cool, Archive based on data access patterns
- **Environment Classification**: Production, Staging, Development

**Example Tenants**:
- **Agricultural Data Hub**: Dedicated storage for crop monitoring imagery
- **Emergency Response Storage**: Shared storage for disaster response and urban planning data

### Project Management for Satellite Image Processing

Projects represent satellite image processing workflows with specific configurations:

**Project Types**:
- **Agricultural Monitoring**: Multispectral imagery for crop health assessment
- **Disaster Response**: High-resolution optical imagery for emergency response
- **Urban Planning**: Synthetic Aperture Radar for development pattern analysis

**Project Configuration**:
- **Image Types**: Multispectral, High-Resolution Optical, Synthetic Aperture Radar
- **Geographic Regions**: Midwest USA, Global, European Cities, etc.
- **Tenant Assignment**: Projects are assigned to tenants for data segregation
- **Resource Tracking**: User count, job count, and processing metrics

### Data Flow

The application simulates backend operations with local state:
- Mock data arrays in components simulate database entities
- CRUD operations update local state arrays  
- Form submissions create new entities with generated IDs
- Delete operations filter entities from arrays
- Tenant-project relationships maintain data segregation patterns
- All data is ephemeral and resets on page refresh (no persistence layer)

### Key Component Relationships

```
App.tsx (auth wrapper)
├── Login.tsx (unauthenticated)
└── Index.tsx (authenticated)
    ├── TopNavBar.tsx (user info + logout)
    ├── SideNavigation.tsx (role-based menu)
    └── Content Components:
        ├── Dashboard.tsx (role-specific dashboards)
        ├── Org Admin Components:
        │   ├── TenantsManagement.tsx (ADLSv2 tenant management)
        │   ├── ProjectsManagement.tsx (satellite image processing projects)
        │   ├── UsersManagement.tsx (organization user management)
        │   ├── OrgJobDefinitions.tsx (job definitions for image processing)
        │   ├── ProjectUserAssignments.tsx (assign users to projects)
        │   ├── ProjectTenantAssignments.tsx (assign tenants to projects)
        │   ├── ProjectJobAssignments.tsx (assign jobs to projects)
        │   └── NotificationChannels.tsx (notification management system)
        ├── UserProjectPortal.tsx (refactored modular project user interface)
        │   ├── user-portal/FilesTab.tsx (file management with thick client integration)
        │   ├── user-portal/AnalyticsTab.tsx (enterprise analytics dashboard)
        │   ├── user-portal/ToolsTab.tsx (API tokens and client downloads)
        │   ├── user-portal/types.ts (centralized type definitions)
        │   └── user-portal/data.ts (mock data and utilities)
        └── RootAdminManagement.tsx (root admin router)
            ├── OrganizationsManagement.tsx
            ├── GlobalUsersManagement.tsx
            ├── JobLibrariesManagement.tsx
            ├── LibraryAssignments.tsx
            └── JobDefinitionsManagement.tsx
```

## Development Notes

- This is a Lovable.dev project with automatic deployment
- Uses modern React patterns (hooks, functional components)
- Tailwind utility-first styling approach
- TypeScript strict mode enabled
- ESLint configured for code quality
- No backend dependencies - fully client-side with mock data
- Component isolation allows for independent development of features
- Satellite image processing context with ADLSv2 storage integration patterns
- Mock data reflects realistic satellite imagery workflows and Azure storage configurations

## Key Development Patterns

- **Component Structure**: Each major feature has its own management component with complete CRUD operations
- **Form Patterns**: Use React Hook Form with Zod validation, consistent error handling
- **Modal Usage**: Complex forms use shadcn/ui Dialog components for create/edit operations
- **Table Display**: Consistent use of shadcn/ui Table components with actions column
- **State Management**: Local useState for UI state, no global state management beyond authentication
- **Navigation**: Section-based navigation controlled by state in `pages/Index.tsx`, not URL routing

## Critical Type Consistency Issues

**IMPORTANT**: There is a critical inconsistency in user role type definitions that must be maintained:

- **App.tsx**: Uses outdated role types `'root' | 'org' | 'user'`
- **All other components**: Use correct role types `'global_admin' | 'org_admin' | 'project_admin' | 'contributor' | 'reader'`

**When modifying user roles, always use the five-role system**: `'global_admin' | 'org_admin' | 'project_admin' | 'contributor' | 'reader'`

**Never use custom roles** in mock data (e.g., "Data Analyst", "GIS Analyst") - only use the system-defined roles listed above.

## Code Quality Standards

- **ESLint Configuration**: Uses TypeScript ESLint with React hooks and refresh plugins
- **Unused Variables**: `@typescript-eslint/no-unused-vars` is disabled in eslint.config.js
- **Role Type Safety**: Always use proper TypeScript interfaces for user roles
- **Mock Data Consistency**: All mock data should reflect realistic satellite imagery processing workflows

### User Project Portal Features

The `UserProjectPortal.tsx` component provides project users with comprehensive access to their satellite image processing workflows through a sidebar navigation interface (consistent with admin users):

#### **Navigation Structure** (Section-Based with Sidebar):
1. **Dashboard**: Analytics with thick client status and project performance metrics
2. **Projects**: Project overview with role-based access and job triggering
3. **Files**: Full-page file browser with cloud-drive interface
4. **File Processing**: Upload/processing status tracking with user filtering
5. **Job History**: Comprehensive job execution tracking and status monitoring (replaced Scheduled Jobs)
6. **Tools**: Client downloads, API token management, and external integrations

#### **Files Section - Enhanced File Management**:
- **Full-Page Browser**: Maximized height and width for better file navigation
- **Cloud-Drive Interface**: Tree-view file browser with folder navigation (input/, output/, archive/)
- **Eye Icon Details**: Clean interface with file details shown in modal dialogs
- **Real-Time Status Tracking**: Upload progress, processing status, and file-to-job associations
- **Auto-Trigger Integration**: Files with lightning bolt icons show auto-processing enabled
- **Processing Pipeline Visualization**: Upload → Auto-trigger → Processing → Output generation
- **Search Integration**: Real-time filtering of files and folders by name
- **Metadata Display**: File size, upload date, processing date, job associations
- **Output Linking**: Processed results automatically linked to source files

#### **File Processing Section - Status Tracking with User Accountability**:
- **Status Overview**: 5-column dashboard showing file counts by status (uploading, uploaded, processing, processed, failed)
- **User Filtering**: Track and filter files by the user who uploaded them
- **User Display**: Shows user name and email in processing table for accountability
- **Multi-Dimensional Filtering**: Search + status filter + user filter
- **Manual Processing**: Trigger processing for uploaded files without auto-trigger
- **Progress Monitoring**: Real-time progress bars for active uploads/processing

#### **Thick Client Integration**:
- **Heartbeat Monitoring**: Real-time connection status (Connected/Idle/Disconnected)
- **Session Management**: Multiple client sessions with platform/version tracking
- **Upload Progress**: Live progress bars for batch uploads from thick client
- **API Token System**: Granular permission management (upload, download, job_trigger, status_read, file_delete)
- **Authentication Flow**: Secure token-based authentication for client access
- **Multi-Platform Support**: Windows, macOS, Linux client downloads

#### **Enhanced Analytics Dashboard**:
- **Admin-Style 4-Column Grid**: Images Processed, Job Success Rate, Data Uploaded, Processing Time
- **Project Performance Matrix**: Success rates with color-coded progress bars per project
- **Thick Client Status Section**: Real-time connection monitoring, session details, upload tracking
- **Recent Activity Feed**: Real-time event tracking (uploads, job starts/completions, client connections)
- **Storage Usage Analytics**: Tenant-to-project data consumption with progress visualization
- **Processing Statistics**: Manual vs automated job breakdown with efficiency metrics
- **Trend Indicators**: Month-over-month improvements and performance metrics
- **Proper Layout Alignment**: Fixed card alignment issues with sequential stacking

#### **Advanced Job Management**:
- **Auto-Trigger System**: File upload events automatically trigger processing jobs
- **Event-Based Processing**: Threshold alerts, change detection, and custom triggers
- **Job-to-File Association**: Complete traceability from input files to output results
- **Status Synchronization**: Real-time updates between file uploads and job processing
- **Execution Tracking**: Detailed job history with duration, images processed, and output size

#### **Component Architecture (Refactored)**:
The User Portal has been refactored into modular components for better maintainability:

**File Structure**:
```
src/components/user-portal/
├── types.ts (centralized interfaces with project roles)
├── data.ts (project-wise mock data and RBAC utilities)
├── ProjectsManagement.tsx (project access with role badges)
├── FilesManagement.tsx (project-wise file browser with RBAC)
├── FileProcessingManagement.tsx (project-filtered processing with RBAC)
├── JobHistoryManagement.tsx (project-filtered job history with RBAC)
├── AnalyticsManagement.tsx (dashboard with thick client status)
├── ToolsManagement.tsx (API tokens and client tools)
├── FilesTab.tsx (file tree component with role permissions)
├── AnalyticsTab.tsx (analytics dashboard component)
├── ToolsTab.tsx (tools component)
├── FileDetailsDialog.tsx (file details modal)
└── UserProjectPortal.tsx (main portal with RBAC routing)
```

**Benefits**:
- **Consistent UI Patterns**: All sections follow admin interface patterns
- **Section-Based Navigation**: Uses same sidebar system as admin roles
- **Modular Architecture**: Each section is independently maintainable  
- **Enhanced File Management**: Full-page browser with eye icon details
- **User Accountability**: File processing page shows user who uploaded each file
- **Real-Time Monitoring**: Dashboard includes thick client status tracking
- **Project-Wise Data Segregation**: Complete data isolation between projects
- **Granular RBAC**: Role-based permissions at both user and project levels
- **Multi-Level Filtering**: Project, status, user, and search-based filtering
- **Permission Visualization**: Role badges and conditional UI elements

### Project-Wise RBAC Implementation

The application implements comprehensive Role-Based Access Control (RBAC) with project-wise data segregation:

#### **User Role Hierarchy**:
1. **Root Administrator**: System-wide access, all permissions
2. **Organization Administrator**: Organization-scoped access, can manage projects and users
3. **Project User**: Project-specific access with granular role permissions:
   - **Admin Role**: Full project control (upload, delete, trigger jobs, manage files)
   - **Reader Role**: Read-only access (view, download, analytics access only)

#### **Project-Wise Data Segregation**:
- **File System**: `getMockFileSystemByProject(projectId)` provides project-specific file trees
- **Job Executions**: `getJobExecutionsByProject(projectId)` filters jobs by project
- **Analytics Data**: Calculated per project based on filtered job executions
- **Navigation State**: Project selection persists across all user portal sections

#### **RBAC Implementation Details**:

**File Management RBAC**:
- `canUpload`: Only admin role users can upload files
- `canDelete`: Only admin role users can delete files  
- `canTriggerJobs`: Only admin role users can trigger processing jobs
- `canCreateFolder`: Only admin role users can create new folders

**Job Processing RBAC**:
- `canTriggerJobs`: Admin role + project admin permission required
- `canExportHistory`: Admin users only
- `canRefreshStatus`: All users (read operation)
- Job visibility filtered by user's accessible projects

**UI Permission Enforcement**:
- Conditional rendering of action buttons based on permissions
- Role badges displayed throughout interface
- Project selection dropdowns show user's role per project
- Disabled states for unauthorized actions

#### **Data Access Patterns**:
```typescript
// Project-wise file access
const projectFiles = getMockFileSystemByProject(selectedProjectId);

// Role-based permission check
const canPerformAction = userRole === 'admin' && currentProject?.role === 'admin';

// RBAC-filtered job data
const accessibleJobs = jobs.filter(job => 
  userProjects.some(p => p.id === job.projectId)
);
```

#### **Security Features**:
- **Data Isolation**: Complete separation of project data
- **Role Validation**: Multiple permission checks for sensitive operations
- **UI Security**: No unauthorized actions exposed in interface
- **Audit Trail**: User accountability in file processing logs
- **Session Context**: Role and project information maintained throughout session

## Important Context

- **Domain Focus**: Satellite image processing and analysis workflows
- **Storage Architecture**: Azure Data Lake Storage v2 (ADLSv2) for image data segregation
- **Tenant Purpose**: Storage segregation for different types of satellite imagery projects
- **Project Types**: Agricultural monitoring, disaster response, urban planning with specific image types
- **Data Flow**: Projects → Tenants → ADLSv2 Storage Accounts → Satellite Image Data
- **Client Tool**: "Thick Client" for local data processing and upload capabilities
- **Multi-Account Support**: Tenants can span different Azure subscriptions and resource groups

### File Management & Processing Pipeline

The application now includes a comprehensive file management system integrated with satellite image processing workflows:

#### **File Processing Pipeline**:
```
Thick Client Upload → File Browser (Files Tab) → Auto-Trigger → Job Processing → Output Generation → Results Linking
```

#### **File System Structure**:
- **input/**: Raw satellite imagery uploaded via Thick Client
- **output/**: Processed results (NDVI analysis, classification maps, etc.)
- **archive/**: Historical data and legacy processing results

#### **Auto-Trigger Integration**:
- Files marked with auto-trigger automatically initiate processing jobs upon upload completion
- Real-time status synchronization between file uploads and job execution
- Complete traceability from input files to generated outputs
- Event-based processing for file uploads, threshold alerts, and change detection

#### **Thick Client Integration**:
- **Authentication**: Token-based API access with granular permissions
- **Session Management**: Real-time heartbeat monitoring and connection status
- **Upload Coordination**: Batch upload capabilities with progress tracking
- **Multi-Platform Support**: Windows, macOS, Linux desktop applications
- **Offline Processing**: Local preprocessing before cloud upload

#### **Data Consistency**:
- All mock data maintains realistic satellite imagery processing context
- File-to-job associations reflect actual processing workflows
- Analytics calculations derived from real job execution data
- Storage metrics aligned with Azure ADLSv2 usage patterns

### Notification Management System

The `NotificationChannels.tsx` component provides organization administrators with comprehensive notification management capabilities for satellite image processing workflows:

**Core Features**:
- **Multi-Channel Support**: Email, Slack, Microsoft Teams, SMS, and Webhooks
- **Event-Based Rules**: Configure notifications for job events, data uploads, threshold alerts, and system events
- **Project-Scoped Notifications**: Target specific satellite image processing projects
- **Activity Logging**: Track notification delivery success rates and history

**Supported Notification Channels**:

1. **Email Notifications**:
   - SMTP configuration (server, port, credentials)
   - SSL/TLS encryption support
   - OAuth token support (Gmail, Outlook)
   - Multiple recipient support

2. **Slack Integration**:
   - Channel targeting (#satellite-processing, #alerts)
   - Webhook URL configuration
   - Bot token authentication (xoxb-)
   - App token support (xapp-)
   - Signing secret for webhook verification

3. **Microsoft Teams**:
   - Teams webhook URL configuration
   - App ID and password for bot framework integration
   - Connector card format support

4. **SMS Notifications**:
   - Multi-provider support (Twilio, AWS SNS, Nexmo/Vonage)
   - Provider-specific authentication:
     - **Twilio**: Account SID, Auth Token, Service SID
     - **AWS SNS**: Access Key, Secret Key, Region
   - Phone number management

5. **Webhook Integration**:
   - Flexible authentication options:
     - Bearer Token authentication
     - API Key with custom header names
     - Basic authentication (username/password)
     - Custom headers support
   - Webhook signature verification with signing keys
   - Configurable authentication types

**Event Types Supported**:
- **Job Events**: Job completed, failed, started
- **Data Events**: Satellite imagery uploaded, processing thresholds exceeded
- **System Events**: System alerts, storage limits, user activity
- **Custom Events**: Configurable for specific processing workflows

**Notification Rules**:
- **Event-Based Triggers**: Configure which events trigger notifications
- **Project Filtering**: Scope notifications to specific satellite image projects
- **Severity Levels**: Critical, High, Medium, Low priority classification
- **Conditional Logic**: Advanced filtering based on job types and conditions
- **Template Management**: Customizable notification message templates

**Security Features**:
- **Credential Protection**: All sensitive fields (tokens, passwords, keys) use password input types
- **Masked Display**: API keys and tokens displayed with bullet points in the UI
- **Provider Isolation**: Separate credential storage for different SMS/webhook providers
- **Role-Based Access**: Only organization administrators can manage notification channels

**Integration Context**:
- **Satellite Processing Focus**: Events and templates designed for image processing workflows
- **Project Alignment**: Notification targeting aligned with agricultural monitoring, disaster response, and urban planning projects
- **Azure Integration**: Compatible with ADLSv2 storage events and processing pipeline notifications
- **Multi-Tenant Support**: Notifications can be scoped to specific tenants and projects