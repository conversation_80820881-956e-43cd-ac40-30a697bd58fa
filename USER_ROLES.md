# User Roles Documentation

This document outlines all user roles implemented in the Versa Portal multi-tenant satellite image processing system.

## Role Hierarchy Overview

The system implements a **multi-scope role assignment architecture** where users can have different roles at different organizational levels. Users are created without roles and then assigned specific permissions within various scopes.

## System-Level Roles

### Global Administrator
- **Scope**: System-wide access
- **Permissions**: 
  - Create and manage organizations
  - Manage global users across all organizations
  - System settings and configuration
  - View all billing and usage data
  - Access to all pipeline management features
- **Key Capabilities**:
  - Organization CRUD operations
  - Global user management
  - System-wide reporting and analytics
  - Root-level pipeline libraries and job definitions

## Organization-Level Roles

### Organization Administrator
- **Scope**: Single organization
- **Permissions**:
  - Create and manage projects within organization
  - Manage organization users
  - Create and assign tenants
  - View organization billing
  - Manage notification channels
- **Key Capabilities**:
  - Tenant management (Azure ADLSv2 storage)
  - Project creation and oversight
  - Organization user role assignments
  - Job definitions for satellite image processing

### Project Administrator
- **Scope**: Specific projects within organization
- **Permissions**:
  - Create projects within their scope
  - Create dedicated tenants for projects
  - Manage project team members
  - Assign project-level roles (admin, contributor, reader)
- **Key Capabilities**:
  - Project-scoped tenant creation
  - User assignment with granular permissions
  - Project infrastructure management

## Project-Level Roles

### Contributor
- **Scope**: Specific projects
- **Permissions**:
  - Upload satellite imagery
  - Trigger processing jobs
  - View processing results
  - Access project analytics
- **Key Capabilities**:
  - File upload and management
  - Manual job execution
  - Data analysis and visualization
  - Thick client integration for batch uploads

### Reader
- **Scope**: Specific projects  
- **Permissions**:
  - View satellite imagery
  - View processing results
  - Download processed data
  - Access read-only analytics
- **Key Capabilities**:
  - Browse file system (read-only)
  - Download ADLS keys for data access
  - View job execution history
  - Analytics dashboard access

## Role Assignment Architecture

### Multi-Scope Assignment
- **System Roles**: Assigned at global level (Global Administrator)
- **Organization Roles**: Assigned within specific organizations
- **Project Roles**: Assigned within specific projects
- **Cross-Scope**: Same user can have different roles across multiple scopes

### Examples of Multi-Scope Users
```
User: John Smith
├── Organization "Acme Corp" → Organization Administrator
├── Project "Agricultural Monitoring" → Project Administrator  
└── Project "Disaster Response" → Contributor

User: Sarah Davis  
├── Organization "TechCorp" → (No organization role)
├── Project "Crop Analysis" → Contributor
└── Project "Urban Planning" → Reader
```

## Permission Matrix

| Feature | Global Admin | Org Admin | Project Admin | Contributor | Reader |
|---------|-------------|-----------|---------------|-------------|--------|
| Create Organizations | ✅ | ❌ | ❌ | ❌ | ❌ |
| Create Projects | ✅ | ✅ | ✅ | ❌ | ❌ |
| Manage Users | ✅ | ✅ (org scope) | ✅ (project scope) | ❌ | ❌ |
| Create Tenants | ✅ | ✅ | ✅ | ❌ | ❌ |
| Upload Images | ✅ | ✅ | ✅ | ✅ | ❌ |
| Trigger Jobs | ✅ | ✅ | ✅ | ✅ | ❌ |
| View Results | ✅ | ✅ | ✅ | ✅ | ✅ |
| Download Data | ✅ | ✅ | ✅ | ✅ | ✅ |
| System Settings | ✅ | ❌ | ❌ | ❌ | ❌ |
| Billing Access | ✅ | ✅ (org only) | ❌ | ❌ | ❌ |

## Role Assignment Process

### User Creation
1. Users are created with **name and email only**
2. No roles assigned during creation
3. Informational message guides administrators to use role assignment

### Role Assignment Methods
1. **System-Level**: Global administrators assign system roles
2. **Organization-Level**: Organization administrators assign organization roles  
3. **Project-Level**: Organization and project administrators assign project roles

### Role Management UI
- **Shield Icon**: Indicates role assignment capability
- **Multi-Scope Badges**: Display current roles across different scopes
- **Granular Permissions**: Show specific capabilities per role

## Azure Integration Context

### Tenant Mapping
- **Global Admin**: Access to all tenants across organizations
- **Org Admin**: Access to organization's tenants only
- **Project Admin**: Can create dedicated tenants for projects
- **Contributor/Reader**: Access through assigned project tenants

### Pipeline Management Roles
- **Global Admin**: System-wide job libraries and definitions
- **Org Admin**: Organization-scoped job definitions
- **Project Admin**: Project-specific job assignments
- **Contributor**: Execute assigned jobs
- **Reader**: View job results only

## Security Considerations

### Principle of Least Privilege
- Users receive minimum required permissions
- Scope-based access prevents cross-organization data exposure
- Project-level isolation for satellite imagery data

### Data Segregation
- **Tenant Level**: Azure ADLSv2 storage isolation
- **Project Level**: File system and processing isolation  
- **Role Level**: Feature and data access restrictions

## Technical Implementation

### Interface Structure
```typescript
// Global User Interface
interface GlobalUser {
  id: string;
  name: string;
  email: string;
  lastLogin?: string;
  createdAt: string;
  lastUpdated: string;
  systemRoles: {
    role: 'global_admin';
    permissions: string[];
  }[];
  organizationRoles: {
    organizationId: string;
    role: 'org_admin' | 'project_admin' | 'contributor' | 'reader';
    permissions: string[];
  }[];
}

// Organization User Interface
interface OrgUser {
  id: string;
  name: string;
  email: string;
  createdAt: string;
  lastLogin?: string;
  projectCount: number;
  organizationRoles: {
    organizationId: string;
    role: 'org_admin' | 'project_admin' | 'contributor' | 'reader';
    permissions: string[];
  }[];
  projectRoles: {
    projectId: string;
    role: 'project_admin' | 'contributor' | 'reader';
    permissions: string[];
  }[];
}
```

### Role Assignment Flow
1. **User Creation**: Basic user record with empty role arrays
2. **Role Assignment**: Administrators use Shield button interfaces
3. **Permission Validation**: System checks scope-appropriate permissions
4. **UI Updates**: Multi-scope badges reflect current assignments

## Future Enhancements

### Planned Features
- **Role Templates**: Pre-defined role combinations for common scenarios
- **Temporary Roles**: Time-limited access assignments
- **Audit Logging**: Track all role assignment changes
- **Custom Permissions**: Fine-grained permission customization
- **Role Inheritance**: Automatic role propagation in organizational hierarchies

### Integration Opportunities
- **Azure Active Directory**: SSO and role synchronization
- **RBAC Policies**: Azure resource-level access control
- **API Authentication**: Token-based role validation
- **Monitoring Integration**: Role-based access logging and alerting

This role system ensures secure, scalable management of satellite image processing workflows while maintaining clear organizational boundaries and appropriate access controls.