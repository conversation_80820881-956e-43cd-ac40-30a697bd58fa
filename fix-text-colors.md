# Text Color Fix Guide for Dark Theme

## Quick Reference for Replacing Hardcoded Colors

Use these semantic color tokens to ensure proper visibility in both light and dark themes:

### Primary Text
- `text-gray-900` → `text-foreground`
- `text-gray-800` → `text-foreground`  
- `text-gray-700` → `text-foreground`

### Secondary/Muted Text  
- `text-gray-600` → `text-muted-foreground`
- `text-gray-500` → `text-muted-foreground`
- `text-gray-400` → `text-muted-foreground`

### Interactive/Brand Colors
- `text-blue-600` → `text-primary`
- `text-blue-700` → `text-primary`

### Status Colors
- `text-green-600` → `text-success`
- `text-red-600` → `text-destructive`
- `text-yellow-600` → `text-warning`
- `text-amber-600` → `text-warning`

## Quick Fix Commands

For any remaining components with text visibility issues, use these find/replace patterns:

```bash
# In VS Code or your editor, use these find/replace operations:
text-gray-600 → text-muted-foreground
text-gray-500 → text-muted-foreground  
text-gray-900 → text-foreground
text-blue-600 → text-primary
text-green-600 → text-success
text-red-600 → text-destructive
```

## Components Fixed So Far
✅ TenantsManagement.tsx  
✅ TenantFormFields.tsx
✅ UserProjectPortal.tsx
✅ Dashboard.tsx
✅ SideNavigation.tsx
✅ TopNavBar.tsx
✅ Login.tsx

## Still Need Attention
🔧 Other management components (ProjectsManagement, UsersManagement, etc.)
🔧 Dialog and form components
🔧 Table components
🔧 Badge and status components

## Dark Theme Improvements Made
- Enhanced contrast ratios
- Better muted text visibility  
- Improved sidebar colors
- Fixed status badge foregrounds
- Added proper semantic color tokens