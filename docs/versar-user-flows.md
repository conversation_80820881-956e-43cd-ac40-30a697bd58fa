# User Workflows

## Root Administrator Flow: Organization Onboarding

### Login and Dashboard
The root admin logs in and sees the "System Dashboard" with overview of all organizations and system metrics. The sidebar shows sections for Dashboard, Root Administration, Pipeline Management, and Billing & Revenue.

### Creating a New Organization
1. **Navigate to Root Administration** → Expand section → Click "Organizations"
2. **Organization Setup**:
   - Organization name: "AgriVision Corp"
   - Primary admin user: <PERSON> (<EMAIL>)
   - Contact information and billing details
   - Initial resource quotas

3. **Azure Account Assignment**:
   - Navigate to "Azure Accounts" in Root Administration
   - Assign organization to existing Azure subscription
   - Set billing integration and cost alerts

4. **Global User Creation**:
   - Navigate to "Global Users" in Root Administration
   - Create user account for <PERSON>
   - Assign "Organization Administrator" role
   - Send welcome email with login credentials

The organization is now active and appears in the Organizations list with badge count updated.

---

## Organization Admin Flow: Setting Up Projects

### Login and Dashboard
Tom logs in and sees the "Organization Dashboard" with overview of tenants, projects, and users. The sidebar shows sections for Dashboard, Tenant Administration, Pipeline Management, Project Management, Billing & Usage, and Notifications.

### Creating a Tenant
1. **Navigate to Tenant Administration** → Click "Tenants"
2. **Tenant Configuration**:
   - Tenant name: "Iowa Agricultural Data"
   - Description: "Storage for crop monitoring and agricultural satellite imagery"
   - Storage type: Shared (cost-effective)
   - Azure region: Central US
   - Initial quota: 100GB

The tenant appears in the Tenants list and the badge count updates to show the new tenant.

### Setting Up a Project
1. **Navigate to Tenant Administration** → Click "Projects"
2. **Project Configuration**:
   - Project name: "Spring Corn Monitoring - Central Iowa"
   - Project type: Agricultural Monitoring
   - Geographic region: Midwest USA
   - Image type: Multispectral
   - Processing schedule: Weekly during growing season

### Adding Team Members
1. **Navigate to Tenant Administration** → Click "Users"
2. **Add Users**:
   - Lisa Chen (<EMAIL>) - Member role
   - Mike Johnson (<EMAIL>) - Member role
   - Sarah Davis (<EMAIL>) - Member role

### Assigning Users to Projects
1. **Navigate to Project Management** → Click "User Assignments"
2. **Assignment Configuration**:
   - Select "Spring Corn Monitoring" project
   - Assign Lisa Chen as Admin access
   - Assign Mike and Sarah as Reader access
   - Set notification preferences

### Setting Up Job Definitions
1. **Navigate to Pipeline Management** → Click "Job Definitions"
2. **Create Processing Workflow**:
   - Job name: "Weekly Vegetation Analysis"
   - Processing type: Multispectral analysis
   - Trigger: Scheduled (weekly)
   - Output format: NDVI maps and health reports

---

## Project User Flow: Daily Operations

### Login and Project Portal
Lisa logs in and goes directly to the Project Portal (no sidebar navigation). She sees tabs for Projects, Files, Job History, Scheduled Jobs, Analytics, and Tools.

### Viewing Assigned Projects
1. **Projects Tab** (default view):
   - See "Spring Corn Monitoring - Central Iowa" with Admin access
   - View project status, recent activity, and processing summary
   - Access project-specific controls and settings

### Managing Satellite Imagery Files
1. **Files Tab** (primary workflow):
   - **Tree Navigation**: Browse files in hierarchical structure (input/, output/, archive/)
   - **Upload Monitoring**: View real-time progress of thick client uploads with progress bars
   - **Auto-Trigger Status**: See lightning bolt indicators for files that automatically trigger processing
   - **File Status Tracking**: Monitor uploaded → processing → processed status changes
   - **Job Association**: Click files to see associated job executions and output results
   - **Thick Client Sessions**: Monitor active desktop client connections and heartbeat status

### Setting Up Thick Client Integration
1. **Tools Tab** → **API Token Management**:
   - Click "Create New Token" for thick client authentication
   - Configure permissions: upload, download, job_trigger, status_read
   - Set expiration period (6 months or 1 year)
   - Copy generated token for thick client configuration

2. **Thick Client Installation**:
   - Download client for Windows/macOS/Linux from Tools tab
   - Install and configure with API token
   - Set project endpoint and storage credentials
   - Begin batch upload of satellite imagery

### Checking Recent Job Results
1. **Job History Tab**:
   - View completed, running, and failed processing jobs
   - See "Weekly Vegetation Analysis" completed on May 15, 2024
   - Review job details, processing time, and output files
   - Download results: vegetation maps, health reports, raw data
   - **Auto-Triggered Jobs**: Identify jobs triggered by file uploads with lightning bolt icons

### Triggering Manual Processing
1. **Projects Tab** → Select project → "Trigger Job"
2. **Job Configuration**:
   - Select "High-Resolution Analysis" 
   - Define processing area (draw on map)
   - Choose priority: Standard (estimated 15 min, $12)
   - Submit job and monitor in Job History

### Setting Up Automated Jobs
1. **Scheduled Jobs Tab**:
   - View existing weekly vegetation analysis schedule
   - Create new scheduled job for specific field monitoring
   - Set trigger conditions (new imagery arrival, threshold alerts)
   - Configure notification preferences

### Downloading Tools and Data
1. **Tools Tab**:
   - Click "Download Thick Client" for batch file operations
   - Access "Data Portal" link for web-based data exploration
   - Download ADLS access keys for direct storage access
   - View API documentation for programmatic access

### Monitoring Project Analytics
1. **Analytics Tab** (Enterprise Dashboard):
   - **4-Column Metrics Grid**: Job success rates, total processing time, images processed, data generated
   - **Project Performance Tracking**: Individual project analytics with comparative insights
   - **Real-Time Activity Feed**: Live updates of system activities, job completions, and processing status
   - **Resource Utilization**: Storage usage, processing capacity, and performance metrics
   - **Processing Trends**: Historical data analysis with trend visualization

---

## Organization Admin: Notification Setup

### Configuring Notification Channels
1. **Navigate to Notifications** → Click "Notification Channels"
2. **Email Setup**:
   - Configure SMTP settings for organization email
   - Set recipient lists for different alert types
   - Test email delivery

3. **Slack Integration**:
   - Enter Slack webhook URL for #satellite-processing channel
   - Configure bot token for interactive notifications
   - Set up event filtering (job completion, failures, alerts)

4. **Teams Integration**:
   - Configure Teams webhook for project notifications
   - Set up bot framework integration
   - Define message templates

### Setting Up Alert Rules
1. **Create Notification Rules**:
   - Event type: Job completion, failure, data upload
   - Target projects: Select specific projects or all
   - Notification channels: Email + Slack for critical alerts
   - Severity levels: Critical, High, Medium, Low

---

## Common Use Cases

### Emergency Response Scenario
**Hurricane Damage Assessment**
1. Org admin creates new project in "Projects" section
2. Sets up high-priority job definition in "Pipeline Management"
3. Assigns emergency response team in "User Assignments"
4. Configures immediate notifications in "Notification Channels"
5. Project users monitor progress in "Job History" tab

### Agricultural Research Scenario
**Multi-State Drought Monitoring**
1. Create tenant for research data storage
2. Set up multiple projects for different states
3. Configure automated weekly processing jobs
4. Set up data sharing through user assignments
5. Monitor long-term trends in Analytics tab

### Urban Planning Scenario
**City Development Analysis**
1. Create dedicated tenant for city planning data
2. Set up annual processing schedule for development patterns
3. Configure automated reports and notifications
4. Share results with planning committee through user assignments

---

## Key Navigation Principles

**Role-Based Interface**: Each user type sees only relevant sections and capabilities.

**Badge System**: Numeric badges show counts of items, "NEW" badges highlight new features.

**Progressive Disclosure**: Expandable sections keep interface clean while providing access to detailed functions.

**Search Navigation**: Search box helps find specific features quickly in the sidebar.

**Tabbed Experience**: Project users get focused, task-oriented interface without administrative clutter.