<svg viewBox="0 0 1200 620" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Professional color scheme -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#475569;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#020617;stop-opacity:1" />
    </linearGradient>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b"/>
    </marker>
    <!-- Icons -->
    <g id="userIcon">
      <circle cx="6" cy="4" r="3" fill="none" stroke="currentColor" stroke-width="1.5"/>
      <path d="M2 20v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2" fill="none" stroke="currentColor" stroke-width="1.5"/>
    </g>
    <g id="serverIcon">
      <rect x="2" y="3" width="20" height="14" rx="2" ry="2" fill="none" stroke="currentColor" stroke-width="1.5"/>
      <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="1.5"/>
      <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="1.5"/>
    </g>
  </defs>

  <rect width="1200" height="620" fill="#ffffff"/>
  
  <text x="600" y="35" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="28" font-weight="600" fill="#0f172a">
    Versar GDS Admin Portal
  </text>
  <text x="600" y="55" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="400" fill="#64748b">
    Strategic Architecture Overview
  </text>

  <!-- Users -->
  <g id="users">
    <rect x="50" y="80" width="1100" height="60" fill="url(#primaryGradient)" rx="8"/>
    <text x="600" y="105" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="20" font-weight="600" fill="white">
      Users &amp; Access Control
    </text>
    <text x="600" y="125" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)">
      Multi-Tenant Role-Based Access • Enterprise Authentication
    </text>
  </g>

  <!-- Application Layer -->
  <g id="application">
    <rect x="50" y="160" width="1100" height="60" fill="#f8fafc" stroke="#e2e8f0" stroke-width="1" rx="8"/>
    <text x="600" y="185" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="20" font-weight="600" fill="#1e293b">
      Satellite Image Processing Platform
    </text>
    <text x="600" y="205" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" fill="#64748b">
      Enterprise Web Portal • Multi-Tenant Administration • Project Management
    </text>
  </g>


  <!-- Business Services -->
  <g id="business-services">
    <rect x="50" y="240" width="1100" height="80" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1" rx="8"/>
    <text x="600" y="270" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="20" font-weight="600" fill="#1e293b">
      Cloud Business Services
    </text>
    <text x="600" y="290" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" fill="#64748b">
      Serverless Microservices • Auto-Scaling • Event-Driven Processing
    </text>
    <text x="600" y="308" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" fill="#6b7280">
      Organization Management • Project Administration • Analytics • Notifications
    </text>
  </g>


  <!-- Data Platform -->
  <g id="data-platform">
    <rect x="50" y="340" width="1100" height="80" fill="#f8fafc" stroke="#e2e8f0" stroke-width="1" rx="8"/>
    <text x="600" y="370" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="20" font-weight="600" fill="#1e293b">
      Azure Data Platform
    </text>
    <text x="600" y="390" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" fill="#64748b">
      Enterprise Data Lake • SQL Database • Caching • Security • Multi-Region
    </text>
    <text x="600" y="408" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" fill="#6b7280">
      Satellite Image Storage • Metadata Management • High-Performance Access
    </text>
  </g>

  <!-- Enterprise Cloud Infrastructure -->
  <g id="cloud-infrastructure">
    <rect x="50" y="440" width="1100" height="80" fill="url(#accentGradient)" rx="8"/>
    <text x="600" y="470" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="20" font-weight="600" fill="white">
      Microsoft Azure Cloud Infrastructure
    </text>
    <text x="600" y="490" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)">
      Global Regions • Enterprise Security • Compliance • 99.9% SLA
    </text>
    <text x="600" y="508" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" fill="rgba(255,255,255,0.7)">
      Commercial Cloud • Government Cloud • Multi-Region Deployment
    </text>
  </g>
  
  <!-- Business Outcomes -->
  <g id="business-outcomes">
    <rect x="50" y="540" width="1100" height="60" fill="#fafafa" stroke="#e5e7eb" stroke-width="1" rx="8"/>
    <text x="600" y="565" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="18" font-weight="600" fill="#374151">
      Business Outcomes
    </text>
    <text x="600" y="585" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" fill="#6b7280">
      Reduced Operational Costs • Accelerated Time-to-Market • Enhanced Security • Global Scalability
    </text>
  </g>

  <!-- Flow Arrows -->
  <line x1="600" y1="140" x2="600" y2="155" stroke="#94a3b8" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="600" y1="220" x2="600" y2="235" stroke="#94a3b8" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="600" y1="320" x2="600" y2="335" stroke="#94a3b8" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="600" y1="420" x2="600" y2="435" stroke="#94a3b8" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="600" y1="520" x2="600" y2="535" stroke="#94a3b8" stroke-width="3" marker-end="url(#arrowhead)"/>
</svg>