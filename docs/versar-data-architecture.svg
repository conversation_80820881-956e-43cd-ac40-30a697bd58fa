<svg viewBox="0 0 1400 1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Professional color scheme -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#475569;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#020617;stop-opacity:1" />
    </linearGradient>
    <marker id="dataArrow" markerWidth="10" markerHeight="8" refX="9" refY="4" orient="auto">
      <path d="M0,0 L0,8 L10,4 z" fill="#94a3b8"/>
    </marker>
  </defs>

  <rect width="1400" height="1000" fill="#ffffff"/>
  
  <text x="700" y="35" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="28" font-weight="600" fill="#0f172a">
    Versar GDS Admin Portal
  </text>
  <text x="700" y="55" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="400" fill="#64748b">
    Enterprise Data Architecture
  </text>

  <g id="application-layer">
    <rect x="50" y="80" width="1300" height="60" fill="url(#primaryGradient)" rx="8"/>
    <text x="700" y="105" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="18" font-weight="600" fill="white">
      Enterprise Application Layer
    </text>
    <text x="700" y="125" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)">
      FastAPI Microservices • Domain-Driven Architecture
    </text>
  </g>

  <g id="data-access-layer">
    <rect x="50" y="160" width="1300" height="50" fill="url(#secondaryGradient)" rx="8"/>
    <text x="700" y="180" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="600" fill="white">
      Data Access Layer
    </text>
    <text x="700" y="198" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)">
      SQLAlchemy ORM • Repository Pattern • Async Operations
    </text>
  </g>

  <g id="primary-data-stores">
    <rect x="50" y="230" width="1300" height="200" fill="#f8fafc" stroke="#e2e8f0" stroke-width="1" rx="8"/>
    <text x="700" y="255" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="18" font-weight="600" fill="#1e293b">
      Data Storage Layer
    </text>
    
    <rect x="80" y="275" width="250" height="130" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="205" y="300" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="600" fill="#1e293b">
      Azure SQL Database
    </text>
    <text x="205" y="318" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#64748b">
      ACID Compliance + JSON Flexibility
    </text>
    <text x="95" y="340" font-family="Inter, system-ui, sans-serif" font-size="10" font-weight="600" fill="#475569">Core Tables:</text>
    <text x="95" y="355" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Organizations</text>
    <text x="95" y="368" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Tenants</text>
    <text x="95" y="381" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Users</text>
    <text x="95" y="394" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Projects</text>
    
    <text x="200" y="340" font-family="Inter, system-ui, sans-serif" font-size="10" font-weight="600" fill="#475569">Assignment Tables:</text>
    <text x="200" y="355" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Project-User Assignments</text>
    <text x="200" y="368" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Project-Tenant Assignments</text>
    <text x="200" y="381" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Project-Job Assignments</text>
    <text x="200" y="394" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Audit Logs</text>

    <rect x="350" y="275" width="250" height="130" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="475" y="300" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="600" fill="#1e293b">
      Redis Cache
    </text>
    <text x="475" y="318" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#64748b">
      Session + API Response Caching
    </text>
    <text x="365" y="340" font-family="Inter, system-ui, sans-serif" font-size="10" font-weight="600" fill="#475569">Cache Types:</text>
    <text x="365" y="355" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• User Sessions</text>
    <text x="365" y="368" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Permission Matrix</text>
    <text x="365" y="381" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• API Responses</text>
    <text x="365" y="394" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Configuration Data</text>

    <rect x="620" y="275" width="250" height="130" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="745" y="300" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="600" fill="#1e293b">
      ADLS Gen2
    </text>
    <text x="745" y="318" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#64748b">
      Satellite Imagery Storage
    </text>
    <text x="635" y="340" font-family="Inter, system-ui, sans-serif" font-size="10" font-weight="600" fill="#475569">Storage Models:</text>
    <text x="635" y="355" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Dedicated Tenants</text>
    <text x="635" y="368" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Shared Tenants</text>
    <text x="635" y="381" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Regional Deployment</text>
    <text x="635" y="394" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Hierarchical Namespace</text>

    <rect x="890" y="275" width="250" height="130" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="1015" y="300" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="600" fill="#1e293b">
      Azure Key Vault
    </text>
    <text x="1015" y="318" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#64748b">
      Enterprise Security
    </text>
    <text x="905" y="340" font-family="Inter, system-ui, sans-serif" font-size="10" font-weight="600" fill="#475569">Secrets:</text>
    <text x="905" y="355" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• API Keys</text>
    <text x="905" y="368" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Connection Strings</text>
    <text x="905" y="381" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• Certificates</text>
    <text x="905" y="394" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">• HSM Backing</text>
  </g>

  <g id="storage-models">
    <rect x="50" y="450" width="1300" height="120" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1" rx="8"/>
    <text x="700" y="475" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="600" fill="#1e293b">
      Storage Deployment Models
    </text>

    <rect x="150" y="500" width="300" height="50" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="300" y="520" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="13" font-weight="600" fill="#1e293b">
      Dedicated Storage Model
    </text>
    <text x="300" y="535" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      Isolated ADLS per tenant
    </text>
    <text x="300" y="548" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      Production workloads
    </text>

    <rect x="500" y="500" width="300" height="50" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="650" y="520" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="13" font-weight="600" fill="#1e293b">
      Shared Storage Model
    </text>
    <text x="650" y="535" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      Cost-effective shared ADLS
    </text>
    <text x="650" y="548" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      Development/testing
    </text>

    <rect x="850" y="500" width="300" height="50" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="1000" y="520" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="13" font-weight="600" fill="#1e293b">
      Azure Integration
    </text>
    <text x="1000" y="535" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      Cross-subscription support
    </text>
    <text x="1000" y="548" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      Regional deployment
    </text>
  </g>

  <g id="enterprise-features">
    <rect x="50" y="590" width="1300" height="80" fill="#fafafa" stroke="#e5e7eb" stroke-width="1" rx="8"/>
    <text x="700" y="615" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="600" fill="#1e293b">
      Enterprise Data Architecture Benefits
    </text>
    
    <rect x="150" y="635" width="200" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
    <text x="250" y="650" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
      Multi-Tenant Isolation
    </text>
    
    <rect x="370" y="635" width="200" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
    <text x="470" y="650" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
      ACID Compliance
    </text>
    
    <rect x="590" y="635" width="200" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
    <text x="690" y="650" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
      High Performance Caching
    </text>
    
    <rect x="810" y="635" width="200" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
    <text x="910" y="650" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
      Hierarchical Storage
    </text>
    
    <rect x="1030" y="635" width="200" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
    <text x="1130" y="650" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
      Enterprise Security
    </text>
  </g>

  <g id="connections">
    <line x1="700" y1="140" x2="700" y2="155" stroke="#94a3b8" stroke-width="2" marker-end="url(#dataArrow)"/>
    <line x1="700" y1="210" x2="700" y2="225" stroke="#94a3b8" stroke-width="2" marker-end="url(#dataArrow)"/>
    <line x1="700" y1="430" x2="700" y2="445" stroke="#94a3b8" stroke-width="2" marker-end="url(#dataArrow)"/>
    <line x1="700" y1="570" x2="700" y2="585" stroke="#94a3b8" stroke-width="2" marker-end="url(#dataArrow)"/>
  </g>

</svg>