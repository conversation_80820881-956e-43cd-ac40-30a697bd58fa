<svg viewBox="0 0 1400 1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Professional color scheme -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#475569;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#020617;stop-opacity:1" />
    </linearGradient>
    
    <!-- Regional gradients -->
    <linearGradient id="sharedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#475569;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="regionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="govCloudGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#d97706;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#92400e;stop-opacity:1" />
    </linearGradient>
    
    <!-- Patterns -->
    <pattern id="dedicatedPattern" patternUnits="userSpaceOnUse" width="4" height="4">
      <rect width="4" height="4" fill="#dbeafe"/>
      <path d="M0,4l4,-4M-1,1l2,-2M3,5l2,-2" stroke="#3b82f6" stroke-width="0.5"/>
    </pattern>
    <pattern id="sharedPattern" patternUnits="userSpaceOnUse" width="4" height="4">
      <rect width="4" height="4" fill="#dcfce7"/>
      <circle cx="2" cy="2" r="0.5" fill="#10b981"/>
    </pattern>
    
    <marker id="regionArrow" markerWidth="8" markerHeight="8" refX="7" refY="2" orient="auto">
      <path d="M0,0 L0,4 L8,2 z" fill="#94a3b8"/>
    </marker>
  </defs>

  <rect width="1400" height="1000" fill="#ffffff"/>
  
  <text x="700" y="35" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="28" font-weight="600" fill="#0f172a">
    Versar GDS Admin Portal
  </text>
  <text x="700" y="55" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="400" fill="#64748b">
    Regional Deployment Architecture
  </text>

  <g id="global-management">
    <rect x="50" y="60" width="1300" height="100" fill="url(#sharedGradient)" rx="10"/>
    <text x="700" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      Global Management Layer - Multi-Region Orchestration
    </text>
    
    <rect x="200" y="100" width="150" height="50" fill="white" opacity="0.9" rx="5"/>
    <text x="275" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#059669">API Gateway</text>
    <text x="275" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#059669">Regional Load Balancing</text>
    
    <rect x="380" y="100" width="150" height="50" fill="white" opacity="0.9" rx="5"/>
    <text x="455" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#059669">Data Replication</text>
    <text x="455" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#059669">Cross-Region Sync</text>
    
    <rect x="560" y="100" width="150" height="50" fill="white" opacity="0.9" rx="5"/>
    <text x="635" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#059669">Azure AD B2C</text>
    <text x="635" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#059669">Global Identity</text>
    
    <rect x="740" y="100" width="150" height="50" fill="white" opacity="0.9" rx="5"/>
    <text x="815" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#059669">Service Mesh</text>
    <text x="815" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#059669">Regional Services</text>
    
    <rect x="920" y="100" width="150" height="50" fill="white" opacity="0.9" rx="5"/>
    <text x="995" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#059669">Monitoring</text>
    <text x="995" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#059669">Azure Monitor</text>
  </g>

  <g id="commercial-regions">
    <rect x="80" y="200" width="320" height="280" fill="url(#regionGradient)" rx="10"/>
    <text x="240" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      US East Region
    </text>
    
    <rect x="100" y="240" width="130" height="60" fill="white" opacity="0.9" rx="5"/>
    <text x="165" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1e40af">Microservices</text>
    <text x="165" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#1e40af">Tenant Service</text>
    <text x="165" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#1e40af">Project Service</text>
    
    <rect x="250" y="240" width="130" height="60" fill="white" opacity="0.9" rx="5"/>
    <text x="315" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1e40af">Azure Functions</text>
    <text x="315" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#1e40af">Tenant Provisioning</text>
    <text x="315" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#1e40af">ADLS Management</text>
    
    <rect x="100" y="320" width="280" height="80" fill="url(#dedicatedPattern)" stroke="#3b82f6" stroke-width="2" rx="5"/>
    <text x="240" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1e40af">
      Enterprise: &quot;Dedicated Storage&quot;
    </text>
    <text x="120" y="360" font-family="Arial, sans-serif" font-size="9" fill="#1e40af">Form shows &quot;Dedicated Storage&quot; option</text>
    <text x="120" y="375" font-family="Arial, sans-serif" font-size="9" fill="#1e40af">Azure account dropdown selection</text>
    <text x="120" y="390" font-family="Arial, sans-serif" font-size="9" fill="#1e40af">Backend handles ADLS creation</text>
    
    <rect x="100" y="410" width="280" height="50" fill="url(#sharedPattern)" stroke="#10b981" stroke-width="2" rx="5"/>
    <text x="240" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#059669">
      Enterprise: &quot;Shared Storage&quot;
    </text>
    <text x="240" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#059669">
      Cost-effective option for development/testing
    </text>

    <rect x="420" y="200" width="320" height="280" fill="url(#regionGradient)" rx="10"/>
    <text x="580" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      Asia Pacific Region
    </text>
    
    <rect x="440" y="240" width="130" height="60" fill="white" opacity="0.9" rx="5"/>
    <text x="505" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1e40af">Assignment Service</text>
    <text x="505" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#1e40af">ProjectUserAssignments</text>
    <text x="505" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#1e40af">ProjectTenantAssignments</text>
    
    <rect x="590" y="240" width="130" height="60" fill="white" opacity="0.9" rx="5"/>
    <text x="655" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1e40af">Project Portal</text>
    <text x="655" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#1e40af">UserProjectPortal</text>
    <text x="655" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#1e40af">Project User Service</text>
    
    <rect x="440" y="320" width="280" height="80" fill="url(#dedicatedPattern)" stroke="#3b82f6" stroke-width="2" rx="5"/>
    <text x="580" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1e40af">
      Assignment Workflows
    </text>
    <text x="460" y="360" font-family="Arial, sans-serif" font-size="9" fill="#1e40af">Many-to-many relationship management</text>
    <text x="460" y="375" font-family="Arial, sans-serif" font-size="9" fill="#1e40af">Access level controls (admin/reader)</text>
    <text x="460" y="390" font-family="Arial, sans-serif" font-size="9" fill="#1e40af">Project assignment tracking</text>
    
    <rect x="440" y="410" width="280" height="50" fill="url(#sharedPattern)" stroke="#10b981" stroke-width="2" rx="5"/>
    <text x="580" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#059669">
      Shared Tenant Model
    </text>
    <text x="580" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#059669">
      Shared ADLS with Dedicated Functions
    </text>

    <rect x="760" y="200" width="320" height="280" fill="url(#regionGradient)" rx="10"/>
    <text x="920" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      Europe West Region
    </text>
    
    <rect x="780" y="240" width="130" height="60" fill="white" opacity="0.9" rx="5"/>
    <text x="845" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1e40af">GDPR Functions</text>
    <text x="845" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#1e40af">Compliance Processing</text>
    <text x="845" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#1e40af">Data Residency</text>
    
    <rect x="930" y="240" width="130" height="60" fill="white" opacity="0.9" rx="5"/>
    <text x="995" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1e40af">Service Bus EU</text>
    <text x="995" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#1e40af">Regional Topics</text>
    <text x="995" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#1e40af">GDPR Compliance</text>
    
    <rect x="780" y="320" width="280" height="80" fill="url(#dedicatedPattern)" stroke="#3b82f6" stroke-width="2" rx="5"/>
    <text x="920" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1e40af">
      EU Dedicated Tenants
    </text>
    <text x="800" y="360" font-family="Arial, sans-serif" font-size="9" fill="#1e40af">Tenant E: GDPR Compliant</text>
    <text x="800" y="375" font-family="Arial, sans-serif" font-size="9" fill="#1e40af">Tenant F: Data Sovereignty</text>
    <text x="800" y="390" font-family="Arial, sans-serif" font-size="9" fill="#1e40af">No Cross border Transfer</text>
    
    <rect x="780" y="410" width="280" height="50" fill="url(#sharedPattern)" stroke="#10b981" stroke-width="2" rx="5"/>
    <text x="920" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#059669">
      EU Shared Storage
    </text>
    <text x="920" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#059669">
      GDPR compliant shared resources
    </text>
  </g>

  <g id="government-regions">
    <rect x="80" y="520" width="320" height="280" fill="url(#govCloudGradient)" rx="10"/>
    <text x="240" y="545" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      US Gov Virginia
    </text>
    
    <rect x="100" y="560" width="130" height="60" fill="white" opacity="0.9" rx="5"/>
    <text x="165" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#d97706">Gov Functions</text>
    <text x="165" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#d97706">FedRAMP Functions</text>
    <text x="165" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#d97706">Secure Processing</text>
    
    <rect x="250" y="560" width="130" height="60" fill="white" opacity="0.9" rx="5"/>
    <text x="315" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#d97706">Gov Service Bus</text>
    <text x="315" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#d97706">Classified Topics</text>
    <text x="315" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#d97706">Air Gap Isolation</text>
    
    <rect x="100" y="640" width="280" height="80" fill="url(#dedicatedPattern)" stroke="#f59e0b" stroke-width="2" rx="5"/>
    <text x="240" y="660" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d97706">
      Government Dedicated Tenants
    </text>
    <text x="120" y="680" font-family="Arial, sans-serif" font-size="9" fill="#d97706">DOD Tenant: Secret Clearance</text>
    <text x="120" y="695" font-family="Arial, sans-serif" font-size="9" fill="#d97706">Agency A: Confidential Data</text>
    <text x="120" y="710" font-family="Arial, sans-serif" font-size="9" fill="#d97706">Complete Air gap Isolation</text>
    
    <rect x="100" y="730" width="280" height="50" fill="#fed7aa" stroke="#f59e0b" stroke-width="2" rx="5"/>
    <text x="240" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d97706">
      Gov Cloud Shared Resources
    </text>
    <text x="240" y="765" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#d97706">
      Classified shared storage
    </text>

    <rect x="420" y="520" width="320" height="280" fill="url(#govCloudGradient)" rx="10"/>
    <text x="580" y="545" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      US Gov Texas
    </text>
    
    <rect x="440" y="560" width="130" height="60" fill="white" opacity="0.9" rx="5"/>
    <text x="505" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#d97706">Gov DR Functions</text>
    <text x="505" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#d97706">Secure Failover</text>
    <text x="505" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#d97706">Encrypted Sync</text>
    
    <rect x="590" y="560" width="130" height="60" fill="white" opacity="0.9" rx="5"/>
    <text x="655" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#d97706">Compliance Bus</text>
    <text x="655" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#d97706">ITAR Topics</text>
    <text x="655" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#d97706">Security Logs</text>
    
    <rect x="440" y="640" width="280" height="80" fill="url(#dedicatedPattern)" stroke="#f59e0b" stroke-width="2" rx="5"/>
    <text x="580" y="660" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d97706">
      Regional Gov Tenants
    </text>
    <text x="460" y="680" font-family="Arial, sans-serif" font-size="9" fill="#d97706">Defense Contractor B</text>
    <text x="460" y="695" font-family="Arial, sans-serif" font-size="9" fill="#d97706">Regional Agency C</text>
    <text x="460" y="710" font-family="Arial, sans-serif" font-size="9" fill="#d97706">Geographic Distribution</text>
    
    <rect x="440" y="730" width="280" height="50" fill="#fed7aa" stroke="#f59e0b" stroke-width="2" rx="5"/>
    <text x="580" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d97706">
      Gov DR Storage
    </text>
    <text x="580" y="765" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#d97706">
      Disaster recovery with compliance
    </text>
  </g>

  <g id="regional-management">
    <rect x="760" y="520" width="320" height="280" fill="#e5e7eb" stroke="#6b7280" stroke-width="2" rx="10"/>
    <text x="920" y="545" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#374151">
      Regional Management
    </text>
    
    <rect x="780" y="560" width="280" height="60" fill="white" stroke="#6b7280" stroke-width="1" rx="5"/>
    <text x="920" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#374151">
      Cross Region Orchestration
    </text>
    <text x="800" y="600" font-family="Arial, sans-serif" font-size="9" fill="#374151">Azure Functions Orchestration</text>
    <text x="800" y="610" font-family="Arial, sans-serif" font-size="9" fill="#374151">Service Bus Topic Management</text>
    
    <rect x="780" y="630" width="280" height="60" fill="white" stroke="#6b7280" stroke-width="1" rx="5"/>
    <text x="920" y="650" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#374151">
      Regional Cost Management
    </text>
    <text x="800" y="670" font-family="Arial, sans-serif" font-size="9" fill="#374151">Serverless Cost Optimization</text>
    <text x="800" y="680" font-family="Arial, sans-serif" font-size="9" fill="#374151">Auto Scaling Functions</text>
    
    <rect x="780" y="700" width="280" height="60" fill="white" stroke="#6b7280" stroke-width="1" rx="5"/>
    <text x="920" y="720" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#374151">
      Compliance Dashboard
    </text>
    <text x="800" y="740" font-family="Arial, sans-serif" font-size="9" fill="#374151">Function App Monitoring</text>
    <text x="800" y="750" font-family="Arial, sans-serif" font-size="9" fill="#374151">Service Bus Analytics</text>
    
    <rect x="780" y="770" width="280" height="20" fill="#dbeafe" stroke="#3b82f6" stroke-width="1" rx="5"/>
    <text x="920" y="782" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e40af">
      Serverless: 15 Regions, 50 Functions, Pay per Execution
    </text>
  </g>

  <g id="network-connections">
    <line x1="700" y1="160" x2="240" y2="190" stroke="#059669" stroke-width="3" marker-end="url(#regionArrow)"/>
    <line x1="700" y1="160" x2="580" y2="190" stroke="#059669" stroke-width="3" marker-end="url(#regionArrow)"/>
    <line x1="700" y1="160" x2="920" y2="190" stroke="#059669" stroke-width="3" marker-end="url(#regionArrow)"/>
  </g>

  <g id="legend">
    <rect x="1100" y="200" width="280" height="280" fill="white" stroke="#9ca3af" stroke-width="1" rx="5"/>
    <text x="1240" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#374151">
      Legend
    </text>
    
    <rect x="1120" y="235" width="20" height="15" fill="url(#dedicatedPattern)" stroke="#3b82f6" stroke-width="1"/>
    <text x="1150" y="247" font-family="Arial, sans-serif" font-size="10" fill="#374151">Dedicated Tenants</text>
    
    <rect x="1120" y="255" width="20" height="15" fill="url(#sharedPattern)" stroke="#10b981" stroke-width="1"/>
    <text x="1150" y="267" font-family="Arial, sans-serif" font-size="10" fill="#374151">Shared Resources</text>
    
    <rect x="1120" y="275" width="20" height="15" fill="url(#regionGradient)"/>
    <text x="1150" y="287" font-family="Arial, sans-serif" font-size="10" fill="#374151">Commercial Cloud</text>
    
    <rect x="1120" y="295" width="20" height="15" fill="url(#govCloudGradient)"/>
    <text x="1150" y="307" font-family="Arial, sans-serif" font-size="10" fill="#374151">Government Cloud</text>
    
    <text x="1120" y="330" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#374151">Regional Coverage:</text>
    <text x="1120" y="345" font-family="Arial, sans-serif" font-size="9" fill="#374151">Commercial: 12 regions</text>
    <text x="1120" y="360" font-family="Arial, sans-serif" font-size="9" fill="#374151">Government: 5 regions</text>
    <text x="1120" y="375" font-family="Arial, sans-serif" font-size="9" fill="#374151">GDPR Compliant: 4 regions</text>
    
    <text x="1120" y="400" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#374151">Serverless Benefits:</text>
    <text x="1120" y="415" font-family="Arial, sans-serif" font-size="9" fill="#374151">Pay per execution model</text>
    <text x="1120" y="430" font-family="Arial, sans-serif" font-size="9" fill="#374151">Auto scaling to zero</text>
    <text x="1120" y="445" font-family="Arial, sans-serif" font-size="9" fill="#374151">Zero infrastructure management</text>
  </g>

  <text x="700" y="950" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#6b7280">
    Serverless regional deployment with Azure Functions and Service Bus topics for auto-scaling processing
  </text>
  <text x="700" y="970" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#6b7280">
    Pay per execution model with regional compliance support and zero infrastructure management
  </text>
</svg>