<svg viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Professional color scheme -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#475569;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#020617;stop-opacity:1" />
    </linearGradient>
    <marker id="regionArrow" markerWidth="8" markerHeight="8" refX="7" refY="2" orient="auto">
      <path d="M0,0 L0,4 L8,2 z" fill="#94a3b8"/>
    </marker>
  </defs>

  <rect width="1200" height="800" fill="#ffffff"/>
  
  <text x="600" y="35" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="28" font-weight="600" fill="#0f172a">
    Versar GDS Admin Portal
  </text>
  <text x="600" y="55" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="400" fill="#64748b">
    Regional Deployment Architecture
  </text>

  <!-- Global Management Layer -->
  <g id="global-management">
    <rect x="50" y="80" width="1100" height="60" fill="url(#primaryGradient)" rx="8"/>
    <text x="600" y="105" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="18" font-weight="600" fill="white">
      Global Management Layer
    </text>
    <text x="600" y="125" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)">
      Multi-Region Orchestration • Cross-Subscription Support • Global Identity
    </text>
  </g>

  <!-- Regional Deployments -->
  <g id="regions">
    <rect x="50" y="160" width="1100" height="300" fill="#f8fafc" stroke="#e2e8f0" stroke-width="1" rx="8"/>
    <text x="600" y="185" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="600" fill="#1e293b">
      Regional Deployments
    </text>

    <!-- Commercial Regions -->
    <rect x="100" y="210" width="300" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="250" y="235" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="600" fill="#1e293b">
      Commercial Cloud
    </text>
    <text x="250" y="255" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#64748b">
      Multi-Region Deployment
    </text>
    <text x="120" y="275" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • East US, West US 2, Central US
    </text>
    <text x="120" y="290" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • West Europe, North Europe
    </text>
    <text x="120" y="305" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • Southeast Asia, Australia East
    </text>
    <text x="120" y="320" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • Auto-scaling microservices
    </text>

    <!-- Government Cloud -->
    <rect x="450" y="210" width="300" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="600" y="235" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="600" fill="#1e293b">
      Government Cloud
    </text>
    <text x="600" y="255" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#64748b">
      Compliance &amp; Security
    </text>
    <text x="470" y="275" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • US Gov Virginia, US Gov Texas
    </text>
    <text x="470" y="290" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • US DoD East, US DoD Central
    </text>
    <text x="470" y="305" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • FedRAMP compliance
    </text>
    <text x="470" y="320" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • Air-gapped isolation
    </text>

    <!-- Data Residency -->
    <rect x="800" y="210" width="300" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="950" y="235" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="600" fill="#1e293b">
      Data Residency
    </text>
    <text x="950" y="255" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#64748b">
      Regional Compliance
    </text>
    <text x="820" y="275" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • GDPR compliance (EU regions)
    </text>
    <text x="820" y="290" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • Data sovereignty requirements
    </text>
    <text x="820" y="305" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • Cross-border data protection
    </text>
    <text x="820" y="320" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • Regional backup policies
    </text>

    <!-- Storage Models -->
    <rect x="100" y="350" width="480" height="80" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="340" y="375" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="13" font-weight="600" fill="#1e293b">
      Storage Deployment Models
    </text>
    <text x="120" y="395" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • Dedicated ADLS per tenant (production isolation)
    </text>
    <text x="120" y="408" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • Shared ADLS across tenants (cost optimization)
    </text>
    <text x="120" y="421" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • Regional placement based on data residency requirements
    </text>

    <!-- Cross-Region Services -->
    <rect x="620" y="350" width="480" height="80" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="860" y="375" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="13" font-weight="600" fill="#1e293b">
      Cross-Region Services
    </text>
    <text x="640" y="395" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • Azure Service Bus topic replication
    </text>
    <text x="640" y="408" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • Database geo-replication with conflict resolution
    </text>
    <text x="640" y="421" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#6b7280">
      • Global identity and authentication coordination
    </text>
  </g>

  <!-- Serverless Processing -->
  <g id="serverless-layer">
    <rect x="50" y="480" width="1100" height="60" fill="url(#accentGradient)" rx="8"/>
    <text x="600" y="505" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="600" fill="white">
      Serverless Processing Layer
    </text>
    <text x="600" y="525" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)">
      Azure Functions • Auto-Scaling • Pay-per-Execution • Regional Orchestration
    </text>
  </g>

  <!-- Data Layer -->
  <g id="data-layer">
    <rect x="50" y="560" width="1100" height="80" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1" rx="8"/>
    <text x="600" y="585" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="600" fill="#1e293b">
      Global Data Layer
    </text>
    
    <rect x="150" y="605" width="160" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
    <text x="230" y="620" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
      Azure SQL (Global)
    </text>
    
    <rect x="330" y="605" width="160" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
    <text x="410" y="620" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
      Redis (Regional)
    </text>
    
    <rect x="510" y="605" width="160" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
    <text x="590" y="620" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
      ADLS (Regional)
    </text>
    
    <rect x="690" y="605" width="160" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
    <text x="770" y="620" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
      Key Vault (Regional)
    </text>
    
    <rect x="870" y="605" width="160" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
    <text x="950" y="620" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
      Monitor (Global)
    </text>
  </g>

  <!-- Enterprise Benefits -->
  <g id="benefits">
    <rect x="50" y="660" width="1100" height="80" fill="#fafafa" stroke="#e5e7eb" stroke-width="1" rx="8"/>
    <text x="600" y="685" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="600" fill="#1e293b">
      Regional Architecture Benefits
    </text>
    
    <text x="200" y="705" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#6b7280">
      Low Latency
    </text>
    <text x="350" y="705" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#6b7280">
      Data Sovereignty
    </text>
    <text x="500" y="705" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#6b7280">
      Disaster Recovery
    </text>
    <text x="650" y="705" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#6b7280">
      Compliance
    </text>
    <text x="800" y="705" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#6b7280">
      Cost Optimization
    </text>
    <text x="950" y="705" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#6b7280">
      Auto-Scaling
    </text>
    
    <text x="200" y="720" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#6b7280">
      Performance
    </text>
    <text x="350" y="720" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#6b7280">
      Regional Laws
    </text>
    <text x="500" y="720" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#6b7280">
      High Availability
    </text>
    <text x="650" y="720" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#6b7280">
      FedRAMP/GDPR
    </text>
    <text x="800" y="720" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#6b7280">
      Resource Efficiency
    </text>
    <text x="950" y="720" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#6b7280">
      Serverless
    </text>
  </g>

  <!-- Flow Arrows -->
  <line x1="600" y1="140" x2="600" y2="155" stroke="#94a3b8" stroke-width="2" marker-end="url(#regionArrow)"/>
  <line x1="600" y1="460" x2="600" y2="475" stroke="#94a3b8" stroke-width="2" marker-end="url(#regionArrow)"/>
  <line x1="600" y1="540" x2="600" y2="555" stroke="#94a3b8" stroke-width="2" marker-end="url(#regionArrow)"/>
  <line x1="600" y1="640" x2="600" y2="655" stroke="#94a3b8" stroke-width="2" marker-end="url(#regionArrow)"/>
</svg>