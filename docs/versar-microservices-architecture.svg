<svg viewBox="0 0 1400 900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Professional color scheme -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#475569;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#020617;stop-opacity:1" />
    </linearGradient>
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
      <path d="M0,0 L0,6 L9,3 z" fill="#94a3b8"/>
    </marker>
  </defs>

  <rect width="1400" height="900" fill="#ffffff"/>
  
  <text x="700" y="35" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="28" font-weight="600" fill="#0f172a">
    Versar GDS Admin Portal
  </text>
  <text x="700" y="55" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="400" fill="#64748b">
    Microservices Architecture
  </text>

  <!-- API Gateway -->
  <g id="api-gateway">
    <rect x="450" y="80" width="500" height="50" fill="url(#primaryGradient)" rx="8"/>
    <text x="700" y="102" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="600" fill="white">
      Enterprise API Gateway
    </text>
    <text x="700" y="118" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)">
      Load Balancer • Rate Limiting • Circuit Breaker • Request Routing • Health Checks
    </text>
  </g>

  <!-- Microservices -->
  <g id="microservices">
    <rect x="50" y="150" width="1300" height="180" fill="#f8fafc" stroke="#e2e8f0" stroke-width="1" rx="8"/>
    <text x="700" y="175" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="18" font-weight="600" fill="#1e293b">
      Microservices Implementation Layer - Technical Architecture
    </text>
    
    <!-- Admin Service -->
    <rect x="80" y="190" width="180" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="170" y="210" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="13" font-weight="600" fill="#1e293b">
      Admin Service
    </text>
    <text x="170" y="225" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      FastAPI + Python 3.11
    </text>
    <text x="90" y="245" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Organization Management
    </text>
    <text x="90" y="258" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Global User Administration
    </text>
    <text x="90" y="271" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • System Configuration
    </text>
    <text x="90" y="284" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Azure Account Integration
    </text>
    <text x="90" y="297" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Cross-Org Analytics
    </text>

    <!-- Tenant Service -->
    <rect x="280" y="190" width="180" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="370" y="210" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="13" font-weight="600" fill="#1e293b">
      Tenant Service
    </text>
    <text x="370" y="225" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      FastAPI + SQLAlchemy
    </text>
    <text x="290" y="245" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • ADLS Provisioning
    </text>
    <text x="290" y="258" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Storage Management
    </text>
    <text x="290" y="271" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Regional Deployment
    </text>
    <text x="290" y="284" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Lifecycle Management
    </text>
    <text x="290" y="297" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Capacity Planning
    </text>

    <!-- User Service -->
    <rect x="480" y="190" width="180" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="570" y="210" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="13" font-weight="600" fill="#1e293b">
      User Service
    </text>
    <text x="570" y="225" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      FastAPI + Azure AD B2C
    </text>
    <text x="490" y="245" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Role Management
    </text>
    <text x="490" y="258" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Access Control
    </text>
    <text x="490" y="271" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • User Provisioning
    </text>
    <text x="490" y="284" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Activity Monitoring
    </text>
    <text x="490" y="297" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Permission Matrix
    </text>

    <!-- Project Service -->
    <rect x="680" y="190" width="180" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="770" y="210" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="13" font-weight="600" fill="#1e293b">
      Project Service
    </text>
    <text x="770" y="225" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      FastAPI + Assignment Logic
    </text>
    <text x="690" y="245" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Satellite Projects
    </text>
    <text x="690" y="258" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Resource Assignment
    </text>
    <text x="690" y="271" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Workflow Orchestration
    </text>
    <text x="690" y="284" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Project Analytics
    </text>
    <text x="690" y="297" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Multi-Assignment Logic
    </text>

    <!-- Billing Service -->
    <rect x="880" y="190" width="180" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="970" y="210" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="13" font-weight="600" fill="#1e293b">
      Billing Service
    </text>
    <text x="970" y="225" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      FastAPI + Analytics
    </text>
    <text x="890" y="245" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Revenue Tracking
    </text>
    <text x="890" y="258" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Cost Analytics
    </text>
    <text x="890" y="271" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Usage Monitoring
    </text>
    <text x="890" y="284" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Invoice Generation
    </text>
    <text x="890" y="297" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Multi-Level Tracking
    </text>
    
    <!-- Notification Service -->
    <rect x="1080" y="190" width="180" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="1170" y="210" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="13" font-weight="600" fill="#1e293b">
      Notification Service
    </text>
    <text x="1170" y="225" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      FastAPI + Multi-Channel
    </text>
    <text x="1090" y="245" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Email • Slack • Teams
    </text>
    <text x="1090" y="258" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • SMS • Webhooks
    </text>
    <text x="1090" y="271" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Event-Based Rules
    </text>
    <text x="1090" y="284" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Project Scoping
    </text>
    <text x="1090" y="297" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      • Delivery Tracking
    </text>
  </g>

  <!-- Service Bus Topics -->
  <g id="service-bus">
    <rect x="50" y="350" width="1300" height="70" fill="url(#accentGradient)" rx="8"/>
    <text x="700" y="375" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="600" fill="white">
      Azure Service Bus - Enterprise Messaging
    </text>
    <text x="700" y="395" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)">
      Pub/Sub Topics • Message Ordering • Dead Letter Queues • Duplicate Detection • Session State
    </text>
  </g>

  <!-- Azure Functions -->
  <g id="azure-functions">
    <rect x="50" y="440" width="1300" height="50" fill="url(#secondaryGradient)" rx="8"/>
    <text x="700" y="460" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="600" fill="white">
      Azure Functions - Serverless Processing
    </text>
    <text x="700" y="478" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)">
      Consumption Plan • Auto Scaling • Regional Deployment
    </text>
  </g>

  <!-- Data Layer -->
  <g id="data-layer">
    <rect x="50" y="510" width="1300" height="120" fill="#f8fafc" stroke="#e2e8f0" stroke-width="1" rx="8"/>
    <text x="700" y="535" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="600" fill="#1e293b">
      Data Layer
    </text>
    
    <!-- SQL Database -->
    <rect x="100" y="550" width="220" height="60" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="210" y="570" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="600" fill="#1e293b">
      Azure SQL Database
    </text>
    <text x="210" y="585" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      ACID • JSON Columns
    </text>
    <text x="210" y="598" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      Multi-Tenant Schema
    </text>

    <!-- Redis Cache -->
    <rect x="340" y="550" width="180" height="60" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="430" y="570" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="600" fill="#1e293b">
      Redis Cache
    </text>
    <text x="430" y="585" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      Sessions • API Cache
    </text>
    <text x="430" y="598" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      High Performance
    </text>

    <!-- ADLS Gen2 -->
    <rect x="540" y="550" width="180" height="60" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="630" y="570" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="600" fill="#1e293b">
      ADLS Gen2
    </text>
    <text x="630" y="585" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      Satellite Imagery
    </text>
    <text x="630" y="598" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      Hierarchical Storage
    </text>

    <!-- Azure Key Vault -->
    <rect x="740" y="550" width="180" height="60" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="830" y="570" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="600" fill="#1e293b">
      Azure Key Vault
    </text>
    <text x="830" y="585" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      Secrets • Security
    </text>
    <text x="830" y="598" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      HSM Backed
    </text>
    
    <!-- Azure Monitor -->
    <rect x="940" y="550" width="180" height="60" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="1030" y="570" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="600" fill="#1e293b">
      Azure Monitor
    </text>
    <text x="1030" y="585" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      Telemetry • Logs
    </text>
    <text x="1030" y="598" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      Application Insights
    </text>
    
    <!-- Azure Storage -->
    <rect x="1140" y="550" width="180" height="60" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
    <text x="1230" y="570" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="600" fill="#1e293b">
      Blob Storage
    </text>
    <text x="1230" y="585" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">
      Artifacts • Backups
    </text>
    <text x="1230" y="598" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="9" fill="#6b7280">
      Archive Storage
    </text>
  </g>

  <!-- Connection Arrows -->
  <line x1="700" y1="130" x2="700" y2="145" stroke="#94a3b8" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="700" y1="330" x2="700" y2="345" stroke="#94a3b8" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="700" y1="420" x2="700" y2="435" stroke="#94a3b8" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="700" y1="490" x2="700" y2="505" stroke="#94a3b8" stroke-width="2" marker-end="url(#arrow)"/>

  <!-- Enterprise Benefits -->
  <rect x="50" y="650" width="1300" height="80" fill="#fafafa" stroke="#e5e7eb" stroke-width="1" rx="8"/>
  <text x="700" y="675" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="600" fill="#1e293b">
    Microservices Technical Benefits
  </text>
  
  <rect x="100" y="690" width="200" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
  <text x="200" y="705" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
    Domain-Driven Design
  </text>
  
  <rect x="320" y="690" width="200" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
  <text x="420" y="705" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
    Independent Scaling
  </text>
  
  <rect x="540" y="690" width="200" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
  <text x="640" y="705" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
    Fault Isolation
  </text>
  
  <rect x="760" y="690" width="200" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
  <text x="860" y="705" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
    Technology Flexibility
  </text>
  
  <rect x="980" y="690" width="200" height="25" fill="white" stroke="#e2e8f0" stroke-width="1" rx="4"/>
  <text x="1080" y="705" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="11" fill="#475569">
    Continuous Deployment
  </text>
</svg>