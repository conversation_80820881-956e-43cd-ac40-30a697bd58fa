{"permissions": {"allow": ["Bash(npm run lint)", "Bash(grep:*)", "Bash(rg:*)", "Bash(npm run build:*)", "Bash(find:*)", "WebFetch(domain:www.versar.com)", "WebFetch(domain:versar.com)", "Bash(xmllint:*)", "Bash(for:*)", "Bash(do echo \"Validating $file:\")", "<PERSON><PERSON>(echo:*)", "Bash(done)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(npm run dev:*)", "Bash(npm run lint:*)", "Bash(npx eslint:*)", "mcp__ide__getDiagnostics"], "deny": []}}