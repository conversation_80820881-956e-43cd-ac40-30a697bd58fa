@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Source+Sans+Pro:wght@300;400;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    /* Base colors - Versar clean white theme */
    --background: 255 255 255; /* Pure white like Versar's site */
    --foreground: 31 41 55; /* Gray 700 - Professional dark text */

    /* Card colors */
    --card: 255 255 255; /* Pure white */
    --card-foreground: 31 41 55; /* Gray 700 */

    /* Popover colors */
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;

    /* Primary brand colors - Dark Blue */
    --primary: 30 58 138; /* Blue 800 - Dark blue primary brand color */
    --primary-foreground: 255 255 255; /* Pure white */

    /* Secondary colors - Light gray backgrounds */
    --secondary: 248 250 252; /* Slate 50 - Very light background */
    --secondary-foreground: 71 85 105; /* Slate 600 - Professional text */

    /* Muted colors - Subtle text and backgrounds */
    --muted: 248 250 252; /* Slate 50 */
    --muted-foreground: 100 116 139; /* Slate 500 - Muted text */

    /* Accent colors - Dark blue accent */
    --accent: 239 246 255; /* Blue 50 - Light blue accent background */
    --accent-foreground: 30 58 138; /* Blue 800 - Professional accent text */

    /* Status colors */
    --success: 5 150 105; /* Emerald 600 */
    --success-foreground: 255 255 255;
    --warning: 217 119 6; /* Amber 600 */
    --warning-foreground: 255 255 255;
    --destructive: 220 38 38; /* Red 600 */
    --destructive-foreground: 255 255 255;
    --info: 37 99 235; /* Blue 600 */
    --info-foreground: 255 255 255;

    /* Border and input - Clean minimal borders */
    --border: 226 232 240; /* Slate 200 - Subtle borders */
    --input: 226 232 240; /* Slate 200 - Clean input borders */
    --ring: 30 58 138; /* Dark blue focus ring */

    --radius: 0.5rem;

    /* Enhanced sidebar - Clean professional */
    --sidebar-background: 248 250 252; /* Slate 50 - Clean light background */
    --sidebar-foreground: 71 85 105; /* Slate 600 - Professional text */
    --sidebar-primary: 30 58 138; /* Blue 800 - Dark blue */
    --sidebar-primary-foreground: 255 255 255; /* Pure white */
    --sidebar-accent: 239 246 255; /* Blue 50 - Subtle accent */
    --sidebar-accent-foreground: 30 58 138; /* Blue 800 */
    --sidebar-border: 226 232 240; /* Slate 200 - Clean borders */
    --sidebar-ring: 30 58 138; /* Dark blue */

    /* Status background colors */
    --status-success-bg: 240 253 244; /* Green 50 */
    --status-warning-bg: 255 251 235; /* Amber 50 */
    --status-error-bg: 254 242 242; /* Red 50 */
    --status-info-bg: 239 246 255; /* Blue 50 */
    --status-neutral-bg: 248 250 252; /* Slate 50 */

    /* Versar professional brand colors */
    --versar-teal: 30 58 138; /* Primary dark blue */
    --versar-teal-light: 59 130 246; /* Blue 500 - Lighter accent */
    --versar-navy: 15 23 42; /* Deep navy for contrasts */
    --versar-gray: 71 85 105; /* Professional slate 600 */
    --versar-light-teal: 147 197 253; /* Blue 300 - Light accent */
    --versar-gradient-start: 30 58 138; /* Primary dark blue */
    --versar-gradient-end: 59 130 246; /* Blue 500 */
  }

  .dark {
    /* Dark base colors - Enhanced contrast */
    --background: 3 7 18; /* Darker slate for better contrast */
    --foreground: 248 250 252; /* Slate 50 - Light text for dark backgrounds */

    /* Dark card colors */
    --card: 15 23 42; /* Slate 800 */
    --card-foreground: 241 245 249; /* Slate 100 - Lighter for better readability */

    /* Dark popover colors */
    --popover: 15 23 42; /* Slate 800 */
    --popover-foreground: 241 245 249; /* Slate 100 */

    /* Dark primary colors - Dark blue theme */
    --primary: 59 130 246; /* Blue 500 - Accessible in dark mode */
    --primary-foreground: 15 23 42; /* Slate 800 for contrast */

    /* Dark secondary colors */
    --secondary: 51 65 85; /* Slate 600 */
    --secondary-foreground: 226 232 240; /* Slate 200 - Much lighter for visibility */

    /* Dark muted colors - Critical fix for visibility */
    --muted: 30 41 59; /* Slate 700 */
    --muted-foreground: 203 213 225; /* Slate 300 - Much lighter for dark theme readability */

    /* Dark accent colors - Dark blue */
    --accent: 30 58 138; /* Blue 800 for dark mode */
    --accent-foreground: 147 197 253; /* Blue 300 - Accessible contrast */

    /* Dark status colors - Enhanced visibility */
    --success: 34 197 94; /* Green 500 */
    --success-foreground: 240 253 244; /* Green 50 for contrast */
    --warning: 245 158 11; /* Amber 500 */
    --warning-foreground: 255 251 235; /* Amber 50 for contrast */
    --destructive: 239 68 68; /* Red 500 */
    --destructive-foreground: 254 242 242; /* Red 50 for contrast */
    --info: 59 130 246; /* Blue 500 */
    --info-foreground: 239 246 255; /* Blue 50 for contrast */

    /* Dark borders and inputs */
    --border: 51 65 85; /* Slate 600 */
    --input: 51 65 85; /* Slate 600 */
    --ring: 59 130 246; /* Blue 500 - Accessible focus ring */

    /* Dark sidebar - Professional dark theme */
    --sidebar-background: 15 23 42; /* Slate 800 */
    --sidebar-foreground: 203 213 225; /* Slate 300 */
    --sidebar-primary: 59 130 246; /* Blue 500 - Accessible */
    --sidebar-primary-foreground: 15 23 42; /* Slate 800 */
    --sidebar-accent: 30 58 138; /* Blue 800 */
    --sidebar-accent-foreground: 147 197 253; /* Blue 300 */
    --sidebar-border: 51 65 85; /* Slate 600 */
    --sidebar-ring: 59 130 246; /* Blue 500 */

    /* Dark status background colors */
    --status-success-bg: 20 83 45; /* Green 800 */
    --status-warning-bg: 146 64 14; /* Amber 800 */
    --status-error-bg: 153 27 27; /* Red 800 */
    --status-info-bg: 30 58 138; /* Blue 800 */
    --status-neutral-bg: 51 65 85; /* Slate 600 */

    /* Versar professional colors - dark mode */
    --versar-teal: 59 130 246; /* Blue 500 - Accessible in dark */
    --versar-teal-light: 147 197 253; /* Blue 300 - Light accent */
    --versar-navy: 100 116 139; /* Slate 500 for dark mode */
    --versar-gray: 156 163 175; /* Gray 400 - Readable in dark */
    --versar-light-teal: 147 197 253; /* Blue 300 - Light accent */
    --versar-gradient-start: 59 130 246; /* Blue 500 */
    --versar-gradient-end: 147 197 253; /* Blue 300 */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Professional typography - Versar style */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: -0.025em;
  }
  
  h1 {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
  
  h2 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
  
  h3 {
    font-size: 1.5rem;
    line-height: 2rem;
  }
  
  /* Professional corporate fonts */
  body {
    font-family: "Inter", "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }
  
  /* Professional headings */
  h1, h2, h3, h4, h5, h6 {
    font-family: "Inter", "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }
  
  /* Clean link styling */
  a {
    color: hsl(var(--versar-teal));
    text-decoration: none;
  }
  
  a:hover {
    text-decoration: underline;
  }
}

/* Versar professional brand utilities */
@layer utilities {
  .versar-gradient {
    background: linear-gradient(135deg, 
      hsl(var(--versar-gradient-start)) 0%,
      hsl(var(--versar-gradient-end)) 100%);
  }
  
  .versar-gradient-text {
    background: linear-gradient(135deg, 
      hsl(var(--versar-gradient-start)) 0%,
      hsl(var(--versar-gradient-end)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .versar-teal {
    color: hsl(var(--versar-teal));
  }
  
  .bg-versar-teal {
    background-color: hsl(var(--versar-teal));
  }
  
  .versar-teal-light {
    color: hsl(var(--versar-teal-light));
  }
  
  .bg-versar-teal-light {
    background-color: hsl(var(--versar-teal-light));
  }
  
  .versar-navy {
    color: hsl(var(--versar-navy));
  }
  
  .bg-versar-navy {
    background-color: hsl(var(--versar-navy));
  }
  
  .versar-gray {
    color: hsl(var(--versar-gray));
  }
  
  .bg-versar-gray {
    background-color: hsl(var(--versar-gray));
  }
  
  /* Professional card styling */
  .versar-card {
    @apply bg-card border border-border rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200;
  }
  
  .versar-card-header {
    @apply p-6 pb-4 border-b border-border/50;
  }
  
  .versar-card-content {
    @apply p-6 pt-4;
  }
  
  /* Professional typography utilities */
  .versar-heading {
    @apply font-semibold text-foreground tracking-tight;
  }
  
  .versar-subheading {
    @apply font-medium text-muted-foreground tracking-tight;
  }
  
  .versar-body {
    @apply text-foreground leading-relaxed;
  }
  
  .versar-caption {
    @apply text-sm text-muted-foreground leading-normal;
  }
  
  /* Enhanced table styling */
  .versar-table {
    @apply w-full border-collapse;
  }
  
  .versar-table-header {
    @apply bg-secondary/50 border-b border-border;
  }
  
  .versar-table-row {
    @apply border-b border-border hover:bg-secondary/30 transition-colors duration-150;
  }
  
  /* Dashboard and page headings */
  .dashboard-title, .page-title {
    color: rgb(31 41 55) !important; /* Gray 700 - Dark text for light background */
  }
  
  .dark .dashboard-title, .dark .page-title {
    color: rgb(248 250 252) !important; /* Light gray for dark background */
  }
  
  /* Override any problematic heading inheritance */
  h1.text-3xl, h2.text-2xl, h3.text-xl {
    color: rgb(31 41 55) !important; /* Ensure proper contrast */
  }
  
  .dark h1.text-3xl, .dark h2.text-2xl, .dark h3.text-xl {
    color: rgb(248 250 252) !important; /* Ensure proper contrast in dark mode */
  }
}