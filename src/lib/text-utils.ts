// Text color mapping for converting hardcoded colors to semantic tokens
// This helps maintain theme consistency and accessibility

export const getSemanticTextClass = (originalClass: string): string => {
  const textColorMap: Record<string, string> = {
    // Primary text colors
    'text-gray-900': 'text-foreground',
    'text-gray-800': 'text-foreground',
    'text-gray-700': 'text-foreground',
    
    // Secondary/muted text colors  
    'text-gray-600': 'text-muted-foreground',
    'text-gray-500': 'text-muted-foreground',
    'text-gray-400': 'text-muted-foreground',
    
    // Primary brand colors
    'text-blue-600': 'text-primary',
    'text-blue-700': 'text-primary',
    'text-blue-800': 'text-primary',
    
    // Success colors
    'text-green-600': 'text-success',
    'text-green-700': 'text-success',
    'text-green-800': 'text-success',
    
    // Error/destructive colors
    'text-red-600': 'text-destructive',
    'text-red-700': 'text-destructive',
    'text-red-800': 'text-destructive',
    
    // Warning colors
    'text-yellow-600': 'text-warning',
    'text-yellow-700': 'text-warning',
    'text-yellow-800': 'text-warning',
    'text-amber-600': 'text-warning',
    'text-amber-700': 'text-warning',
    'text-amber-800': 'text-warning',
    
    // Info colors
    'text-blue-500': 'text-info',
    'text-sky-600': 'text-info',
    
    // Purple/accent colors
    'text-purple-600': 'text-accent-foreground',
    'text-purple-700': 'text-accent-foreground',
    'text-purple-800': 'text-accent-foreground',
  };

  return textColorMap[originalClass] || originalClass;
};

// Utility function to replace multiple text classes in a className string
export const convertToSemanticText = (className: string): string => {
  const classes = className.split(' ');
  const convertedClasses = classes.map(cls => 
    cls.startsWith('text-') ? getSemanticTextClass(cls) : cls
  );
  return convertedClasses.join(' ');
};

// Common text class patterns for easy replacement
export const TEXT_PATTERNS = {
  // Primary text
  heading: 'text-foreground font-bold',
  subheading: 'text-foreground font-medium',
  body: 'text-foreground',
  
  // Secondary text
  muted: 'text-muted-foreground',
  description: 'text-muted-foreground',
  caption: 'text-muted-foreground text-sm',
  
  // Interactive text
  link: 'text-primary hover:text-primary/80',
  button: 'text-primary-foreground',
  
  // Status text
  success: 'text-success',
  warning: 'text-warning', 
  error: 'text-destructive',
  info: 'text-info',
} as const;