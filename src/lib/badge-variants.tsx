import React from "react";
import { StatusBadge } from "@/components/ui/status-badge";
import { Badge } from "@/components/ui/badge";

// Status Badge Utilities for consistent badge styling across the application

export const getStatusBadge = (status: string, children?: React.ReactNode) => {
  const statusVariants: Record<string, 'success' | 'warning' | 'error' | 'info' | 'neutral'> = {
    active: 'success',
    completed: 'info',
    success: 'success',
    'on-hold': 'warning',
    running: 'warning',
    processing: 'warning',
    failed: 'error',
    error: 'error',
    queued: 'neutral',
    inactive: 'neutral',
    scheduled: 'info',
    pending: 'neutral'
  };

  const variant = statusVariants[status.toLowerCase()] || 'neutral';
  
  return (
    <StatusBadge variant={variant}>
      {children || status}
    </StatusBadge>
  );
};

export const getJobTypeBadge = (jobType: string) => {
  const jobTypeVariants: Record<string, 'info' | 'success' | 'warning'> = {
    manual: 'info',
    scheduled: 'success',
    event: 'warning'
  };

  const variant = jobTypeVariants[jobType.toLowerCase()] || 'info';
  
  return (
    <StatusBadge variant={variant}>
      {jobType}
    </StatusBadge>
  );
};

export const getRoleBadge = (role: string) => {
  const roleVariants: Record<string, 'destructive' | 'info' | 'secondary'> = {
    admin: 'destructive',
    reader: 'info',
    user: 'secondary',
    root: 'destructive',
    org: 'info'
  };

  const variant = roleVariants[role.toLowerCase()] || 'secondary';
  
  return (
    <StatusBadge variant={variant}>
      {role}
    </StatusBadge>
  );
};

export const getPriorityBadge = (priority: string) => {
  const priorityVariants: Record<string, 'error' | 'warning' | 'info' | 'neutral'> = {
    high: 'error',
    medium: 'warning', 
    normal: 'info',
    low: 'neutral'
  };

  const variant = priorityVariants[priority.toLowerCase()] || 'neutral';
  
  return (
    <StatusBadge variant={variant}>
      {priority}
    </StatusBadge>
  );
};