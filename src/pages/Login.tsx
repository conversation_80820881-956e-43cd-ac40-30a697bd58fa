import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Building2, Shield, FolderOpen, Upload, Eye } from "lucide-react";

interface User {
  id: string;
  name: string;
  email: string;
  role: 'global_admin' | 'org_admin' | 'project_admin' | 'contributor' | 'reader';
  organizationName?: string;
}

interface LoginProps {
  onLogin: (user: User) => void;
}

const dummyUsers: User[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "global_admin"
  },
  {
    id: "2", 
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "org_admin",
    organizationName: "Acme Corporation"
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "project_admin",
    organizationName: "Acme Corporation"
  },
  {
    id: "4",
    name: "<PERSON>",
    email: "sarah@contributor",
    role: "contributor",
    organizationName: "Acme Corporation"
  },
  {
    id: "5",
    name: "Tom Reader",
    email: "tom@reader",
    role: "reader",
    organizationName: "Acme Corporation"
  }
];

export default function Login({ onLogin }: LoginProps) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const user = dummyUsers.find(u => u.email === email);
    
    if (user && password === "password") {
      onLogin(user);
    } else {
      setError("Invalid email or password");
    }
    
    setIsLoading(false);
  };

  const handleDemoLogin = (user: User) => {
    setEmail(user.email);
    setPassword("password");
    onLogin(user);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-accent flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        <div className="text-center">
          <div className="mx-auto w-64 h-64 flex items-center justify-center mb-2">
            <img src="/logo.png" alt="Versar Logo" className="w-64 h-64 object-contain" />
          </div>
          <h2 className="-mt-4 text-3xl font-bold text-foreground">Geospatial and Digital Solutions</h2>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Sign In</CardTitle>
            <CardDescription>Enter your credentials to access the portal</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Signing in..." : "Sign In"}
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Demo Access</CardTitle>
            <CardDescription>Click below to sign in as different user types</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button
              variant="outline"
              className="w-full justify-start hover:bg-destructive/10"
              onClick={() => handleDemoLogin(dummyUsers[0])}
            >
              <Shield className="mr-2 h-4 w-4 text-destructive" />
              <div className="text-left">
                <div className="font-medium">Alice Johnson (<EMAIL>)</div>
                <div className="text-xs text-muted-foreground">Global Administrator</div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              className="w-full justify-start hover:bg-primary/10"
              onClick={() => handleDemoLogin(dummyUsers[1])}
            >
              <Building2 className="mr-2 h-4 w-4 text-primary" />
              <div className="text-left">
                <div className="font-medium">John Smith (<EMAIL>)</div>
                <div className="text-xs text-muted-foreground">Organization Administrator - Acme Corporation</div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              className="w-full justify-start hover:bg-blue-600/10"
              onClick={() => handleDemoLogin(dummyUsers[2])}
            >
              <FolderOpen className="mr-2 h-4 w-4 text-blue-600" />
              <div className="text-left">
                <div className="font-medium">Mike Wilson (<EMAIL>)</div>
                <div className="text-xs text-muted-foreground">Project Administrator - Acme Corporation</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="w-full justify-start hover:bg-green-600/10"
              onClick={() => handleDemoLogin(dummyUsers[3])}
            >
              <Upload className="mr-2 h-4 w-4 text-green-600" />
              <div className="text-left">
                <div className="font-medium">Sarah Davis (sarah@contributor)</div>
                <div className="text-xs text-muted-foreground">Contributor - Acme Corporation</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="w-full justify-start hover:bg-gray-600/10"
              onClick={() => handleDemoLogin(dummyUsers[4])}
            >
              <Eye className="mr-2 h-4 w-4 text-gray-600" />
              <div className="text-left">
                <div className="font-medium">Tom Reader (tom@reader)</div>
                <div className="text-xs text-muted-foreground">Reader - Acme Corporation</div>
              </div>
            </Button>

            <div className="text-xs text-muted-foreground mt-2 text-center">
              Or use email/password combination: <code className="bg-muted px-1 rounded">password</code>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}