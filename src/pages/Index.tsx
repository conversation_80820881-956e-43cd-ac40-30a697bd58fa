import { useState } from "react";
import { TopNavBar } from "@/components/TopNavBar";
import { SideNavigation } from "@/components/SideNavigation";
import { Dashboard } from "@/components/Dashboard";
import { ProjectDashboard } from "@/components/ProjectDashboard";
import { UsersManagement } from "@/components/UsersManagement";
import { OrgJobDefinitions } from "@/components/OrgJobDefinitions";
import { RootAdminManagement } from "@/components/RootAdminManagement";
import { UserProjectPortal } from "@/components/UserProjectPortal";
import { NotificationChannels } from "@/components/NotificationChannels";
import { RootBilling } from "@/components/RootBilling";
import { OrgBilling } from "@/components/OrgBilling";
import { OrgBillingHistory } from "@/components/OrgBillingHistory";
import { BillingReports } from "@/components/BillingReports";
import { OrganizationsManagement } from "@/components/OrganizationsManagement";

interface User {
  id: string;
  name: string;
  email: string;
  role: 'global_admin' | 'org_admin' | 'project_admin' | 'contributor' | 'reader';
  organizationName?: string;
}

interface IndexProps {
  user: User;
  onLogout: () => void;
}

const Index = ({ user, onLogout }: IndexProps) => {
  const [activeSection, setActiveSection] = useState('dashboard');

  // All users now use the same navigation system

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return <ProjectDashboard userRole={user.role} />;
      case 'users':
        return <UsersManagement userRole={user.role} />;
      case 'org-job-definitions':
        return <OrgJobDefinitions />;
      case 'notification-channels':
        return <NotificationChannels />;
      case 'root-billing':
        return <RootBilling />;
      case 'billing-reports':
        return <BillingReports />;
      case 'org-billing':
        return <OrgBilling />;
      case 'billing-history':
        return <OrgBillingHistory />;
      case 'organizations':
        if (user.role === 'org_admin') {
          return <OrganizationsManagement userRole={user.role} userEmail={user.email} />;
        }
        return <RootAdminManagement section={activeSection} userRole={user.role} userEmail={user.email} />;
      case 'global-users':
      case 'system-settings':
      case 'job-libraries':
      case 'library-assignments':
      case 'job-definitions':
        return <RootAdminManagement section={activeSection} userRole={user.role} userEmail={user.email} />;
      case 'tools':
        return <UserProjectPortal section={activeSection} />;
      default:
        return <ProjectDashboard userRole={user.role} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavBar 
        userRole={user.role} 
        userName={user.name}
        onLogout={onLogout}
      />
      <div className="flex h-[calc(100vh-4rem)]">
        <SideNavigation 
          userRole={user.role}
          activeSection={activeSection}
          onSectionChange={setActiveSection}
        />
        <main className="flex-1 overflow-auto">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default Index;
