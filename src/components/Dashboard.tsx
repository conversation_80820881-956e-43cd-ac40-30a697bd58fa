import { BarChart3, Users, Building2, FolderOpen, TrendingUp, Globe, AlertCircle, CreditCard, DollarSign, Activity, AlertTriangle, CheckCircle, XCircle, Server, Eye } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface DashboardProps {
  userRole: 'root' | 'org' | 'user';
}

export function Dashboard({ userRole }: DashboardProps) {
  const rootAdminStats = [
    {
      title: "Total Organizations",
      value: "2",
      description: "1 internal + 1 customer",
      icon: Building2,
      trend: "+100%"
    },
    {
      title: "Users",
      value: "3",
      description: "All system users",
      icon: Users,
      trend: "+0%"
    },
    {
      title: "Monthly Revenue",
      value: "$4.52K",
      description: "Total costs ($2.84K billed)",
      icon: DollarSign,
      trend: "+18.2%"
    },
    {
      title: "Active Tenants",
      value: "4",
      description: "Across all organizations",
      icon: BarChart3,
      trend: "+0%"
    }
  ];

  const orgAdminStats = [
    {
      title: "Tenants",
      value: "4",
      description: "Active storage environments",
      icon: Building2,
      trend: "+0%"
    },
    {
      title: "Projects",
      value: "3",
      description: "Active satellite projects",
      icon: FolderOpen,
      trend: "+0%"
    },
    {
      title: "Organization Users",
      value: "4",
      description: "Team members",
      icon: Users,
      trend: "+0%"
    },
    {
      title: "Current Bill",
      value: "$280.50",
      description: "December 2024",
      icon: CreditCard,
      trend: "****%"
    }
  ];

  const stats = userRole === 'root' ? rootAdminStats : orgAdminStats;

  const orgTenants = [
    {
      id: 1,
      name: "Agricultural Data Hub",
      description: "Storage for crop monitoring and agricultural satellite imagery",
      type: "Production Storage",
      storageUsed: "18.5 TB",
      storageCapacity: "25 TB",
      storagePercent: 74,
      monthlyCost: 1240,
      computeUsed: "56%",
      health: "healthy",
      lastActivity: "Crop monitoring data processed",
      activityTime: "15 min ago",
      alerts: 0,
      projects: 8,
      azureRegion: "East US 2"
    },
    {
      id: 2,
      name: "Emergency Response Storage",
      description: "Dedicated storage for disaster response and urban planning imagery",
      type: "Emergency Response",
      storageUsed: "8.2 TB",
      storageCapacity: "12 TB",
      storagePercent: 68,
      monthlyCost: 680,
      computeUsed: "32%",
      health: "healthy",
      lastActivity: "Disaster response imagery uploaded",
      activityTime: "45 min ago",
      alerts: 0,
      projects: 5,
      azureRegion: "West Europe"
    },
    {
      id: 3,
      name: "Development Sandbox",
      description: "Testing environment for new satellite processing algorithms",
      type: "Development",
      storageUsed: "4.1 TB",
      storageCapacity: "8 TB",
      storagePercent: 51,
      monthlyCost: 420,
      computeUsed: "18%",
      health: "healthy",
      lastActivity: "Algorithm testing completed",
      activityTime: "2 hours ago",
      alerts: 1,
      projects: 3,
      azureRegion: "West Europe"
    },
    {
      id: 4,
      name: "Archive Data Lake",
      description: "Long-term storage for historical satellite imagery",
      type: "Archive Storage",
      storageUsed: "45.2 TB",
      storageCapacity: "50 TB",
      storagePercent: 90,
      monthlyCost: 500,
      computeUsed: "5%",
      health: "warning",
      lastActivity: "High storage usage detected",
      activityTime: "3 hours ago",
      alerts: 2,
      projects: 8,
      azureRegion: "East US 2"
    }
  ];

  const orgProjects = [
    {
      id: 1,
      name: "Agricultural Monitoring",
      type: "Multispectral Analysis",
      tenant: "Agricultural Data Hub",
      storageUsed: "12.3 TB",
      monthlyCost: 890,
      health: "healthy",
      status: "active",
      lastJobRun: "30 min ago",
      users: 24,
      alerts: 0,
      completionRate: 98.5
    },
    {
      id: 2,
      name: "Disaster Response",
      type: "High-Resolution Optical",
      tenant: "Emergency Response Storage",
      storageUsed: "8.7 TB",
      monthlyCost: 650,
      health: "healthy",
      status: "active",
      lastJobRun: "2 hours ago",
      users: 18,
      alerts: 0,
      completionRate: 99.2
    },
    {
      id: 3,
      name: "Urban Planning Analysis",
      type: "SAR Processing",
      tenant: "Emergency Response Storage",
      storageUsed: "5.4 TB",
      monthlyCost: 420,
      health: "warning",
      status: "active",
      lastJobRun: "4 hours ago",
      users: 12,
      alerts: 1,
      completionRate: 94.8
    },
    {
      id: 4,
      name: "Research & Development",
      type: "Algorithm Testing",
      tenant: "Development Sandbox",
      storageUsed: "3.2 TB",
      monthlyCost: 280,
      health: "healthy",
      status: "active",
      lastJobRun: "1 hour ago",
      users: 8,
      alerts: 0,
      completionRate: 96.1
    },
    {
      id: 5,
      name: "Historical Data Archive",
      type: "Long-term Storage",
      tenant: "Archive Data Lake",
      storageUsed: "35.8 TB",
      monthlyCost: 180,
      health: "warning",
      status: "inactive",
      lastJobRun: "1 week ago",
      users: 6,
      alerts: 1,
      completionRate: 100
    }
  ];

  const renderOrgAdminOverview = () => (
    <div className="space-y-6">
      {/* Organization Tenants */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                Organization Tenants
              </CardTitle>
              <CardDescription>Storage environments and infrastructure health for your organization</CardDescription>
            </div>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-success" />
                <span>3 Healthy</span>
              </div>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-warning" />
                <span>1 Warning</span>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tenant</TableHead>
                <TableHead>Storage Usage</TableHead>
                <TableHead>Monthly Cost</TableHead>
                <TableHead>Compute</TableHead>
                <TableHead>Projects</TableHead>
                <TableHead>Health</TableHead>
                <TableHead>Recent Activity</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orgTenants.map((tenant) => (
                <TableRow key={tenant.id} className={`${getHealthColor(tenant.health)}`}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Server className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium">{tenant.name}</div>
                        <div className="text-sm text-muted-foreground">{tenant.type}</div>
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>{tenant.storageUsed}</span>
                        <span className="text-muted-foreground">/ {tenant.storageCapacity}</span>
                      </div>
                      <div className="w-full h-2 bg-muted rounded-full">
                        <div 
                          className={`h-2 rounded-full ${
                            tenant.storagePercent > 85 ? 'bg-destructive' :
                            tenant.storagePercent > 75 ? 'bg-warning' : 'bg-primary'
                          }`}
                          style={{ width: `${tenant.storagePercent}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-muted-foreground">{tenant.storagePercent}% used</div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4 text-success" />
                      <span className="font-bold text-success">${tenant.monthlyCost.toLocaleString()}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Activity className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{tenant.computeUsed}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <FolderOpen className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{tenant.projects}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getHealthIcon(tenant.health)}
                      <span className="text-sm capitalize">{tenant.health}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1 max-w-xs">
                      <div className="text-sm">{tenant.lastActivity}</div>
                      <div className="text-xs text-muted-foreground">{tenant.activityTime}</div>
                      {tenant.alerts > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {tenant.alerts} alerts
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Organization Projects */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5" />
                Projects
              </CardTitle>
              <CardDescription>Active projects and their performance metrics</CardDescription>
            </div>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-success" />
                <span>4 Active</span>
              </div>
              <div className="flex items-center space-x-2">
                <XCircle className="h-4 w-4 text-muted-foreground" />
                <span>1 Inactive</span>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Project</TableHead>
                <TableHead>Tenant</TableHead>
                <TableHead>Storage</TableHead>
                <TableHead>Monthly Cost</TableHead>
                <TableHead>Users</TableHead>
                <TableHead>Success Rate</TableHead>
                <TableHead>Health</TableHead>
                <TableHead>Last Job</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orgProjects.map((project) => (
                <TableRow key={project.id} className={`${getHealthColor(project.health)}`}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <FolderOpen className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <div className="font-medium">{project.name}</div>
                        <div className="text-sm text-muted-foreground">{project.type}</div>
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Server className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{project.tenant}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="text-sm font-medium">{project.storageUsed}</div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4 text-success" />
                      <span className="font-bold text-success">${project.monthlyCost.toLocaleString()}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{project.users}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <TrendingUp className={`h-4 w-4 ${project.completionRate > 95 ? 'text-success' : 'text-warning'}`} />
                      <span className="text-sm font-medium">{project.completionRate}%</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getHealthIcon(project.health)}
                      <span className="text-sm capitalize">{project.health}</span>
                      <Badge variant={project.status === 'active' ? 'default' : 'secondary'} className="text-xs">
                        {project.status}
                      </Badge>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="text-sm text-muted-foreground">{project.lastJobRun}</div>
                    {project.alerts > 0 && (
                      <Badge variant="destructive" className="text-xs mt-1">
                        {project.alerts} alerts
                      </Badge>
                    )}
                  </TableCell>
                  
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Organization Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Activity
          </CardTitle>
          <CardDescription>Latest actions and events in your organization</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {[
              { 
                action: "Agricultural monitoring job completed", 
                entity: "Agricultural Data Hub", 
                project: "Agricultural Monitoring",
                time: "30 minutes ago",
                type: "success",
                icon: CheckCircle
              },
              { 
                action: "High storage usage detected", 
                entity: "Archive Data Lake", 
                project: "Historical Data Archive",
                time: "3 hours ago",
                type: "warning",
                icon: AlertTriangle
              },
              { 
                action: "New user assigned to project", 
                entity: "Emergency Response Storage", 
                project: "Urban Planning Analysis",
                time: "5 hours ago",
                type: "info",
                icon: Users
              },
              { 
                action: "Disaster response processing started", 
                entity: "Emergency Response Storage", 
                project: "Disaster Response",
                time: "6 hours ago",
                type: "info",
                icon: Activity
              },
              { 
                action: "Algorithm testing completed", 
                entity: "Development Sandbox", 
                project: "Research & Development",
                time: "8 hours ago",
                type: "success",
                icon: CheckCircle
              },
              { 
                action: "Data validation in progress", 
                entity: "Emergency Response Storage", 
                project: "Urban Planning Analysis",
                time: "12 hours ago",
                type: "info",
                icon: Server
              }
            ].map((activity, index) => (
              <div key={index} className="flex items-start space-x-3 p-4 bg-muted/30 rounded-lg border hover:bg-muted/50 transition-colors">
                <div className={`p-2 rounded-lg ${
                  activity.type === 'success' ? 'bg-success/10' :
                  activity.type === 'warning' ? 'bg-warning/10' :
                  'bg-info/10'
                }`}>
                  <activity.icon className={`h-4 w-4 ${
                    activity.type === 'success' ? 'text-success' :
                    activity.type === 'warning' ? 'text-warning' :
                    'text-info'
                  }`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-foreground leading-tight">{activity.action}</p>
                  <p className="text-sm text-muted-foreground mt-1">{activity.entity}</p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-muted-foreground">{activity.project}</span>
                    <span className="text-xs text-muted-foreground">{activity.time}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const organizations = [
    {
      id: 1,
      name: "Versar Inc",
      tenants: 8,
      projects: 23,
      users: 45,
      status: "active",
      isInternal: true,
      monthlyCost: 1680, // Internal organization - cost tracked but not billed
      health: "healthy",
      activeAlerts: 1,
      alerts: [
        { type: "info", message: "Monthly system maintenance scheduled for weekend", severity: "low" }
      ],
      tenantHealth: { healthy: 8, warning: 0, critical: 0 }
    },
    {
      id: 2,
      name: "Acme Corporation",
      tenants: 12,
      projects: 45,
      users: 156,
      status: "active",
      isInternal: false,
      monthlyCost: 2840,
      health: "healthy",
      activeAlerts: 2,
      alerts: [
        { type: "warning", message: "High storage usage in Production tenant", severity: "medium" },
        { type: "info", message: "Scheduled maintenance window tonight", severity: "low" }
      ],
      tenantHealth: { healthy: 10, warning: 2, critical: 0 }
    }
  ];

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'healthy': return <CheckCircle className="h-4 w-4 text-success" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-warning" />;
      case 'critical': return <XCircle className="h-4 w-4 text-destructive" />;
      default: return <Activity className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'border-success/20 bg-success/5';
      case 'warning': return 'border-warning/20 bg-warning/5';
      case 'critical': return 'border-destructive/20 bg-destructive/5';
      default: return 'border-border bg-card';
    }
  };

  const renderRootAdminOverview = () => (
    <div className="space-y-6">
      {/* All Organizations Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Organizations
              </CardTitle>
              <CardDescription>Cost, health status, and active alerts for each organization</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Organization</TableHead>
                <TableHead>Resources</TableHead>
                <TableHead>Monthly Cost</TableHead>
                <TableHead>Health</TableHead>
                <TableHead>Tenant Health</TableHead>
                <TableHead>Alerts</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {organizations.map((org) => (
                <TableRow key={org.id} className={`${getHealthColor(org.health)}`}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Building2 className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <div className="font-medium">{org.name}</div>
                        <div className="flex items-center space-x-1 mt-1">
                          <Badge variant={org.status === 'active' ? 'default' : 'secondary'} className="text-xs">
                            {org.status}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {org.isInternal ? "System Default" : "User Created"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">
                        <span className="font-medium text-primary">{org.tenants}</span> tenants
                      </div>
                      <div className="text-sm">
                        <span className="font-medium text-info">{org.projects}</span> projects
                      </div>
                      <div className="text-sm">
                        <span className="font-medium text-purple-600">{org.users}</span> users
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4 text-success" />
                      <span className="font-bold text-success">
                        ${org.monthlyCost.toLocaleString()}
                      </span>
                      {org.isInternal && (
                        <span className="text-xs text-muted-foreground">(Internal)</span>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getHealthIcon(org.health)}
                      <span className="text-sm capitalize">{org.health}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex space-x-1">
                      {org.tenantHealth.healthy > 0 && (
                        <div className="flex items-center space-x-1 bg-success/10 px-2 py-1 rounded text-xs">
                          <div className="w-2 h-2 bg-success rounded-full"></div>
                          <span>{org.tenantHealth.healthy}</span>
                        </div>
                      )}
                      {org.tenantHealth.warning > 0 && (
                        <div className="flex items-center space-x-1 bg-warning/10 px-2 py-1 rounded text-xs">
                          <div className="w-2 h-2 bg-warning rounded-full"></div>
                          <span>{org.tenantHealth.warning}</span>
                        </div>
                      )}
                      {org.tenantHealth.critical > 0 && (
                        <div className="flex items-center space-x-1 bg-destructive/10 px-2 py-1 rounded text-xs">
                          <div className="w-2 h-2 bg-destructive rounded-full"></div>
                          <span>{org.tenantHealth.critical}</span>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-2">
                      <Badge variant={org.activeAlerts > 0 ? 'destructive' : 'secondary'} className="text-xs">
                        {org.activeAlerts} alerts
                      </Badge>
                      {org.alerts.length > 0 && (
                        <div className="space-y-1 max-w-xs">
                          {org.alerts.slice(0, 1).map((alert, alertIndex) => (
                            <div key={alertIndex} className="text-xs p-1 bg-muted/30 rounded flex items-start space-x-1">
                              <AlertCircle className={`h-3 w-3 mt-0.5 flex-shrink-0 ${
                                alert.type === 'error' ? 'text-destructive' : 
                                alert.type === 'warning' ? 'text-warning' : 'text-info'
                              }`} />
                              <span className="text-muted-foreground leading-tight line-clamp-2">{alert.message}</span>
                            </div>
                          ))}
                          {org.alerts.length > 1 && (
                            <div className="text-xs text-muted-foreground">
                              +{org.alerts.length - 1} more
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Tenants Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                Tenants
              </CardTitle>
              <CardDescription>Health status, data usage, and alerts across all satellite image processing tenants</CardDescription>
            </div>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-success" />
                <span>5 Healthy</span>
              </div>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-warning" />
                <span>1 Warning</span>
              </div>
              <div className="flex items-center space-x-2">
                <XCircle className="h-4 w-4 text-destructive" />
                <span>0 Critical</span>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tenant</TableHead>
                <TableHead>Organization</TableHead>
                <TableHead>Storage Usage</TableHead>
                <TableHead>Compute Usage</TableHead>
                <TableHead>Health</TableHead>
                <TableHead>Recent Activity</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[
                {
                  id: 1,
                  name: "Versar Internal Hub",
                  organization: "Versar Inc",
                  type: "Internal Production",
                  storageUsed: "12.4 TB",
                  storageCapacity: "20 TB",
                  storagePercent: 62,
                  computeUsed: "45%",
                  health: "healthy",
                  lastActivity: "Internal processing completed",
                  activityTime: "5 min ago",
                  alerts: 0
                },
                {
                  id: 2,
                  name: "Versar Development Lab",
                  organization: "Versar Inc",
                  type: "Internal Development",
                  storageUsed: "3.2 TB",
                  storageCapacity: "5 TB",
                  storagePercent: 64,
                  computeUsed: "23%",
                  health: "healthy",
                  lastActivity: "Algorithm testing in progress",
                  activityTime: "12 min ago",
                  alerts: 0
                },
                {
                  id: 3,
                  name: "Agricultural Data Hub",
                  organization: "Acme Corporation",
                  type: "Production Storage",
                  storageUsed: "18.5 TB",
                  storageCapacity: "25 TB",
                  storagePercent: 74,
                  computeUsed: "56%",
                  health: "healthy",
                  lastActivity: "Crop monitoring data processed",
                  activityTime: "15 min ago",
                  alerts: 0
                },
                {
                  id: 4,
                  name: "Emergency Response Storage",
                  organization: "Acme Corporation",
                  type: "Emergency Response",
                  storageUsed: "8.2 TB",
                  storageCapacity: "12 TB",
                  storagePercent: 68,
                  computeUsed: "32%",
                  health: "healthy",
                  lastActivity: "Disaster response imagery uploaded",
                  activityTime: "45 min ago",
                  alerts: 0
                },
                {
                  id: 5,
                  name: "Development Sandbox",
                  organization: "Acme Corporation",
                  type: "Development",
                  storageUsed: "4.1 TB",
                  storageCapacity: "8 TB",
                  storagePercent: 51,
                  computeUsed: "18%",
                  health: "healthy",
                  lastActivity: "Algorithm testing completed",
                  activityTime: "2 hours ago",
                  alerts: 1
                },
                {
                  id: 6,
                  name: "Archive Data Lake",
                  organization: "Acme Corporation",
                  type: "Archive Storage",
                  storageUsed: "45.2 TB",
                  storageCapacity: "50 TB",
                  storagePercent: 90,
                  computeUsed: "5%",
                  health: "warning",
                  lastActivity: "High storage usage detected",
                  activityTime: "3 hours ago",
                  alerts: 2
                }
              ].map((tenant) => (
                <TableRow key={tenant.id} className={`${getHealthColor(tenant.health)}`}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Server className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium">{tenant.name}</div>
                        <div className="text-sm text-muted-foreground">{tenant.type}</div>
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{tenant.organization}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>{tenant.storageUsed}</span>
                        <span className="text-muted-foreground">/ {tenant.storageCapacity}</span>
                      </div>
                      <div className="w-full h-2 bg-muted rounded-full">
                        <div 
                          className={`h-2 rounded-full ${
                            tenant.storagePercent > 80 ? 'bg-warning' : 
                            tenant.storagePercent > 90 ? 'bg-destructive' : 'bg-primary'
                          }`}
                          style={{ width: `${tenant.storagePercent}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-muted-foreground">{tenant.storagePercent}% used</div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Activity className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{tenant.computeUsed}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getHealthIcon(tenant.health)}
                      <span className="text-sm capitalize">{tenant.health}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1 max-w-xs">
                      <div className="text-sm">{tenant.lastActivity}</div>
                      <div className="text-xs text-muted-foreground">{tenant.activityTime}</div>
                      {tenant.alerts > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {tenant.alerts} alerts
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Activity
          </CardTitle>
          <CardDescription>Latest actions across all organizations and tenants</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {[
              { 
                action: "Image processing job completed", 
                entity: "Versar-Production", 
                organization: "Versar Inc",
                time: "5 minutes ago",
                type: "success",
                icon: CheckCircle
              },
              { 
                action: "High storage usage detected", 
                entity: "Acme-Production", 
                organization: "Acme Corporation",
                time: "2 hours ago",
                type: "warning",
                icon: AlertTriangle
              },
              { 
                action: "New tenant created", 
                entity: "Acme-Staging", 
                organization: "Acme Corporation",
                time: "4 hours ago",
                type: "info",
                icon: Server
              },
              { 
                action: "Algorithm testing completed", 
                entity: "Versar-Development", 
                organization: "Versar Inc",
                time: "6 hours ago",
                type: "success",
                icon: CheckCircle
              },
              { 
                action: "User assigned to project", 
                entity: "Satellite Analysis Project", 
                organization: "Acme Corporation",
                time: "8 hours ago",
                type: "info",
                icon: Users
              },
              { 
                action: "System backup completed", 
                entity: "System Maintenance", 
                organization: "Versar Inc",
                time: "12 hours ago",
                type: "success",
                icon: CheckCircle
              },
              { 
                action: "Data migration in progress", 
                entity: "Acme-Staging", 
                organization: "Acme Corporation",
                time: "1 day ago",
                type: "info",
                icon: Activity
              },
              { 
                action: "Job library updated", 
                entity: "Satellite Processing Library", 
                organization: "Versar Inc",
                time: "1 day ago",
                type: "info",
                icon: Globe
              }
            ].map((activity, index) => (
              <div key={index} className="flex items-start space-x-3 p-4 bg-muted/30 rounded-lg border hover:bg-muted/50 transition-colors">
                <div className={`p-2 rounded-lg ${
                  activity.type === 'success' ? 'bg-success/10' :
                  activity.type === 'warning' ? 'bg-warning/10' :
                  'bg-info/10'
                }`}>
                  <activity.icon className={`h-4 w-4 ${
                    activity.type === 'success' ? 'text-success' :
                    activity.type === 'warning' ? 'text-warning' :
                    'text-info'
                  }`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-foreground leading-tight">{activity.action}</p>
                  <p className="text-sm text-muted-foreground mt-1">{activity.entity}</p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-muted-foreground">{activity.organization}</span>
                    <span className="text-xs text-muted-foreground">{activity.time}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="p-6">

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
              <div className="text-xs text-green-600 font-medium mt-1">
                {stat.trend}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {userRole === 'root' && renderRootAdminOverview()}
      {userRole === 'org' && renderOrgAdminOverview()}

    </div>
  );
}
