import { useState } from "react";
import { Plus, Users, Edit, Trash2, <PERSON>, Shield } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface GlobalUser {
  id: string;
  name: string;
  email: string;
  phone?: string;
  lastLogin?: string;
  createdAt: string;
  lastUpdated: string;
  systemRoles: {
    role: 'global_admin';
    permissions: string[];
  }[];
  organizationRoles: {
    organizationId: string;
    role: 'org_admin' | 'project_admin' | 'contributor' | 'reader';
    permissions: string[];
  }[];
}


export function GlobalUsersManagement() {
  const [users, setUsers] = useState<GlobalUser[]>([
    {
      id: "1",
      name: "Alice Johnson",
      email: "<EMAIL>",
      lastLogin: "2024-06-18",
      createdAt: "2024-01-01",
      lastUpdated: "2024-06-18",
      systemRoles: [
        {
          role: "global_admin",
          permissions: ["create_organizations", "manage_global_users", "view_all_billing", "system_settings"]
        }
      ],
      organizationRoles: []
    },
    {
      id: "2", 
      name: "Bob Smith",
      email: "<EMAIL>",
      lastLogin: "2024-06-17",
      createdAt: "2024-01-15",
      lastUpdated: "2024-06-17",
      systemRoles: [
      ],
      organizationRoles: [
        {
          organizationId: "1",
          role: "org_admin",
          permissions: ["create_projects", "manage_users", "manage_tenants", "view_billing"]
        }
      ]
    },
    {
      id: "3",
      name: "Carol Davis", 
      email: "<EMAIL>",
      lastLogin: "2024-06-16",
      createdAt: "2024-02-10",
      lastUpdated: "2024-06-16",
      systemRoles: [],
      organizationRoles: []
    }
  ]);

  const [selectedUser, setSelectedUser] = useState<GlobalUser | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);

  const [formData, setFormData] = useState<Partial<GlobalUser>>({
    name: "",
    email: ""
  });

  const handleCreate = () => {
    const newUser: GlobalUser = {
      id: Date.now().toString(),
      name: formData.name || "",
      email: formData.email || "",
      lastLogin: "Never",
      createdAt: new Date().toISOString().split('T')[0],
      lastUpdated: new Date().toISOString().split('T')[0],
      systemRoles: [],
      organizationRoles: []
    };

    setUsers([...users, newUser]);
    resetForm();
    setIsCreateDialogOpen(false);
  };

  const handleEdit = () => {
    if (!selectedUser) return;

    const updatedUsers = users.map(user =>
      user.id === selectedUser.id
        ? { 
            ...user, 
            ...formData, 
            lastUpdated: new Date().toISOString().split('T')[0] 
          }
        : user
    );

    setUsers(updatedUsers);
    setIsEditDialogOpen(false);
    setSelectedUser(null);
    resetForm();
  };

  const handleDelete = (userId: string) => {
    setUsers(users.filter(user => user.id !== userId));
  };

  const resetForm = () => {
    setFormData({
      name: "",
      email: ""
    });
  };

  const openEditDialog = (user: GlobalUser) => {
    setSelectedUser(user);
    setFormData(user);
    setIsEditDialogOpen(true);
  };

  const openViewDialog = (user: GlobalUser) => {
    setSelectedUser(user);
    setIsViewDialogOpen(true);
  };

  const getRolesBadges = (user: GlobalUser) => {
    const badges = [];
    
    // Ensure user has the required properties
    const systemRoles = user.systemRoles || [];
    const organizationRoles = user.organizationRoles || [];
    
    // System roles
    systemRoles.forEach(sysRole => {
      const colors: Record<string, string> = {
        global_admin: 'bg-red-100 text-red-800'
      };
      const labels: Record<string, string> = {
        global_admin: 'Global Admin'
      };
      badges.push(
        <Badge key={`sys-${sysRole.role}`} className={colors[sysRole.role]}>
          {labels[sysRole.role]} (System)
        </Badge>
      );
    });
    
    // Organization roles count
    if (organizationRoles.length > 0) {
      badges.push(
        <Badge key="org-roles" variant="outline" className="bg-blue-50 text-blue-700">
          {organizationRoles.length} Org Role{organizationRoles.length !== 1 ? 's' : ''}
        </Badge>
      );
    }
    
    // No roles assigned
    if (systemRoles.length === 0 && organizationRoles.length === 0) {
      badges.push(
        <Badge key="no-roles" variant="secondary" className="bg-yellow-50 text-yellow-700">
          No Roles Assigned
        </Badge>
      );
    }
    
    return badges;
  };


  const filteredUsers = users;

  const renderForm = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Full Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="Enter full name"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            placeholder="<EMAIL>"
          />
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Users</CardTitle>
              <CardDescription>
                {filteredUsers.length} user{filteredUsers.length !== 1 ? 's' : ''} total
              </CardDescription>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add User
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-lg">
                <DialogHeader>
                  <DialogTitle>Create New User</DialogTitle>
                  <DialogDescription>
                    Add a new user to the system
                  </DialogDescription>
                </DialogHeader>
                {renderForm()}
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreate}>Create User</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Users className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">{user.email}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-muted-foreground">{user.lastLogin}</span>
                  </TableCell>
                  <TableCell className="text-center">
                    <TooltipProvider>
                      <div className="flex items-center justify-center space-x-2">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openViewDialog(user)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>View Details</TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openEditDialog(user)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Edit User</TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-blue-600 hover:text-blue-700"
                              title="Assign Roles"
                            >
                              <Shield className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Assign Roles</TooltipContent>
                        </Tooltip>
                        <AlertDialog>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <AlertDialogTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                            </TooltipTrigger>
                            <TooltipContent>Delete User</TooltipContent>
                          </Tooltip>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete User</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete "{user.name}"? This action cannot be undone and will revoke all system access.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleDelete(user.id)}>
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TooltipProvider>
                  </TableCell>
                </TableRow>
              ))}
              </TableBody>
            </Table>
          </CardContent>
      </Card>


      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information
            </DialogDescription>
          </DialogHeader>
          {renderForm()}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEdit}>Update User</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
            <DialogDescription>
              Complete user information
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Users className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg text-foreground">{selectedUser.name}</h3>
                  <p className="text-muted-foreground">{selectedUser.email}</p>
                  <div className="mt-2 flex flex-wrap gap-1">
                    {getRolesBadges(selectedUser)}
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-sm text-foreground mb-3">Activity</h3>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium text-foreground">Last Login:</span>
                      <p className="text-sm text-muted-foreground">{selectedUser.lastLogin}</p>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-semibold text-sm text-foreground mb-3">Timeline</h3>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium text-foreground">Created:</span>
                      <p className="text-sm text-muted-foreground">{selectedUser.createdAt}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-foreground">Last Updated:</span>
                      <p className="text-sm text-muted-foreground">{selectedUser.lastUpdated}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}