import { useState } from "react";
import { Plus, <PERSON>tings, Edit, Trash2, Bell, Mail, MessageSquare, Phone } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>og<PERSON>rigger } from "@/components/ui/alert-dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";

type ChannelType = "email" | "sms" | "webhook" | "slack";
type ChannelStatus = "active" | "inactive" | "error";
type Provider = "twilio" | "aws-sns" | "nexmo";
type AuthType = "none" | "basic" | "bearer" | "api-key" | "custom";

interface NotificationChannel {
  id: string;
  name: string;
  type: ChannelType;
  status: ChannelStatus;
  description: string;
  provider?: Provider;
  endpoint?: string;
  authType?: AuthType;
  lastUsed: string;
  createdAt: string;
  isEnabled: boolean;
  config: Record<string, any>;
}

export function NotificationChannels() {
  const [channels, setChannels] = useState<NotificationChannel[]>([
    {
      id: "1",
      name: "Production Email Alerts",
      type: "email",
      status: "active",
      description: "Critical system alerts for production environment",
      endpoint: "<EMAIL>",
      authType: "none",
      lastUsed: "2024-06-19",
      createdAt: "2024-01-15",
      isEnabled: true,
      config: {
        smtpServer: "smtp.company.com",
        port: 587,
        encryption: "TLS"
      }
    },
    {
      id: "2",
      name: "SMS Emergency Notifications",
      type: "sms",
      status: "active",
      description: "Emergency notifications for critical system failures",
      provider: "twilio",
      authType: "api-key",
      lastUsed: "2024-06-18",
      createdAt: "2024-02-01",
      isEnabled: true,
      config: {
        accountSid: "AC***",
        fromNumber: "+**********"
      }
    },
    {
      id: "3",
      name: "Slack Integration",
      type: "webhook",
      status: "active",
      description: "Team notifications in #alerts channel",
      endpoint: "https://hooks.slack.com/services/...",
      authType: "bearer",
      lastUsed: "2024-06-19",
      createdAt: "2024-03-10",
      isEnabled: true,
      config: {
        channel: "#alerts",
        username: "AlertBot"
      }
    },
    {
      id: "4",
      name: "Development Webhook",
      type: "webhook",
      status: "inactive",
      description: "Development environment notifications",
      endpoint: "https://dev-webhook.company.com/alerts",
      authType: "basic",
      lastUsed: "2024-06-10",
      createdAt: "2024-04-05",
      isEnabled: false,
      config: {}
    }
  ]);

  const [selectedChannel, setSelectedChannel] = useState<NotificationChannel | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false);

  const [formData, setFormData] = useState<Partial<NotificationChannel>>({
    name: "",
    type: "email",
    status: "active",
    description: "",
    endpoint: "",
    provider: "twilio",
    authType: "none",
    isEnabled: true,
    config: {}
  });

  const handleCreate = () => {
    const newChannel: NotificationChannel = {
      id: Date.now().toString(),
      name: formData.name || "",
      type: formData.type as ChannelType,
      status: formData.status as ChannelStatus,
      description: formData.description || "",
      endpoint: formData.endpoint,
      provider: formData.provider as Provider,
      authType: formData.authType as AuthType,
      lastUsed: "Never",
      createdAt: new Date().toISOString().split('T')[0],
      isEnabled: formData.isEnabled || true,
      config: formData.config || {}
    };

    setChannels([...channels, newChannel]);
    resetForm();
    setIsCreateDialogOpen(false);
  };

  const handleEdit = () => {
    if (!selectedChannel) return;

    const updatedChannels = channels.map(channel =>
      channel.id === selectedChannel.id
        ? {
            ...channel,
            name: formData.name || channel.name,
            type: formData.type as ChannelType || channel.type,
            status: formData.status as ChannelStatus || channel.status,
            description: formData.description || channel.description,
            endpoint: formData.endpoint || channel.endpoint,
            provider: formData.provider as Provider || channel.provider,
            authType: formData.authType as AuthType || channel.authType,
            isEnabled: formData.isEnabled !== undefined ? formData.isEnabled : channel.isEnabled,
            config: { ...channel.config, ...formData.config }
          }
        : channel
    );

    setChannels(updatedChannels);
    setIsEditDialogOpen(false);
    setSelectedChannel(null);
    resetForm();
  };

  const handleDelete = (channelId: string) => {
    setChannels(channels.filter(channel => channel.id !== channelId));
  };

  const handleToggleStatus = (channelId: string) => {
    setChannels(channels.map(channel =>
      channel.id === channelId
        ? { ...channel, isEnabled: !channel.isEnabled, status: !channel.isEnabled ? 'active' : 'inactive' }
        : channel
    ));
  };

  const openEditDialog = (channel: NotificationChannel) => {
    setSelectedChannel(channel);
    setFormData({
      name: channel.name,
      type: channel.type,
      status: channel.status,
      description: channel.description,
      endpoint: channel.endpoint,
      provider: channel.provider,
      authType: channel.authType,
      isEnabled: channel.isEnabled,
      config: channel.config
    });
    setIsEditDialogOpen(true);
  };

  const openConfigDialog = (channel: NotificationChannel) => {
    setSelectedChannel(channel);
    setFormData({
      config: channel.config
    });
    setIsConfigDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: "",
      type: "email",
      status: "active",
      description: "",
      endpoint: "",
      provider: "twilio",
      authType: "none",
      isEnabled: true,
      config: {}
    });
  };

  const getChannelIcon = (type: ChannelType) => {
    switch (type) {
      case "email":
        return <Mail className="h-4 w-4" />;
      case "sms":
        return <MessageSquare className="h-4 w-4" />;
      case "webhook":
        return <Bell className="h-4 w-4" />;
      case "slack":
        return <MessageSquare className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: ChannelStatus, isEnabled: boolean) => {
    if (!isEnabled) {
      return <Badge variant="secondary">Disabled</Badge>;
    }
    
    const variants: Record<ChannelStatus, 'default' | 'destructive' | 'secondary'> = {
      active: 'default',
      inactive: 'secondary',
      error: 'destructive'
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const renderCreateEditDialog = (isEdit: boolean) => (
    <Dialog 
      open={isEdit ? isEditDialogOpen : isCreateDialogOpen} 
      onOpenChange={isEdit ? setIsEditDialogOpen : setIsCreateDialogOpen}
    >
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{isEdit ? 'Edit' : 'Create'} Notification Channel</DialogTitle>
          <DialogDescription>
            {isEdit ? 'Update the notification channel settings' : 'Create a new notification channel for alerts and notifications'}
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Channel Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter channel name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">Channel Type</Label>
              <Select 
                value={formData.type} 
                onValueChange={(value) => setFormData({ ...formData, type: value as ChannelType })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="sms">SMS</SelectItem>
                  <SelectItem value="webhook">Webhook</SelectItem>
                  <SelectItem value="slack">Slack</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter channel description"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select 
                value={formData.status} 
                onValueChange={(value) => setFormData({ ...formData, status: value as ChannelStatus })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="endpoint">Endpoint/Address</Label>
              <Input
                id="endpoint"
                value={formData.endpoint}
                onChange={(e) => setFormData({ ...formData, endpoint: e.target.value })}
                placeholder="Email, phone number, or webhook URL"
              />
            </div>
            {(formData.type === 'sms') && (
              <div className="space-y-2">
                <Label htmlFor="provider">SMS Provider</Label>
                <Select 
                  value={formData.provider} 
                  onValueChange={(value) => setFormData({ ...formData, provider: value as Provider })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="twilio">Twilio</SelectItem>
                    <SelectItem value="aws-sns">AWS SNS</SelectItem>
                    <SelectItem value="nexmo">Nexmo</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="authType">Authentication Type</Label>
              <Select 
                value={formData.authType} 
                onValueChange={(value) => setFormData({ ...formData, authType: value as AuthType })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="basic">Basic Auth</SelectItem>
                  <SelectItem value="bearer">Bearer Token</SelectItem>
                  <SelectItem value="api-key">API Key</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="enabled"
                checked={formData.isEnabled}
                onCheckedChange={(checked) => setFormData({ ...formData, isEnabled: checked })}
              />
              <Label htmlFor="enabled">Enable Channel</Label>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => {
              if (isEdit) {
                setIsEditDialogOpen(false);
              } else {
                setIsCreateDialogOpen(false);
              }
              resetForm();
            }}
          >
            Cancel
          </Button>
          <Button onClick={isEdit ? handleEdit : handleCreate}>
            {isEdit ? 'Update' : 'Create'} Channel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Notification Channels</h1>
          <p className="text-gray-600 mt-2">Manage notification channels for alerts and system events</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Channel
            </Button>
          </DialogTrigger>
          {renderCreateEditDialog(false)}
        </Dialog>
      </div>

      <div className="grid gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Active Channels</CardTitle>
            <CardDescription>
              {channels.filter(c => c.isEnabled).length} of {channels.length} channels are currently active
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Channel</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Endpoint</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Used</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {channels.map((channel) => (
                  <TableRow key={channel.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          {getChannelIcon(channel.type)}
                        </div>
                        <div>
                          <div className="font-medium">{channel.name}</div>
                          <div className="text-sm text-gray-500">{channel.description}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="capitalize">
                        {channel.type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate">
                        {channel.endpoint || '-'}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(channel.status, channel.isEnabled)}</TableCell>
                    <TableCell>{channel.lastUsed}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(channel.id)}
                        >
                          <Switch checked={channel.isEnabled} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openConfigDialog(channel)}
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openEditDialog(channel)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Channel</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete "{channel.name}"? This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleDelete(channel.id)}>
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {renderCreateEditDialog(true)}

      {/* Configuration Dialog */}
      <Dialog open={isConfigDialogOpen} onOpenChange={setIsConfigDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Channel Configuration</DialogTitle>
            <DialogDescription>
              Advanced configuration settings for {selectedChannel?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Configuration (JSON)</Label>
              <Textarea
                value={JSON.stringify(formData.config, null, 2)}
                onChange={(e) => {
                  try {
                    const config = JSON.parse(e.target.value);
                    setFormData({ ...formData, config });
                  } catch (error) {
                    // Invalid JSON, ignore
                  }
                }}
                rows={10}
                className="font-mono text-sm"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsConfigDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => {
              handleEdit();
              setIsConfigDialogOpen(false);
            }}>
              Save Configuration
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
