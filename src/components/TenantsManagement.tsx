import { useState } from "react";
import { CreateTenantDialog } from "./tenants/CreateTenantDialog";
import { EditTenantDialog } from "./tenants/EditTenantDialog";
import { TenantDetailsDialog } from "./tenants/TenantDetailsDialog";
import { TenantsTable } from "./tenants/TenantsTable";
import { TenantsFilters } from "./tenants/TenantsFilters";
import { Tenant } from "./tenants/types";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export function TenantsManagement() {
  const [tenants, setTenants] = useState<Tenant[]>([
    {
      id: "1",
      name: "Agricultural Data Hub",
      description: "Storage for crop monitoring and agricultural satellite imagery",
      status: "active",
      endpoint: "https://agri-data.acme.versaplatform.com",
      storageType: "dedicated",
      createdAt: "2024-01-15",
      lastActivity: "2024-06-18",
      projectCount: 8,
      dataSize: "18.5 TB"
    },
    {
      id: "2",
      name: "Emergency Response Storage",
      description: "Dedicated storage for disaster response and urban planning imagery",
      status: "active",
      endpoint: "https://emergency-data.acme.versaplatform.com",
      storageType: "dedicated",
      createdAt: "2024-02-01",
      lastActivity: "2024-06-17",
      projectCount: 5,
      dataSize: "8.2 TB"
    },
    {
      id: "3",
      name: "Development Sandbox",
      description: "Testing environment for new satellite processing algorithms",
      status: "active",
      endpoint: "https://dev-sandbox.acme.versaplatform.com",
      storageType: "shared",
      createdAt: "2024-03-10",
      lastActivity: "2024-06-19",
      projectCount: 3,
      dataSize: "4.1 TB"
    },
    {
      id: "4",
      name: "Archive Data Lake",
      description: "Long-term storage for historical satellite imagery",
      status: "active",
      endpoint: "https://archive-lake.acme.versaplatform.com",
      storageType: "dedicated",
      createdAt: "2024-01-05",
      lastActivity: "2024-06-16",
      projectCount: 8,
      dataSize: "45.2 TB"
    }
  ]);

  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  const handleCreateTenant = (newTenant: Tenant) => {
    setTenants([...tenants, newTenant]);
  };

  const handleUpdateTenant = (updatedTenant: Tenant) => {
    const updatedTenants = tenants.map(tenant =>
      tenant.id === updatedTenant.id ? updatedTenant : tenant
    );
    setTenants(updatedTenants);
    setSelectedTenant(null);
  };

  const handleDelete = (tenantId: string) => {
    setTenants(tenants.filter(tenant => tenant.id !== tenantId));
  };

  const openEditDialog = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    setIsEditDialogOpen(true);
  };

  const openViewDialog = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    setIsViewDialogOpen(true);
  };

  const closeEditDialog = () => {
    setIsEditDialogOpen(false);
    setSelectedTenant(null);
  };

  const closeViewDialog = () => {
    setIsViewDialogOpen(false);
    setSelectedTenant(null);
  };

  const filteredTenants = tenants.filter(tenant => {
    const matchesSearch = tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || tenant.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Tenants</CardTitle>
              <CardDescription>
                Manage tenants for dataset image segregation
              </CardDescription>
            </div>
            <CreateTenantDialog
              onCreateTenant={handleCreateTenant}
            />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <TenantsFilters
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              statusFilter={statusFilter}
              onStatusFilterChange={setStatusFilter}
            />

            <TenantsTable
              tenants={filteredTenants}
              onEdit={openEditDialog}
              onView={openViewDialog}
              onDelete={handleDelete}
            />
          </div>
        </CardContent>
      </Card>

      <EditTenantDialog
        tenant={selectedTenant}
        isOpen={isEditDialogOpen}
        onClose={closeEditDialog}
        onUpdate={handleUpdateTenant}
      />

      <TenantDetailsDialog
        tenant={selectedTenant}
        isOpen={isViewDialogOpen}
        onClose={closeViewDialog}
      />
    </div>
  );
}