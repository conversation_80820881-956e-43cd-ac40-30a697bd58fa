import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { TenantFormFields } from "./TenantFormFields";
import { Tenant, TenantFormData } from "./types";

interface EditTenantDialogProps {
  tenant: Tenant | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (tenant: Tenant) => void;
}

export function EditTenantDialog({ 
  tenant, 
  isOpen, 
  onClose, 
  onUpdate 
}: EditTenantDialogProps) {
  const [formData, setFormData] = useState<TenantFormData>(tenant || {});

  // Update formData when tenant changes
  useState(() => {
    if (tenant) {
      setFormData(tenant);
    }
  });

  const handleEdit = () => {
    if (!tenant) return;

    const updatedTenant: Tenant = {
      ...tenant,
      ...formData
    } as Tenant;

    onUpdate(updatedTenant);
    onClose();
  };

  if (!tenant) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Edit Tenant</DialogTitle>
          <DialogDescription>
            Update tenant configuration for satellite image processing
          </DialogDescription>
        </DialogHeader>
        <TenantFormFields
          formData={formData}
          onFormDataChange={setFormData}
          isEditing={true}
        />
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleEdit} disabled={!formData.name || !formData.storageType}>
            Update Tenant
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}