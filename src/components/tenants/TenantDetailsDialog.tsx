import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Tenant } from "./types";

interface TenantDetailsDialogProps {
  tenant: Tenant | null;
  isOpen: boolean;
  onClose: () => void;
}

export function TenantDetailsDialog({ tenant, isOpen, onClose }: TenantDetailsDialogProps) {
  const getStatusBadge = (status: Tenant['status']) => {
    const variants: Record<Tenant['status'], 'default' | 'destructive' | 'secondary'> = {
      active: 'default',
      inactive: 'secondary',
      maintenance: 'destructive'
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  if (!tenant) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl">
        <DialogHeader>
          <DialogTitle>Tenant Details</DialogTitle>
          <DialogDescription>
            Complete tenant configuration and metrics
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6">
          <div className="grid grid-cols-3 gap-6">
            <div>
              <h3 className="font-semibold text-sm text-gray-700 mb-3">Basic Information</h3>
              <div className="space-y-2">
                <div>
                  <span className="text-sm font-medium">Name:</span>
                  <p className="text-sm text-gray-600">{tenant.name}</p>
                </div>
                <div>
                  <span className="text-sm font-medium">Description:</span>
                  <p className="text-sm text-gray-600">{tenant.description}</p>
                </div>
                <div>
                  <span className="text-sm font-medium">Endpoint:</span>
                  <p className="text-sm text-gray-600">{tenant.endpoint}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">Status:</span>
                  {getStatusBadge(tenant.status)}
                </div>
              </div>
            </div>
            
            
            <div>
              <h3 className="font-semibold text-sm text-gray-700 mb-3">Storage Configuration</h3>
              <div className="space-y-2">
                <div>
                  <span className="text-sm font-medium">Storage Type:</span>
                  <p className="text-sm text-gray-600 capitalize">{tenant.storageType}</p>
                </div>
                <div>
                  <span className="text-sm font-medium">Backend Configuration:</span>
                  <p className="text-sm text-gray-600">
                    {tenant.storageType === 'shared' 
                      ? 'Uses existing shared Azure Data Lake Storage within the selected account'
                      : 'Dedicated Azure Data Lake Storage account within the selected Azure account'
                    }
                  </p>
                </div>
                <div>
                  <span className="text-sm font-medium">Management:</span>
                  <p className="text-sm text-gray-600">Automated by backend services</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-sm text-gray-700 mb-3">Usage Statistics</h3>
              <div className="space-y-2">
                <div>
                  <span className="text-sm font-medium">Active Projects:</span>
                  <p className="text-sm text-gray-600">{tenant.projectCount}</p>
                </div>
                <div>
                  <span className="text-sm font-medium">Total Data Size:</span>
                  <p className="text-sm text-gray-600">{tenant.dataSize}</p>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold text-sm text-gray-700 mb-3">Timeline</h3>
              <div className="space-y-2">
                <div>
                  <span className="text-sm font-medium">Created:</span>
                  <p className="text-sm text-gray-600">{tenant.createdAt}</p>
                </div>
                <div>
                  <span className="text-sm font-medium">Last Activity:</span>
                  <p className="text-sm text-gray-600">{tenant.lastActivity}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}