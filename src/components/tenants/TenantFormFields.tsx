import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TenantFormData, Tenant } from "./types";

interface TenantFormFieldsProps {
  formData: TenantFormData;
  onFormDataChange: (data: TenantFormData) => void;
  isEditing?: boolean;
}

export function TenantFormFields({ 
  formData, 
  onFormDataChange,
  isEditing = false 
}: TenantFormFieldsProps) {
  const updateFormData = (updates: Partial<TenantFormData>) => {
    onFormDataChange({ ...formData, ...updates });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <h3 className="font-semibold text-sm text-foreground">Basic Information</h3>
        <div className="space-y-2">
          <Label htmlFor="name">Tenant Name</Label>
          <Input
            id="name"
            value={formData.name || ""}
            onChange={(e) => updateFormData({ name: e.target.value })}
            placeholder="Enter tenant name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description || ""}
            onChange={(e) => updateFormData({ description: e.target.value })}
            placeholder="Enter tenant description"
            rows={3}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="endpoint">Endpoint URL</Label>
          <Input
            id="endpoint"
            value={formData.endpoint || ""}
            onChange={(e) => updateFormData({ endpoint: e.target.value })}
            placeholder="https://tenant.example.com"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select 
            value={formData.status || "active"} 
            onValueChange={(value) => updateFormData({ status: value as Tenant['status'] })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="maintenance">Maintenance</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Storage Configuration */}
      <div className="space-y-4">
        <h3 className="font-semibold text-sm text-foreground">Storage Configuration</h3>
        <div className="space-y-2">
          <Label htmlFor="storageType">Storage Type</Label>
          <Select 
            value={formData.storageType || "shared"} 
            onValueChange={(value) => updateFormData({ storageType: value as 'shared' | 'dedicated' })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="shared">Shared Storage</SelectItem>
              <SelectItem value="dedicated">Dedicated Storage</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        {formData.storageType && (
          <div className="p-4 bg-muted/30 rounded-lg">
            <div className="text-sm">
              <div className="font-medium mb-2">
                {formData.storageType === 'shared' ? 'Shared Storage' : 'Dedicated Storage'}
              </div>
              <div className="text-muted-foreground">
                {formData.storageType === 'shared' 
                  ? 'Uses existing shared Azure Data Lake Storage within the selected account. Cost-effective for development and testing environments.'
                  : 'Creates dedicated Azure Data Lake Storage account within the selected Azure account. Recommended for production workloads requiring isolation.'
                }
              </div>
              <div className="mt-2 text-xs text-muted-foreground">
                <strong>Backend handles:</strong> ADLS account creation, region placement, replication configuration, and access management.
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}