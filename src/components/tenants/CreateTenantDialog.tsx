import { useState } from "react";
import { Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { TenantFormFields } from "./TenantFormFields";
import { Tenant, TenantFormData } from "./types";

interface CreateTenantDialogProps {
  onCreateTenant: (tenant: Tenant) => void;
}

export function CreateTenantDialog({ onCreateTenant }: CreateTenantDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState<TenantFormData>({
    name: "",
    description: "",
    status: "active",
    endpoint: "",
    storageType: "shared"
  });

  const handleCreate = () => {
    const newTenant: Tenant = {
      id: Date.now().toString(),
      name: formData.name || "",
      description: formData.description || "",
      status: formData.status as Tenant['status'],
      endpoint: formData.endpoint || "",
      storageType: formData.storageType as Tenant['storageType'],
      createdAt: new Date().toISOString().split('T')[0],
      lastActivity: new Date().toISOString().split('T')[0],
      projectCount: 0,
      dataSize: "0 GB"
    };

    onCreateTenant(newTenant);
    resetForm();
    setIsOpen(false);
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      status: "active",
      endpoint: "",
      storageType: "shared"
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Tenant
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Create New Tenant</DialogTitle>
          <DialogDescription>
            Set up a new tenant environment for satellite image processing
          </DialogDescription>
        </DialogHeader>
        <TenantFormFields
          formData={formData}
          onFormDataChange={setFormData}
        />
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleCreate} disabled={!formData.name || !formData.storageType}>
            Create Tenant
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}