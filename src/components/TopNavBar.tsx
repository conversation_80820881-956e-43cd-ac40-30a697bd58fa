
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, LogOut } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ThemeToggle } from "@/components/theme/ThemeToggle";

interface TopNavBarProps {
  userRole: 'global_admin' | 'org_admin' | 'project_admin' | 'contributor' | 'reader';
  userName: string;
  onLogout: () => void;
}

const getRoleDisplayName = (role: TopNavBarProps['userRole']): string => {
  switch (role) {
    case 'global_admin':
      return 'Global Administrator';
    case 'org_admin':
      return 'Organization Administrator';
    case 'project_admin':
      return 'Project Administrator';
    case 'contributor':
      return 'Contributor';
    case 'reader':
      return 'Reader';
    default:
      return 'User';
  }
};

export function TopNavBar({ userRole, userName, onLogout }: TopNavBarProps) {
  return (
    <div className="h-16 bg-secondary/30 border-b border-border flex items-center justify-between px-6 shadow-sm">
      {/* Logo Section */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <div className="w-24 h-24 flex items-center justify-center">
            <img src="/logo.png" alt="Versar Logo" className="w-24 h-24 object-contain" />
          </div>
          <div>
            <h1 className="text-xl font-semibold text-blue-700 tracking-tight">Geospatial and Digital Solutions</h1>
          </div>
        </div>
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-4">
        <ThemeToggle />
        <Button variant="ghost" size="sm">
          <Bell className="h-4 w-4" />
        </Button>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src="" />
                <AvatarFallback>{userName.split(' ').map(n => n[0]).join('')}</AvatarFallback>
              </Avatar>
              <div className="text-left">
                <p className="text-sm font-medium">{userName}</p>
                <p className="text-xs text-muted-foreground capitalize">{getRoleDisplayName(userRole)}</p>
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <User className="mr-2 h-4 w-4" />
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={onLogout}>
              <LogOut className="mr-2 h-4 w-4" />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
