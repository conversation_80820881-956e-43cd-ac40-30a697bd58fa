
import { useState } from "react";
import { Search, Download, Eye, Calendar, DollarSign, TrendingUp, Building2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export function RootBilling() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedPeriod, setSelectedPeriod] = useState("current");

  const mockOrganizationUsage = [
    {
      id: 1,
      name: "Versar Inc",
      tenants: 15,
      actualCost: 1680.00,
      currentBill: 0.00,
      previousBill: 0.00,
      status: "System Default",
      billingPeriod: "Dec 2024",
      perTenantCost: 112.00,
      isInternal: true
    },
    {
      id: 2,
      name: "Acme Corporation",
      tenants: 12,
      actualCost: 2840.50,
      currentBill: 2840.50,
      previousBill: 2650.00,
      status: "Paid",
      billingPeriod: "Dec 2024",
      perTenantCost: 236.71,
      isInternal: false
    }
  ];

  const totalRevenue = mockOrganizationUsage.reduce((sum, org) => sum + org.currentBill, 0);
  const totalCosts = mockOrganizationUsage.reduce((sum, org) => sum + org.actualCost, 0);
  const totalTenants = mockOrganizationUsage.reduce((sum, org) => sum + org.tenants, 0);
  const averagePerTenant = totalRevenue / totalTenants;
  const averageCostPerTenant = totalCosts / totalTenants;

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold dashboard-title">Billing Management</h2>
          <p className="text-gray-600">Monitor revenue and billing across all organizations</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            December 2024
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Revenue Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Billable Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-green-600 font-medium">+8.2% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Costs</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalCosts.toLocaleString()}</div>
            <p className="text-xs text-blue-600 font-medium">Includes internal</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTenants}</div>
            <p className="text-xs text-green-600 font-medium">+3 new this month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Cost/Tenant</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${averageCostPerTenant.toFixed(0)}</div>
            <p className="text-xs text-blue-600 font-medium">All tenants</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outstanding</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$0</div>
            <p className="text-xs text-green-600 font-medium">All invoices current</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search organizations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Billing Table */}
      <Card>
        <CardHeader>
          <CardTitle>Organization Billing</CardTitle>
          <CardDescription>Current billing period usage and costs</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Organization</TableHead>
                <TableHead>Tenants</TableHead>
                <TableHead>Actual Cost</TableHead>
                <TableHead>Billable Amount</TableHead>
                <TableHead>Per Tenant</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockOrganizationUsage.map((org) => (
                <TableRow key={org.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{org.name}</div>
                      <div className="text-sm text-gray-500">{org.billingPeriod}</div>
                    </div>
                  </TableCell>
                  <TableCell>{org.tenants}</TableCell>
                  <TableCell className="font-medium">${org.actualCost.toLocaleString()}</TableCell>
                  <TableCell className="font-medium">
                    {org.isInternal ? (
                      <div>
                        <div className="text-sm text-gray-500">$0.00</div>
                        <div className="text-xs text-blue-600">System Default - Not Billed</div>
                      </div>
                    ) : (
                      `$${org.currentBill.toLocaleString()}`
                    )}
                  </TableCell>
                  <TableCell>${org.perTenantCost.toFixed(0)}</TableCell>
                  <TableCell>
                    <Badge variant={
                      org.status === 'Paid' ? 'default' : 
                      org.status === 'Pending' ? 'secondary' :
                      org.status === 'System Default' ? 'outline' : 'destructive'
                    }>
                      {org.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
