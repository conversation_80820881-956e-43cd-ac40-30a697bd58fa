import { useState } from "react";
import { Plus, User, Edit, Trash2, <PERSON>, User<PERSON>heck } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { <PERSON>, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface OrgUser {
  id: string;
  name: string;
  email: string;
  phone?: string;
  status: 'active' | 'inactive' | 'pending';
  createdAt: string;
  lastLogin?: string;
  projectCount: number;
  organizationRoles: {
    organizationId: string;
    role: 'org_admin' | 'project_admin' | 'contributor' | 'reader';
    permissions: string[];
  }[];
  projectRoles: {
    projectId: string;
    role: 'project_admin' | 'contributor' | 'reader';
    permissions: string[];
  }[];
}

interface UsersManagementProps {
  userRole?: 'global_admin' | 'org_admin' | 'project_admin' | 'contributor' | 'reader';
}

export function UsersManagement({ userRole = 'org_admin' }: UsersManagementProps) {
  const [users, setUsers] = useState<OrgUser[]>([
    {
      id: "1",
      name: "John Smith",
      email: "<EMAIL>",
      status: "active",
      createdAt: "2024-01-15",
      lastLogin: "2024-06-18",
      projectCount: 8,
      organizationRoles: [
        {
          organizationId: "1",
          role: "org_admin",
          permissions: ["create_projects", "manage_users", "manage_tenants", "view_billing"]
        }
      ],
      projectRoles: []
    },
    {
      id: "2",
      name: "Mike Wilson",
      email: "<EMAIL>",
      status: "active",
      createdAt: "2024-02-01",
      lastLogin: "2024-06-17",
      projectCount: 3,
      organizationRoles: [
        {
          organizationId: "1",
          role: "project_admin",
          permissions: ["create_projects", "manage_project_users", "create_tenants"]
        }
      ],
      projectRoles: [
        {
          projectId: "1",
          role: "project_admin",
          permissions: ["manage_project_users", "create_tenants"]
        }
      ]
    },
    {
      id: "3",
      name: "Sarah Davis",
      email: "<EMAIL>",
      status: "active",
      createdAt: "2024-02-15",
      lastLogin: "2024-06-16",
      projectCount: 2,
      organizationRoles: [],
      projectRoles: [
        {
          projectId: "1",
          role: "contributor",
          permissions: ["upload_images", "trigger_jobs", "view_results"]
        },
        {
          projectId: "2",
          role: "contributor",
          permissions: ["upload_images", "trigger_jobs", "view_results"]
        }
      ]
    },
    {
      id: "4",
      name: "Tom Reader",
      email: "<EMAIL>",
      status: "active",
      createdAt: "2024-03-01",
      lastLogin: "2024-06-15",
      projectCount: 1,
      organizationRoles: [],
      projectRoles: [
        {
          projectId: "1",
          role: "reader",
          permissions: ["view_images", "view_results", "download_data"]
        }
      ]
    },
    {
      id: "5",
      name: "Lisa Chen",
      email: "<EMAIL>",
      status: "active",
      createdAt: "2024-03-15",
      lastLogin: "2024-06-14",
      projectCount: 0,
      organizationRoles: [],
      projectRoles: []
    },
    {
      id: "6",
      name: "Mike Davis",
      email: "<EMAIL>",
      status: "pending",
      createdAt: "2024-06-15",
      projectCount: 0,
      organizationRoles: [],
      projectRoles: []
    }
  ]);

  const [selectedUser, setSelectedUser] = useState<OrgUser | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const [formData, setFormData] = useState<Partial<OrgUser>>({
    name: "",
    email: ""
  });

  const getRolePermissions = (role: string): string[] => {
    switch (role) {
      case 'org_admin':
        return ["create_projects", "manage_users", "manage_tenants", "view_billing"];
      case 'project_admin':
        return ["create_projects", "manage_project_users", "create_tenants"];
      case 'contributor':
        return ["upload_images", "trigger_jobs", "view_results"];
      case 'reader':
        return ["view_images", "view_results", "download_data"];
      default:
        return ["view_images"];
    }
  };

  const handleCreate = () => {
    const newUser: OrgUser = {
      id: Date.now().toString(),
      name: formData.name || "",
      email: formData.email || "",
      phone: formData.phone,
      status: "active" as OrgUser['status'],
      createdAt: new Date().toISOString().split('T')[0],
      projectCount: 0,
      organizationRoles: [],
      projectRoles: []
    };

    setUsers([...users, newUser]);
    resetForm();
    setIsCreateDialogOpen(false);
  };

  const handleEdit = () => {
    if (!selectedUser) return;

    const updatedUsers = users.map(user =>
      user.id === selectedUser.id
        ? { ...user, ...formData }
        : user
    );

    setUsers(updatedUsers);
    setIsEditDialogOpen(false);
    setSelectedUser(null);
    resetForm();
  };

  const handleDelete = (userId: string) => {
    setUsers(users.filter(user => user.id !== userId));
  };

  const resetForm = () => {
    setFormData({
      name: "",
      email: ""
    });
  };

  const openEditDialog = (user: OrgUser) => {
    setSelectedUser(user);
    setFormData(user);
    setIsEditDialogOpen(true);
  };

  const openViewDialog = (user: OrgUser) => {
    setSelectedUser(user);
    setIsViewDialogOpen(true);
  };

  const getStatusBadge = (status: OrgUser['status']) => {
    const variants: Record<OrgUser['status'], 'default' | 'destructive' | 'secondary'> = {
      active: 'default',
      inactive: 'secondary',
      pending: 'destructive'
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const getRoleBadge = (role: 'admin' | 'member') => {
    return (
      <Badge variant={role === 'admin' ? 'default' : 'secondary'}>
        {role}
      </Badge>
    );
  };

  const getRolesBadges = (user: OrgUser) => {
    const badges = [];
    
    // Ensure user has the required properties
    const organizationRoles = user.organizationRoles || [];
    const projectRoles = user.projectRoles || [];
    
    // Organization roles
    organizationRoles.forEach(orgRole => {
      const colors: Record<string, string> = {
        org_admin: 'bg-red-100 text-red-800',
        project_admin: 'bg-blue-100 text-blue-800', 
        contributor: 'bg-green-100 text-green-800',
        reader: 'bg-gray-100 text-gray-800'
      };
      const labels: Record<string, string> = {
        org_admin: 'Org Admin',
        project_admin: 'Project Admin',
        contributor: 'Contributor',
        reader: 'Reader'
      };
      badges.push(
        <Badge key={`org-${orgRole.organizationId}`} className={colors[orgRole.role]}>
          {labels[orgRole.role]} (Org)
        </Badge>
      );
    });
    
    // Project roles count
    if (projectRoles.length > 0) {
      badges.push(
        <Badge key="project-roles" variant="outline" className="bg-purple-50 text-purple-700">
          {projectRoles.length} Project Role{projectRoles.length !== 1 ? 's' : ''}
        </Badge>
      );
    }
    
    // No roles assigned
    if (organizationRoles.length === 0 && projectRoles.length === 0) {
      badges.push(
        <Badge key="no-roles" variant="secondary" className="bg-yellow-50 text-yellow-700">
          No Roles Assigned
        </Badge>
      );
    }
    
    return badges;
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Organization Users</CardTitle>
              <CardDescription>
                Manage users and their roles within your organization
              </CardDescription>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add User
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Add New User</DialogTitle>
              <DialogDescription>
                Create a new user account for your organization
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter full name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  placeholder="Enter email address"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreate}>Add User</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Filters */}
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <Input
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-sm"
                />
              </div>
            </div>
            
            <div className="text-sm text-muted-foreground">
              {filteredUsers.length} user{filteredUsers.length !== 1 ? 's' : ''} found
            </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <User className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {user.lastLogin ? (
                      <div className="text-sm text-gray-600">{user.lastLogin}</div>
                    ) : (
                      <span className="text-sm text-gray-500">Never</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openViewDialog(user)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditDialog(user)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete User</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete "{user.name}"? This will remove them from all projects and cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDelete(user.id)}>
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information and role
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Full Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-email">Email Address</Label>
              <Input
                id="edit-email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEdit}>Update User</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
            <DialogDescription>
              Complete user information
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                  <User className="h-8 w-8 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">{selectedUser.name}</h3>
                  <p className="text-gray-600">{selectedUser.email}</p>
                  {selectedUser.phone && (
                    <p className="text-sm text-gray-500">{selectedUser.phone}</p>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm font-medium">Roles:</span>
                  <div className="mt-1 flex flex-wrap gap-1">{getRolesBadges(selectedUser)}</div>
                </div>
                <div>
                  <span className="text-sm font-medium">Status:</span>
                  <div className="mt-1">{getStatusBadge(selectedUser.status)}</div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm font-medium">Projects:</span>
                  <p className="text-sm text-gray-600">{selectedUser.projectCount}</p>
                </div>
                <div>
                  <span className="text-sm font-medium">Created:</span>
                  <p className="text-sm text-gray-600">{selectedUser.createdAt}</p>
                </div>
              </div>
              
              <div>
                <span className="text-sm font-medium">Last Login:</span>
                <p className="text-sm text-gray-600">
                  {selectedUser.lastLogin || 'Never logged in'}
                </p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}