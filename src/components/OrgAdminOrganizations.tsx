import { useState } from "react";
import { Building2, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface Organization {
  id: string;
  name: string;
  description: string;
  type: 'commercial' | 'government';
  configTemplate: 'basic' | 'advanced' | 'europe_advanced';
  adminUserEmail: string;
  createdAt: string;
  jobLibraries: string[];
}

export function OrgAdminOrganizations() {
  // Mock data - in real app, this would show only orgs where current user is admin
  const [organizations] = useState<Organization[]>([
    {
      id: "2",
      name: "Acme Corporation",
      description: "Leading manufacturing company - Customer organization",
      type: "commercial",
      configTemplate: "basic",
      adminUserEmail: "<EMAIL>",
      createdAt: "2024-01-15",
      jobLibraries: ["data-processing", "reporting"]
    }
  ]);

  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>My Organizations</CardTitle>
              <CardDescription>
                {organizations.length} organization{organizations.length !== 1 ? 's' : ''} where you are administrator
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Administrator</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-center">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {organizations.map((org) => (
                <TableRow key={org.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Building2 className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium">{org.name}</div>
                        <div className="text-sm text-muted-foreground">{org.description}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={org.type === 'government' ? 'default' : 'secondary'}>
                      {org.type === 'commercial' ? 'Commercial' : 'Government'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{org.adminUserEmail}</div>
                  </TableCell>
                  <TableCell>{org.createdAt}</TableCell>
                  <TableCell className="text-center">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedOrg(org);
                              setIsViewDialogOpen(true);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>View Details</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Organization Details</DialogTitle>
            <DialogDescription>
              View complete organization information
            </DialogDescription>
          </DialogHeader>
          {selectedOrg && (
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-sm text-gray-700 mb-2">Basic Information</h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium">Name:</span>
                    <p className="text-sm text-gray-600">{selectedOrg.name}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Description:</span>
                    <p className="text-sm text-gray-600">{selectedOrg.description}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Type:</span>
                    <div className="mt-1">
                      <Badge variant={selectedOrg.type === 'government' ? 'default' : 'secondary'}>
                        {selectedOrg.type === 'commercial' ? 'Commercial' : 'Government'}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Configuration Template:</span>
                    <div className="mt-1">
                      <Badge variant="outline">
                        {selectedOrg.configTemplate === 'basic' ? 'Basic' : 
                         selectedOrg.configTemplate === 'advanced' ? 'Advanced' : 'Europe Advanced'}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Created:</span>
                    <p className="text-sm text-gray-600">{selectedOrg.createdAt}</p>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold text-sm text-gray-700 mb-2">Administrator</h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium">Email:</span>
                    <p className="text-sm text-gray-600">{selectedOrg.adminUserEmail}</p>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold text-sm text-gray-700 mb-2">Assignments</h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium">Job Libraries:</span>
                    <p className="text-sm text-gray-600">
                      {selectedOrg.jobLibraries.length > 0 ? selectedOrg.jobLibraries.join(', ') : 'None assigned'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}