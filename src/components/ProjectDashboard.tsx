import { useState, useEffect } from "react";
import { Search, Settings, AlertTriangle, Users, Database, HardDrive, Download, Plus, Share2, FileText, Calendar, X, Check, Loader2, ChevronRight, Activity, Clock, Image, Zap, Lock, Globe, FolderOpen, Trash2, FileType, Eye, Edit, CheckCircle, UserPlus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Checkbox } from "@/components/ui/checkbox";

interface Project {
  id: string;
  name: string;
  client: string;
  description: string;
  status: 'active' | 'idle' | 'processing' | 'completed' | 'error';
  currentActivity: string;
  hasAlerts: boolean;
  isProcessing: boolean;
  imageCount: number;
  totalSize: string;
  metadataSize: string;
  metadataCount: number;
  organizationId: string;
  organizationName: string;
  tenantId: string;
  tenantName: string;
  type: 'private' | 'shared';
  clientType: 'commercial' | 'government';
  sharedOrganizations: string[];
  datasetCount: number;
  createdAt: string;
  lastUpdated: string;
  collaborators: Collaborator[];
  tasks: Task[];
  jobExecutions: JobExecution[];
  logs: LogEntry[];
  datasets: Dataset[];
  assignedUsers: ProjectUser[];
}

interface Collaborator {
  id: string;
  name: string;
  email: string;
  role: string;
  organization: string;
}

interface Task {
  id: string;
  name: string;
  service: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  type: 'event' | 'scheduled';
  progress: number;
  startTime?: string;
  endTime?: string;
  logs: string[];
  schedule?: string; // For scheduled jobs
  trigger?: string; // For event-triggered jobs
  createdAt?: string;
}

interface JobExecution {
  id: string;
  jobId: string; // Reference to the Task/Job definition
  jobName: string;
  service: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  startTime: string;
  endTime?: string;
  duration?: string;
  triggeredBy: 'manual' | 'scheduled' | 'event' | 'import';
  logs: string[];
  outputDataset?: string;
}

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'success';
  message: string;
  details?: string;
}

interface Service {
  id: string;
  name: string;
  description: string;
  defaultArgs: string;
}

interface Dataset {
  id: string;
  name: string;
  description: string;
  tag: string;
  lastUpdated: string;
  images: number;
  size: string;
}

interface Tenant {
  id: string;
  name: string;
  description: string;
  storageType: 'shared' | 'dedicated';
}

interface ProjectUser {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  permissions: string[];
}

interface Organization {
  id: string;
  name: string;
  description: string;
  configTemplate: 'basic-commercial' | 'standard-commercial' | 'premium-commercial' | 'basic-government' | 'standard-government' | 'premium-government';
}

interface ProjectDashboardProps {
  userRole: 'global_admin' | 'org_admin' | 'project_admin' | 'contributor' | 'reader';
}

const mockProjects: Project[] = [
  {
    id: "1",
    name: "Agricultural Monitoring",
    client: "AgriTech Solutions",
    description: "Advanced spectral analysis of crop health using multispectral satellite imagery to optimize irrigation, detect disease patterns, and predict yield across 75,000 acres of precision agriculture farmland",
    status: "processing",
    currentActivity: "Processing NDVI analysis for winter wheat fields",
    hasAlerts: false,
    isProcessing: true,
    imageCount: 24500,
    totalSize: "1.2 TB",
    metadataSize: "156 MB",
    metadataCount: 24500,
    organizationId: "1",
    organizationName: "GreenField Analytics",
    tenantId: "1",
    tenantName: "Agricultural Data Hub",
    type: "private",
    clientType: "commercial",
    sharedOrganizations: [],
    datasetCount: 18,
    createdAt: "2024-01-15",
    lastUpdated: "2024-07-09",
    collaborators: [
      { id: "1", name: "Dr. Sarah Mitchell", email: "<EMAIL>", role: "project_admin", organization: "GreenField Analytics" },
      { id: "2", name: "James Rodriguez", email: "<EMAIL>", role: "contributor", organization: "AgriTech Solutions" }
    ],
    tasks: [
      {
        id: "1",
        name: "NDVI Calculation",
        service: "Spectral Analysis",
        status: "running",
        type: "event",
        progress: 72,
        startTime: "2024-07-09T08:15:00Z",
        logs: ["NDVI processing initiated for 450 Sentinel-2 tiles", "Atmospheric correction completed"],
        createdAt: "2024-07-05T10:00:00Z"
      }
    ],
    jobExecutions: [
      {
        id: "exec_1",
        jobId: "1",
        jobName: "NDVI Calculation",
        service: "Spectral Analysis",
        status: "running",
        progress: 72,
        startTime: "2024-07-09T08:15:00Z",
        triggeredBy: "manual",
        logs: ["NDVI processing initiated for 450 Sentinel-2 tiles", "Atmospheric correction completed", "Processing 72% complete"],
        outputDataset: "ndvi_results_20240709"
      }
    ],
    logs: [
      {
        id: "1",
        timestamp: "2024-07-09T08:15:20Z",
        level: "info",
        message: "NDVI processing initiated for 450 Sentinel-2 tiles",
        details: "Processing multispectral bands: Red (665nm), NIR (842nm)"
      },
      {
        id: "2",
        timestamp: "2024-07-09T08:45:12Z",
        level: "info",
        message: "Atmospheric correction completed",
        details: "BRDF correction applied to 72% of tiles"
      },
      {
        id: "3",
        timestamp: "2024-07-09T09:20:35Z",
        level: "success",
        message: "Crop health index calculated",
        details: "NDVI values: Healthy 0.6-0.8, Moderate 0.4-0.6, Stressed <0.4"
      }
    ],
    datasets: [
      {
        id: "1",
        name: "Sentinel-2 Raw",
        description: "Raw multispectral imagery from Sentinel-2 constellation",
        tag: "Raw",
        lastUpdated: "2024-07-09",
        images: 24500,
        size: "1.2 TB"
      },
      {
        id: "2",
        name: "NDVI Processed",
        description: "Normalized Difference Vegetation Index analysis",
        tag: "Processed",
        lastUpdated: "2024-07-09",
        images: 18200,
        size: "890 GB"
      },
      {
        id: "3",
        name: "Crop Health Maps",
        description: "Final crop health classification maps",
        tag: "Released",
        lastUpdated: "2024-07-08",
        images: 15600,
        size: "620 GB"
      }
    ],
    assignedUsers: [
      { id: "1", name: "John Smith", email: "<EMAIL>", role: "admin", permissions: ["read", "write", "delete", "manage_users"] },
      { id: "2", name: "Sarah Wilson", email: "<EMAIL>", role: "editor", permissions: ["read", "write"] }
    ]
  },
  {
    id: "2",
    name: "Urban Planning Analysis",
    client: "Metropolitan Planning Council",
    description: "High-resolution satellite imagery analysis for urban expansion monitoring, land use classification, and infrastructure development planning across the Greater Metropolitan Area",
    status: "active",
    currentActivity: "Analyzing building footprint changes in downtown district",
    hasAlerts: true,
    isProcessing: false,
    imageCount: 12800,
    totalSize: "640 GB",
    metadataSize: "89 MB",
    metadataCount: 12800,
    organizationId: "2",
    organizationName: "CityScope Technologies",
    tenantId: "2",
    tenantName: "Urban Analytics Storage",
    type: "shared",
    clientType: "government",
    sharedOrganizations: ["1"],
    datasetCount: 12,
    createdAt: "2024-02-01",
    lastUpdated: "2024-07-08",
    collaborators: [
      { id: "3", name: "Michael Chen", email: "<EMAIL>", role: "project_admin", organization: "CityScope Technologies" },
      { id: "4", name: "Elena Vasquez", email: "<EMAIL>", role: "contributor", organization: "Metropolitan Planning Council" }
    ],
    tasks: [
      {
        id: "2",
        name: "Building Classification",
        service: "Change Detection",
        status: "completed",
        type: "scheduled",
        progress: 100,
        startTime: "2024-07-08T10:00:00Z",
        endTime: "2024-07-08T14:20:00Z",
        logs: ["Building footprint extraction completed", "Land use classification updated"]
      }
    ],
    logs: [
      {
        id: "3",
        timestamp: "2024-07-08T14:20:15Z",
        level: "success",
        message: "Building footprint extraction completed",
        details: "Detected 15,847 building structures with 94.2% accuracy"
      },
      {
        id: "4",
        timestamp: "2024-07-08T14:25:42Z",
        level: "warning",
        message: "Rapid urban expansion detected",
        details: "18% increase in built-up area compared to 2023 baseline"
      },
      {
        id: "5",
        timestamp: "2024-07-08T15:10:08Z",
        level: "info",
        message: "Land use classification updated",
        details: "Categories: Residential 45%, Commercial 23%, Industrial 12%, Green Space 20%"
      }
    ],
    datasets: [
      {
        id: "1",
        name: "WorldView-3 Baseline",
        description: "High-resolution urban imagery baseline dataset",
        tag: "Baseline",
        lastUpdated: "2024-07-08",
        images: 12800,
        size: "640 GB"
      },
      {
        id: "2",
        name: "Building Footprints",
        description: "Extracted building polygons and classifications",
        tag: "Vector",
        lastUpdated: "2024-07-08",
        images: 8400,
        size: "285 GB"
      },
      {
        id: "3",
        name: "Land Use Maps",
        description: "Comprehensive land use classification results",
        tag: "Released",
        lastUpdated: "2024-07-07",
        images: 6200,
        size: "320 GB"
      }
    ],
    assignedUsers: [
      { id: "3", name: "Mike Johnson", email: "<EMAIL>", role: "admin", permissions: ["read", "write", "delete", "manage_users"] },
      { id: "4", name: "Emily Davis", email: "<EMAIL>", role: "viewer", permissions: ["read"] }
    ],
    jobExecutions: []
  },
  {
    id: "3",
    name: "Forest Change Detection",
    client: "Environmental Conservation Institute",
    description: "Multi-temporal analysis of forest cover changes using Landsat and Sentinel imagery to monitor deforestation, reforestation patterns, and biodiversity impacts across protected forest reserves",
    status: "idle",
    currentActivity: "Awaiting Q3 Landsat acquisitions for temporal analysis",
    hasAlerts: false,
    isProcessing: false,
    imageCount: 18600,
    totalSize: "950 GB",
    metadataSize: "124 MB",
    metadataCount: 18600,
    organizationId: "3",
    organizationName: "ForestWatch Global",
    tenantId: "3",
    tenantName: "Environmental Data Storage",
    type: "shared",
    clientType: "commercial",
    sharedOrganizations: ["1", "2"],
    datasetCount: 25,
    createdAt: "2024-03-01",
    lastUpdated: "2024-07-05",
    collaborators: [
      { id: "5", name: "Dr. Rachel Green", email: "<EMAIL>", role: "project_admin", organization: "ForestWatch Global" },
      { id: "6", name: "Carlos Santos", email: "<EMAIL>", role: "contributor", organization: "Environmental Conservation Institute" }
    ],
    tasks: [
      {
        id: "3",
        name: "Deforestation Alert",
        service: "Change Detection",
        status: "completed",
        type: "event",
        progress: 100,
        startTime: "2024-07-05T06:00:00Z",
        endTime: "2024-07-05T11:45:00Z",
        logs: ["Forest loss detection completed", "Biodiversity impact assessment generated"]
      }
    ],
    logs: [
      {
        id: "5",
        timestamp: "2024-07-05T11:45:30Z",
        level: "success",
        message: "Forest loss detection completed",
        details: "Analyzed 2,500 km² detecting 145 ha of forest loss since June"
      },
      {
        id: "6",
        timestamp: "2024-07-05T12:15:20Z",
        level: "info",
        message: "Biodiversity impact assessment generated",
        details: "Affected habitats: Primary forest 65%, Secondary forest 35%"
      },
      {
        id: "7",
        timestamp: "2024-07-05T12:30:15Z",
        level: "info",
        message: "Carbon stock estimates updated",
        details: "Estimated carbon loss: 2,840 tons CO2 equivalent"
      }
    ],
    datasets: [
      {
        id: "1",
        name: "Landsat Time Series",
        description: "30-year Landsat archive for forest monitoring",
        tag: "Archive",
        lastUpdated: "2024-07-05",
        images: 18600,
        size: "950 GB"
      },
      {
        id: "2",
        name: "Forest Loss Maps",
        description: "Annual forest change detection results",
        tag: "Annual",
        lastUpdated: "2024-07-05",
        images: 12400,
        size: "480 GB"
      },
      {
        id: "3",
        name: "Biodiversity Impact",
        description: "Species habitat impact assessment maps",
        tag: "Analysis",
        lastUpdated: "2024-07-04",
        images: 8200,
        size: "340 GB"
      }
    ],
    assignedUsers: [
      { id: "5", name: "David Brown", email: "<EMAIL>", role: "editor", permissions: ["read", "write"] },
      { id: "6", name: "Lisa Chen", email: "<EMAIL>", role: "editor", permissions: ["read", "write"] }
    ],
    jobExecutions: []
  }
];

const predefinedServices: Service[] = [
  {
    id: "1",
    name: "Add Copyright Service",
    description: "Adds copyright watermark to processed images",
    defaultArgs: `{
  "watermark": {
    "text": "© 2024 Versa Portal",
    "position": "bottom-right",
    "opacity": 0.7,
    "fontSize": 12
  },
  "outputFormat": "PNG",
  "quality": 95
}`
  },
  {
    id: "2",
    name: "Mask Waterbodies",
    description: "Identifies and masks water bodies in satellite imagery",
    defaultArgs: `{
  "threshold": 0.8,
  "minWaterBodySize": 100,
  "algorithm": "NDWI",
  "outputMask": true,
  "categories": ["rivers", "lakes", "ponds"]
}`
  },
  {
    id: "3",
    name: "Spectral Analysis",
    description: "Performs spectral analysis on multispectral imagery",
    defaultArgs: `{
  "bands": ["red", "green", "blue", "nir"],
  "indices": ["NDVI", "NDWI", "EVI"],
  "outputFormat": "GeoTIFF",
  "resolution": "10m"
}`
  },
  {
    id: "4",
    name: "Change Detection",
    description: "Detects changes between temporal imagery sets",
    defaultArgs: `{
  "algorithm": "pixel_difference",
  "threshold": 0.3,
  "minChangeArea": 50,
  "outputFormat": "vector",
  "classes": ["vegetation", "urban", "water"]
}`
  }
];

// Helper function to parse CRON expressions into human-readable descriptions
const parseCronExpression = (cron: string): string => {
  try {
    const parts = cron.trim().split(/\s+/);
    if (parts.length !== 5) {
      return 'Invalid CRON expression (should have 5 parts)';
    }
    
    const [minute, hour, dayOfMonth, month, dayOfWeek] = parts;
    
    // Common patterns
    if (cron === '0 */1 * * *') return 'Every hour';
    if (cron === '0 0 * * *') return 'Daily at midnight';
    if (cron === '0 0 * * 0') return 'Weekly on Sunday at midnight';
    if (cron === '0 0 * * 1') return 'Weekly on Monday at midnight';
    if (cron === '0 0 1 * *') return 'Monthly on the 1st at midnight';
    if (cron === '0 0 1 1 *') return 'Yearly on January 1st at midnight';
    
    // Build description
    let description = 'At ';
    
    // Time
    if (hour === '*' && minute === '*') {
      description += 'every minute';
    } else if (hour === '*') {
      description += minute === '0' ? 'the top of every hour' : `${minute} minutes past every hour`;
    } else {
      const hourNum = parseInt(hour);
      const minNum = parseInt(minute);
      const time = `${hourNum.toString().padStart(2, '0')}:${minNum.toString().padStart(2, '0')}`;
      description += time;
    }
    
    // Day constraints
    if (dayOfWeek !== '*') {
      const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const dayNum = parseInt(dayOfWeek);
      if (!isNaN(dayNum) && dayNum >= 0 && dayNum <= 6) {
        description += ` on ${days[dayNum]}`;
      }
    } else if (dayOfMonth !== '*') {
      const dayNum = parseInt(dayOfMonth);
      if (!isNaN(dayNum)) {
        const suffix = dayNum === 1 ? 'st' : dayNum === 2 ? 'nd' : dayNum === 3 ? 'rd' : 'th';
        description += ` on the ${dayNum}${suffix} of each month`;
      }
    }
    
    // Month constraints
    if (month !== '*') {
      const months = ['January', 'February', 'March', 'April', 'May', 'June',
                     'July', 'August', 'September', 'October', 'November', 'December'];
      const monthNum = parseInt(month);
      if (!isNaN(monthNum) && monthNum >= 1 && monthNum <= 12) {
        description += ` in ${months[monthNum - 1]}`;
      }
    }
    
    return description;
  } catch {
    return 'Invalid CRON expression';
  }
};

export function ProjectDashboard({ userRole }: ProjectDashboardProps) {
  // Permission helpers
  const canCreateProject = userRole === 'global_admin' || userRole === 'org_admin' || userRole === 'project_admin';
  const canEditProject = userRole === 'global_admin' || userRole === 'org_admin' || userRole === 'project_admin';
  const canDeleteProject = userRole === 'global_admin' || userRole === 'org_admin';
  const canAssignUsers = userRole === 'global_admin' || userRole === 'org_admin' || userRole === 'project_admin';
  const canViewDetails = true; // All roles can view details
  const canManageProject = userRole === 'global_admin' || userRole === 'org_admin' || userRole === 'project_admin' || userRole === 'contributor';
  const canCreateJobs = userRole === 'global_admin' || userRole === 'org_admin' || userRole === 'project_admin' || userRole === 'contributor';
  const canImportDatasets = userRole === 'global_admin' || userRole === 'org_admin' || userRole === 'project_admin' || userRole === 'contributor';
  const canShareProject = userRole === 'global_admin' || userRole === 'org_admin' || userRole === 'project_admin';
  const isReaderOnly = userRole === 'reader';
  const [projects, setProjects] = useState<Project[]>(mockProjects);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>(mockProjects);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isDetailViewOpen, setIsDetailViewOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isCreateTaskDialogOpen, setIsCreateTaskDialogOpen] = useState(false);
  const [isImportInProgress, setIsImportInProgress] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importUrl, setImportUrl] = useState("");
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [targetDatasetName, setTargetDatasetName] = useState("");
  const [taskArgs, setTaskArgs] = useState("");
  const [isTaskScheduling, setIsTaskScheduling] = useState(false);
  const [executionTrigger, setExecutionTrigger] = useState<'now' | 'event' | 'scheduled'>('now');
  const [selectedEvent, setSelectedEvent] = useState<string>('');
  const [cronExpression, setCronExpression] = useState<string>('0 0 * * 0'); // Weekly by default
  const [cronDescription, setCronDescription] = useState<string>('Weekly on Sunday at midnight');
  const [isCreateProjectDialogOpen, setIsCreateProjectDialogOpen] = useState(false);
  const [isCreatingProject, setIsCreatingProject] = useState(false);
  const [isCreatingNewTenant, setIsCreatingNewTenant] = useState(false);
  
  // Additional dialog states for project management
  const [isEditProjectDialogOpen, setIsEditProjectDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isAssignUsersDialogOpen, setIsAssignUsersDialogOpen] = useState(false);
  const [isShareProjectDialogOpen, setIsShareProjectDialogOpen] = useState(false);
  const [projectToEdit, setProjectToEdit] = useState<Project | null>(null);
  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);
  
  // Project creation status dialog states
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [projectCreationComplete, setProjectCreationComplete] = useState(false);
  const [tenantProvisioningComplete, setTenantProvisioningComplete] = useState(false);
  const [newProjectData, setNewProjectData] = useState<{ project: Project; tenant: Tenant; isNewTenant: boolean } | null>(null);
  
  // Available organizations for global admins
  const [organizations] = useState<Organization[]>([
    { id: "1", name: "ACME Corporation", description: "Leading manufacturing company", configTemplate: "standard-commercial" },
    { id: "2", name: "TechCorp Industries", description: "Technology solutions provider", configTemplate: "basic-commercial" },
    { id: "3", name: "Global Systems Inc", description: "Enterprise software solutions", configTemplate: "premium-government" }
  ]);

  // Available users for assignment
  const [availableUsers] = useState<ProjectUser[]>([
    { id: "1", name: "John Smith", email: "<EMAIL>", role: "admin", permissions: ["read", "write", "delete", "manage_users"] },
    { id: "2", name: "Sarah Wilson", email: "<EMAIL>", role: "editor", permissions: ["read", "write"] },
    { id: "3", name: "Mike Johnson", email: "<EMAIL>", role: "admin", permissions: ["read", "write", "delete", "manage_users"] },
    { id: "4", name: "Emily Davis", email: "<EMAIL>", role: "viewer", permissions: ["read"] },
    { id: "5", name: "David Brown", email: "<EMAIL>", role: "editor", permissions: ["read", "write"] },
    { id: "6", name: "Lisa Chen", email: "<EMAIL>", role: "editor", permissions: ["read", "write"] },
    { id: "7", name: "Tom Wilson", email: "<EMAIL>", role: "admin", permissions: ["read", "write", "delete", "manage_users"] },
    { id: "8", name: "Anna Rodriguez", email: "<EMAIL>", role: "viewer", permissions: ["read"] }
  ]);

  // Available tenants for project creation
  const [tenants] = useState<Tenant[]>([
    {
      id: "1",
      name: "Agricultural Data Hub",
      description: "Storage for crop monitoring and agricultural satellite imagery",
      storageType: "dedicated"
    },
    {
      id: "2",
      name: "General Purpose Storage",
      description: "Shared storage for various projects",
      storageType: "shared"
    },
    {
      id: "3",
      name: "Urban Analytics Storage",
      description: "Dedicated storage for urban development projects",
      storageType: "dedicated"
    }
  ]);
  
  // Form states for project creation
  const [formData, setFormData] = useState<Partial<Project>>({
    name: "",
    description: "",
    client: "",
    tenantId: "",
    clientType: "commercial"
  });
  
  const [newTenantData, setNewTenantData] = useState({
    name: "",
    description: ""
  });
  
  // Dataset editing state
  const [editingDataset, setEditingDataset] = useState<string | null>(null);
  const [editingDatasetData, setEditingDatasetData] = useState<Partial<Dataset>>({});

  // Description expansion state
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);

  // Active tab state for project details
  const [activeTab, setActiveTab] = useState("activity");

  // Filter projects based on search and status
  useEffect(() => {
    let filtered = projects;

    if (searchTerm) {
      filtered = filtered.filter(project => 
        project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.client.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(project => project.status === statusFilter);
    }

    setFilteredProjects(filtered);
  }, [projects, searchTerm, statusFilter]);

  const handleProjectExpand = (project: Project) => {
    setSelectedProject(project);
    setIsDetailViewOpen(true);
  };

  const handleImportImage = async () => {
    if (!importUrl || !selectedProject) return;

    setIsImportInProgress(true);
    setImportProgress(0);

    // Simulate dataset import progress
    const progressInterval = setInterval(() => {
      setImportProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          
          // Create a task for dataset import
          const importTaskId = Date.now().toString();
          const importTask: Task = {
            id: importTaskId,
            name: `Dataset Import from ${new URL(importUrl).hostname}`,
            service: "Dataset Import Service",
            status: 'completed',
            type: 'event',
            progress: 100,
            startTime: new Date().toISOString(),
            endTime: new Date().toISOString(),
            logs: ["Dataset import started", "Dataset download completed", "Dataset saved to storage and indexed"],
            createdAt: new Date().toISOString()
          };

          // Create job execution for the import
          const importExecution: JobExecution = {
            id: `exec_${importTaskId}`,
            jobId: importTaskId,
            jobName: importTask.name,
            service: "Dataset Import Service",
            status: 'completed',
            progress: 100,
            startTime: new Date().toISOString(),
            endTime: new Date().toISOString(),
            duration: "2m 30s",
            triggeredBy: 'manual',
            logs: ["Import started", "Downloading from URL", "Processing completed", "Dataset indexed"],
            outputDataset: `imported_dataset_${Date.now()}`
          };

          // Add activity to project
          const updatedProjects = projects.map(p =>
            p.id === selectedProject.id
              ? {
                  ...p,
                  currentActivity: "Dataset import completed",
                  isProcessing: false,
                  tasks: [...p.tasks, importTask],
                  jobExecutions: [...p.jobExecutions, importExecution],
                  logs: [
                    ...p.logs,
                    {
                      id: Date.now().toString(),
                      timestamp: new Date().toISOString(),
                      level: "info" as const,
                      message: "Dataset import started",
                      details: `Downloading from: ${importUrl}`
                    },
                    {
                      id: (Date.now() + 1).toString(),
                      timestamp: new Date().toISOString(),
                      level: "info" as const,
                      message: "Dataset download completed",
                      details: "Dataset saved to storage and indexed"
                    }
                  ]
                }
              : p
          );
          setProjects(updatedProjects);

          // Update selectedProject to reflect the new task
          const updatedSelectedProject = updatedProjects.find(p => p.id === selectedProject.id);
          if (updatedSelectedProject) {
            setSelectedProject(updatedSelectedProject);
          }

          // Switch to Activity tab to show the import activity
          setActiveTab("activity");

          // Clear activity after 5 seconds
          setTimeout(() => {
            const clearedProjects = updatedProjects.map(p => 
              p.id === selectedProject.id 
                ? { 
                    ...p, 
                    currentActivity: "Ready for processing",
                    isProcessing: false,
                    imageCount: p.imageCount + 1
                  }
                : p
            );
            setProjects(clearedProjects);
          }, 5000);
          
          setIsImportInProgress(false);
          setImportProgress(0);
          setImportUrl("");
          setIsImportDialogOpen(false);
          return 100;
        }
        return prev + 10;
      });
    }, 1000);
  };

  const handleCreateTask = async () => {
    if (!selectedService || !selectedDataset || !selectedProject) return;
    if (executionTrigger === 'event' && !selectedEvent) return;

    setIsTaskScheduling(true);

    // Simulate job scheduling
    setTimeout(() => {
      // Generate target dataset name if not provided
      const outputDatasetName = targetDatasetName || 
        `${selectedDataset.name}_${selectedService.name}_${new Date().toISOString().split('T')[0]}`;

      // Determine task status and type based on execution trigger
      const getTaskStatus = () => {
        if (executionTrigger === 'now') return 'running';
        if (executionTrigger === 'event') return 'pending';
        if (executionTrigger === 'scheduled') return 'pending';
        return 'pending';
      };

      const getTaskType = () => {
        if (executionTrigger === 'now') return 'event';
        if (executionTrigger === 'event') return 'event';
        if (executionTrigger === 'scheduled') return 'scheduled';
        return 'event';
      };

      const getInitialLogs = () => {
        if (executionTrigger === 'now') {
          return ["Infrastructure is selected for job execution", "Job execution in progress"];
        } else if (executionTrigger === 'event') {
          return [`Job configured for event trigger: ${selectedEvent}`, "Waiting for event to occur"];
        } else if (executionTrigger === 'scheduled') {
          return [`Job scheduled with CRON: ${cronExpression}`, `Next execution: ${cronDescription}`];
        }
        return ["Job configured"];
      };

      const taskId = Date.now().toString();
      const newTask: Task = {
        id: taskId,
        name: `${selectedService.name} on ${selectedDataset.name}`,
        service: selectedService.name,
        status: getTaskStatus(),
        type: getTaskType(),
        progress: executionTrigger === 'now' ? 0 : 0,
        startTime: executionTrigger === 'now' ? new Date().toISOString() : undefined,
        logs: getInitialLogs(),
        schedule: executionTrigger === 'scheduled' ? cronExpression : undefined,
        trigger: executionTrigger === 'event' ? selectedEvent : undefined,
        createdAt: new Date().toISOString()
      };

      // Create job execution if running immediately
      const newJobExecution: JobExecution | null = executionTrigger === 'now' ? {
        id: `exec_${taskId}`,
        jobId: taskId,
        jobName: newTask.name,
        service: selectedService.name,
        status: 'running',
        progress: 0,
        startTime: new Date().toISOString(),
        triggeredBy: 'manual',
        logs: ["Job execution started", "Processing in progress"],
        outputDataset: outputDatasetName
      } : null;

      const updatedProjects = projects.map(p => 
        p.id === selectedProject.id 
          ? { 
              ...p, 
              currentActivity: executionTrigger === 'now' 
                ? `${selectedService.name} processing ${selectedDataset.name}`
                : executionTrigger === 'event'
                ? `Job scheduled for event: ${selectedEvent}`
                : `Job scheduled: ${cronDescription}`,
              isProcessing: executionTrigger === 'now',
              tasks: [...p.tasks, newTask],
              jobExecutions: newJobExecution ? [...p.jobExecutions, newJobExecution] : p.jobExecutions,
              logs: [
                ...p.logs,
                {
                  id: Date.now().toString(),
                  timestamp: new Date().toISOString(),
                  level: "info" as const,
                  message: executionTrigger === 'now' 
                    ? "Infrastructure is selected for job execution"
                    : "Job created and configured",
                  details: `Service: ${selectedService.name}, Source Dataset: ${selectedDataset.name}, Target: ${outputDatasetName}, Trigger: ${executionTrigger}`
                },
                {
                  id: (Date.now() + 1).toString(),
                  timestamp: new Date().toISOString(),
                  level: "info" as const,
                  message: executionTrigger === 'now' 
                    ? "Job execution in progress"
                    : executionTrigger === 'event'
                    ? `Waiting for event: ${selectedEvent}`
                    : `Scheduled with CRON: ${cronExpression}`,
                  details: executionTrigger === 'now'
                    ? `Processing ${selectedDataset.images} images from ${selectedDataset.name}`
                    : executionTrigger === 'event'
                    ? "Job will execute automatically when the specified event occurs"
                    : cronDescription
                }
              ]
            }
          : p
      );
      setProjects(updatedProjects);
      
      // Update selectedProject to reflect the new task
      const updatedSelectedProject = updatedProjects.find(p => p.id === selectedProject.id);
      if (updatedSelectedProject) {
        setSelectedProject(updatedSelectedProject);
      }

      // Switch to Activity tab to show the new job
      setActiveTab("activity");

      // Simulate job progress updates (only for immediate execution)
      let progressInterval: NodeJS.Timeout | null = null;
      if (executionTrigger === 'now') {
        progressInterval = setInterval(() => {
        setProjects(prevProjects => {
          const updatedProjects = prevProjects.map(p => 
            p.id === selectedProject.id 
              ? {
                  ...p,
                  tasks: p.tasks.map(task => 
                    task.id === newTask.id 
                      ? {
                          ...task,
                          progress: Math.min(Math.round(task.progress + Math.random() * 15 + 5), 100)
                        }
                      : task
                  )
                }
              : p
          );
          
          // Update selectedProject to reflect progress changes
          const updatedSelectedProject = updatedProjects.find(p => p.id === selectedProject.id);
          if (updatedSelectedProject) {
            setSelectedProject(updatedSelectedProject);
          }
          
          return updatedProjects;
        });
        }, 2000);

        // Complete the job after progress reaches 100%
        setTimeout(() => {
          if (progressInterval) clearInterval(progressInterval);
        setProjects(prevProjects => {
          const completedProjects = prevProjects.map(p => 
            p.id === selectedProject.id 
              ? {
                  ...p,
                  currentActivity: "Ready for processing",
                  isProcessing: false,
                  tasks: p.tasks.map(task => 
                    task.id === newTask.id 
                      ? {
                          ...task,
                          status: "completed" as const,
                          progress: 100,
                          endTime: new Date().toISOString(),
                          logs: [...task.logs, "Job execution completed successfully", `Output dataset '${outputDatasetName}' generated`]
                        }
                      : task
                  ),
                  logs: [
                    ...p.logs,
                    {
                      id: (Date.now() + 2).toString(),
                      timestamp: new Date().toISOString(),
                      level: "success" as const,
                      message: "Job execution completed",
                      details: `${selectedService.name} processing completed. Output: ${outputDatasetName}`
                    }
                  ]
                }
              : p
          );
          
          // Update selectedProject to reflect completion
          const updatedSelectedProject = completedProjects.find(p => p.id === selectedProject.id);
          if (updatedSelectedProject) {
            setSelectedProject(updatedSelectedProject);
          }
          
          return completedProjects;
        });
        }, 12000); // Complete after ~12 seconds
      }
      
      setIsTaskScheduling(false);
      setSelectedService(null);
      setSelectedDataset(null);
      setTargetDatasetName("");
      setTaskArgs("");
      setExecutionTrigger('now');
      setSelectedEvent('');
      setCronExpression('0 0 * * 0');
      setCronDescription('Weekly on Sunday at midnight');
      setIsCreateTaskDialogOpen(false);
    }, 2000);
  };

  const handleCreateProject = async () => {
    setIsCreatingProject(true);
    
    try {
      let selectedTenant = tenants.find(t => t.id === formData.tenantId);
      let isNewTenant = false;
      
      // If creating a new tenant
      if (isCreatingNewTenant && newTenantData.name) {
        const newTenant: Tenant = {
          id: Date.now().toString(),
          name: newTenantData.name,
          description: newTenantData.description,
          storageType: "shared" // Backend will determine actual storage type
        };
        selectedTenant = newTenant;
        isNewTenant = true;
      }

      // Create project with "creating" status initially
      const newProject: Project = {
        id: Date.now().toString(),
        name: formData.name || "",
        description: formData.description || "",
        client: formData.client || "",
        status: "processing", // Using "processing" instead of "creating" since our interface supports it
        currentActivity: "Setting up project infrastructure",
        hasAlerts: false,
        isProcessing: true,
        imageCount: 0,
        totalSize: "0 GB",
        metadataSize: "0 MB",
        metadataCount: 0,
        organizationId: formData.organizationId || "1",
        organizationName: organizations.find(org => org.id === (formData.organizationId || "1"))?.name || "ACME Corporation",
        tenantId: selectedTenant?.id || "",
        tenantName: selectedTenant?.name || "",
        type: "private",
        clientType: formData.clientType || "commercial",
        sharedOrganizations: [],
        datasetCount: 0,
        createdAt: new Date().toISOString().split('T')[0],
        lastUpdated: new Date().toISOString().split('T')[0],
        collaborators: [],
        tasks: [],
        jobExecutions: [],
        datasets: [],
        assignedUsers: [],
        logs: [
          {
            id: "1",
            timestamp: new Date().toISOString(),
            level: "info",
            message: "Project infrastructure setup in progress",
            details: "Initializing Azure ADLSv2 storage and processing resources"
          }
        ]
      };

      setProjects(prev => [...prev, newProject]);
      setFilteredProjects(prev => [...prev, newProject]);
      
      // Store data for status dialog
      setNewProjectData({
        project: newProject,
        tenant: selectedTenant!,
        isNewTenant: isNewTenant
      });
      
      resetCreateForm();
      setIsCreateProjectDialogOpen(false);
      
      // Show status dialog
      setProjectCreationComplete(false);
      setTenantProvisioningComplete(false);
      setIsStatusDialogOpen(true);
      
      // Start with tenant provisioning first
      if (isNewTenant) {
        // Simulate tenant provisioning process (3 seconds)
        setTimeout(() => {
          setTenantProvisioningComplete(true);
          
          // After tenant is complete, start project creation
          setTimeout(() => {
            setProjectCreationComplete(true);
            
            // Update project status to active after both are complete
            setTimeout(() => {
              setProjects(prev => prev.map(p => 
                p.id === newProject.id 
                  ? { 
                      ...p, 
                      status: "active", 
                      currentActivity: "Ready for image processing",
                      isProcessing: false,
                      lastUpdated: new Date().toISOString().split('T')[0],
                      logs: [
                        ...p.logs,
                        {
                          id: "2",
                          timestamp: new Date().toISOString(),
                          level: "success",
                          message: "Project infrastructure ready",
                          details: "Azure resources provisioned and ready for satellite image processing"
                        }
                      ]
                    }
                  : p
              ));
              setFilteredProjects(prev => prev.map(p => 
                p.id === newProject.id 
                  ? { 
                      ...p, 
                      status: "active", 
                      currentActivity: "Ready for image processing",
                      isProcessing: false,
                      lastUpdated: new Date().toISOString().split('T')[0]
                    }
                  : p
              ));
            }, 1000);
          }, 3000);
        }, 3000);
      } else {
        // If using existing tenant, mark tenant as complete immediately then start project creation
        setTenantProvisioningComplete(true);
        
        // Start project creation after tenant is ready
        setTimeout(() => {
          setProjectCreationComplete(true);
          
          // Update project status to active
          setTimeout(() => {
            setProjects(prev => prev.map(p => 
              p.id === newProject.id 
                ? { 
                    ...p, 
                    status: "active", 
                    currentActivity: "Ready for image processing",
                    isProcessing: false,
                    lastUpdated: new Date().toISOString().split('T')[0],
                    logs: [
                      ...p.logs,
                      {
                        id: "2",
                        timestamp: new Date().toISOString(),
                        level: "success",
                        message: "Project infrastructure ready",
                        details: "Azure resources provisioned and ready for satellite image processing"
                      }
                    ]
                  }
                : p
            ));
            setFilteredProjects(prev => prev.map(p => 
              p.id === newProject.id 
                ? { 
                    ...p, 
                    status: "active", 
                    currentActivity: "Ready for image processing",
                    isProcessing: false,
                    lastUpdated: new Date().toISOString().split('T')[0]
                  }
                : p
            ));
          }, 1000);
        }, 3000);
      }
    } catch (error) {
      console.error('Failed to create project:', error);
      // Handle error state if needed
    } finally {
      setIsCreatingProject(false);
    }
  };

  const resetCreateForm = () => {
    setFormData({
      name: "",
      description: "",
      client: "",
      organizationId: "",
      tenantId: "",
      clientType: "commercial"
    });
    setNewTenantData({
      name: "",
      description: ""
    });
    setIsCreatingNewTenant(false);
  };

  const handleEditDataset = (dataset: Dataset) => {
    setEditingDataset(dataset.id);
    setEditingDatasetData({
      description: dataset.description,
      tag: dataset.tag
    });
  };

  const handleSaveDataset = (datasetId: string) => {
    if (selectedProject) {
      const updatedDatasets = selectedProject.datasets.map(dataset => 
        dataset.id === datasetId 
          ? { ...dataset, ...editingDatasetData }
          : dataset
      );
      const updatedProject = { ...selectedProject, datasets: updatedDatasets };
      setProjects(projects.map(p => p.id === selectedProject.id ? updatedProject : p));
      setEditingDataset(null);
      setEditingDatasetData({});
    }
  };

  const handleDeleteDataset = (datasetId: string) => {
    if (selectedProject) {
      const updatedDatasets = selectedProject.datasets.filter(dataset => dataset.id !== datasetId);
      const updatedProject = { ...selectedProject, datasets: updatedDatasets };
      setProjects(projects.map(p => p.id === selectedProject.id ? updatedProject : p));
    }
  };

  const handleCancelEdit = () => {
    setEditingDataset(null);
    setEditingDatasetData({});
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'bg-green-100 text-green-800',
      processing: 'bg-blue-100 text-blue-800',
      idle: 'bg-gray-100 text-gray-800',
      error: 'bg-red-100 text-red-800',
      completed: 'bg-green-100 text-green-800'
    };
    return <Badge className={variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>{status}</Badge>;
  };

  // Project action handlers
  const handleEditProject = (project: Project) => {
    setProjectToEdit(project);
    setFormData({
      name: project.name,
      description: project.description,
      client: project.client,
      organizationId: project.organizationId,
      tenantId: project.tenantId
    });
    setIsEditProjectDialogOpen(true);
  };

  const handleDeleteProject = (project: Project) => {
    setProjectToDelete(project);
    setIsDeleteDialogOpen(true);
  };

  const handleAssignUsers = (projectId: string, users: ProjectUser[]) => {
    setProjects(prev => prev.map(p => 
      p.id === projectId 
        ? { ...p, assignedUsers: users, lastUpdated: new Date().toISOString().split('T')[0] }
        : p
    ));
  };

  const handleShareProject = (projectId: string, organizationIds: string[]) => {
    setProjects(prev => prev.map(p => 
      p.id === projectId 
        ? { 
            ...p, 
            type: organizationIds.length > 0 ? 'shared' : 'private',
            sharedOrganizations: organizationIds,
            lastUpdated: new Date().toISOString().split('T')[0] 
          }
        : p
    ));
  };

  const openAssignUsersDialog = (project: Project) => {
    setSelectedProject(project);
    setIsAssignUsersDialogOpen(true);
  };

  const openShareProjectDialog = (project: Project) => {
    setSelectedProject(project);
    setIsShareProjectDialogOpen(true);
  };

  const confirmDeleteProject = () => {
    if (projectToDelete) {
      setProjects(projects.filter(p => p.id !== projectToDelete.id));
      setFilteredProjects(filteredProjects.filter(p => p.id !== projectToDelete.id));
      setIsDeleteDialogOpen(false);
      setProjectToDelete(null);
    }
  };

  const handleUpdateProject = () => {
    if (projectToEdit && formData.name && formData.client) {
      const updatedProject = {
        ...projectToEdit,
        name: formData.name,
        description: formData.description || "",
        client: formData.client,
        organizationId: formData.organizationId || projectToEdit.organizationId,
        tenantId: formData.tenantId || projectToEdit.tenantId,
        lastUpdated: new Date().toISOString().split('T')[0]
      };
      
      setProjects(projects.map(p => p.id === projectToEdit.id ? updatedProject : p));
      setFilteredProjects(filteredProjects.map(p => p.id === projectToEdit.id ? updatedProject : p));
      setIsEditProjectDialogOpen(false);
      setProjectToEdit(null);
      resetCreateForm();
    }
  };

  return (
    <div className="p-6 space-y-6">
      {!isDetailViewOpen ? (
        // Project Cards Grid View
        <>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Projects</h1>
            </div>
            {canCreateProject && (
              <Dialog open={isCreateProjectDialogOpen} onOpenChange={setIsCreateProjectDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Project
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Create New Project</DialogTitle>
                    <DialogDescription>
                      Set up a new satellite image processing project with dedicated storage
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">Project Name</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData({...formData, name: e.target.value})}
                          placeholder="Enter project name"
                        />
                      </div>
                      <div>
                        <Label htmlFor="client">Client</Label>
                        <Input
                          id="client"
                          value={formData.client}
                          onChange={(e) => setFormData({...formData, client: e.target.value})}
                          placeholder="Enter client name"
                        />
                      </div>
                    </div>
                    
                    {userRole === 'global_admin' && (
                      <div>
                        <Label htmlFor="organization">Organization</Label>
                        <Select value={formData.organizationId} onValueChange={(value) => setFormData({...formData, organizationId: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select organization" />
                          </SelectTrigger>
                          <SelectContent>
                            {organizations.map((org) => (
                              <SelectItem key={org.id} value={org.id}>
                                <div className="flex items-center space-x-2">
                                  <div>
                                    <div className="font-medium">{org.name}</div>
                                    <div className="text-xs text-gray-500">{org.description}</div>
                                  </div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                    
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                        placeholder="Enter project description"
                        rows={3}
                      />
                    </div>

                    {/* Client Type Selection */}
                    <div>
                      <Label htmlFor="clientType">Client Type</Label>
                      <Select
                        value={formData.clientType}
                        onValueChange={(value) => setFormData({...formData, clientType: value as 'commercial' | 'government'})}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select client type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="commercial">Commercial</SelectItem>
                          <SelectItem value="government">Government</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Tenant Selection */}
                    <div className="space-y-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex-1">
                          <Label htmlFor="tenant">Tenant</Label>
                          <Select 
                            value={isCreatingNewTenant ? "new" : formData.tenantId} 
                            onValueChange={(value) => {
                              if (value === "new") {
                                setIsCreatingNewTenant(true);
                                setFormData({...formData, tenantId: ""});
                              } else {
                                setIsCreatingNewTenant(false);
                                setFormData({...formData, tenantId: value});
                              }
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select existing tenant or create new" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="new">
                                <div className="flex items-center space-x-2">
                                  <Plus className="h-4 w-4" />
                                  <span>Create New Tenant</span>
                                </div>
                              </SelectItem>
                              {tenants.map((tenant) => (
                                <SelectItem key={tenant.id} value={tenant.id}>
                                  <div className="flex items-center space-x-2">
                                    <div>
                                      <div className="font-medium">{tenant.name}</div>
                                      <div className="text-xs text-gray-500">{tenant.storageType}</div>
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {/* New Tenant Creation Fields */}
                      {isCreatingNewTenant && (
                        <div className="border rounded-lg p-4 bg-gray-50">
                          <div className="space-y-3">
                            <div className="space-y-2">
                              <Label htmlFor="newTenantName">Tenant Name</Label>
                              <Input
                                id="newTenantName"
                                value={newTenantData.name}
                                onChange={(e) => setNewTenantData({...newTenantData, name: e.target.value})}
                                placeholder="Enter tenant name"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="newTenantDesc">Description</Label>
                              <Textarea
                                id="newTenantDesc"
                                value={newTenantData.description}
                                onChange={(e) => setNewTenantData({...newTenantData, description: e.target.value})}
                                placeholder="Enter tenant description"
                                rows={2}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => {
                      setIsCreateProjectDialogOpen(false);
                      resetCreateForm();
                    }}>
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleCreateProject} 
                      disabled={isCreatingProject || !formData.name || !formData.client || (userRole === 'global_admin' && !formData.organizationId) || (!formData.tenantId && !isCreatingNewTenant) || (isCreatingNewTenant && !newTenantData.name)}
                    >
                      {isCreatingProject ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        'Create Project'
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            )}
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-4">
            <div className="flex-1 max-w-sm">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search projects or clients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="idle">Idle</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="error">Error</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Project Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-6">
            {filteredProjects.map((project) => (
              <Card 
                key={project.id} 
                className="hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => handleProjectExpand(project)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    {/* Project Icon and Info */}
                    <div className="flex items-start space-x-3 flex-1 min-w-0">
                      {/* Project Icon */}
                      <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <FolderOpen className="h-5 w-5 text-blue-600" />
                      </div>
                      
                      {/* Project Info */}
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-lg line-clamp-1">{project.name}</CardTitle>
                        <CardDescription className="text-sm text-gray-600 line-clamp-1">
                          {project.client}
                        </CardDescription>
                      </div>
                    </div>
                    
                    {/* Alerts */}
                    <div className="flex-shrink-0">
                      {project.hasAlerts && (
                        <AlertTriangle className="h-4 w-4 text-amber-500" />
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Current Activity */}
                  <div className="flex items-center space-x-2 ml-12">
                    <Activity className="h-4 w-4 text-blue-500" />
                    <span className="text-sm text-gray-600 line-clamp-1">{project.currentActivity}</span>
                  </div>
                  
                  {/* Project Statistics - Aligned Style */}
                  <div className="space-y-2">
                    {/* Datasets - Aligned with Activity */}
                    <div className="flex items-center space-x-2 ml-12">
                      <Database className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-gray-600">
                        <span className="font-bold text-gray-900">{project.datasetCount || 0}</span> Datasets
                      </span>
                    </div>
                    
                    {/* Images and Storage - Inline */}
                    <div className="flex items-center space-x-6 ml-12">
                      <div className="flex items-center space-x-2">
                        <Image className="h-4 w-4 text-purple-500" />
                        <span className="text-sm text-gray-600">
                          <span className="font-bold text-gray-900">{project.imageCount.toLocaleString()}</span> Images
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <HardDrive className="h-4 w-4 text-blue-500" />
                        <span className="text-sm text-gray-600">
                          <span className="font-bold text-gray-900">{project.totalSize}</span> Storage
                        </span>
                      </div>
                    </div>
                    
                    {/* Metadata Row */}
                    <div className="flex items-center space-x-2 ml-12">
                      <FileType className="h-4 w-4 text-orange-500" />
                      <span className="text-sm text-gray-600">
                        <span className="font-bold text-gray-900">{project.metadataSize}</span> Metadata
                      </span>
                    </div>
                  </div>
                  
                  {/* Status and Type */}
                  <div className="flex items-center space-x-2 ml-12">
                    {getStatusBadge(project.status)}
                    <Badge variant={project.type === 'private' ? 'default' : 'secondary'} className="flex items-center space-x-1">
                      {project.type === 'private' ? (
                        <>
                          <Lock className="h-3 w-3" />
                          <span>Private</span>
                        </>
                      ) : (
                        <>
                          <Globe className="h-3 w-3" />
                          <span>Shared</span>
                        </>
                      )}
                    </Badge>
                    <Badge variant={project.clientType === 'government' ? 'outline' : 'secondary'}>
                      {project.clientType === 'commercial' ? 'Commercial' : 'Government'}
                    </Badge>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="border-t pt-4 mt-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {canViewDetails && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleProjectExpand(project);
                                  }}
                                  className="h-8 w-8 p-0"
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>View Details</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        {canEditProject && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditProject(project);
                                  }}
                                  className="h-8 w-8 p-0"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>Edit Project</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        {canAssignUsers && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openAssignUsersDialog(project);
                                  }}
                                  className="h-8 w-8 p-0"
                                >
                                  <Users className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>Assign Users</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        {canShareProject && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openShareProjectDialog(project);
                                  }}
                                  className="h-8 w-8 p-0"
                                >
                                  <Share2 className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>Share Project</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                      {canDeleteProject && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteProject(project);
                                }}
                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Delete Project</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  </div>

                </CardContent>
              </Card>
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <FolderOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              {projects.length === 0 ? (
                // No projects at all
                <>
                  <h3 className="text-xl font-medium text-gray-900 mb-2">No projects assigned</h3>
                  <p className="text-gray-500 mb-6">Get started by creating your first satellite image processing project</p>
                  {canCreateProject && (
                    <Button onClick={() => setIsCreateProjectDialogOpen(true)} className="mx-auto">
                      <Plus className="h-4 w-4 mr-2" />
                      Create First Project
                    </Button>
                  )}
                </>
              ) : (
                // Search/filter returned no results
                <>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
                  <p className="text-gray-500">Try adjusting your search or filter criteria</p>
                </>
              )}
            </div>
          )}
        </>
      ) : (
        // Project Detail View
        selectedProject && (
          <div className="space-y-8 pb-8">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsDetailViewOpen(false)}
                  className="flex items-center space-x-2"
                >
                  <ChevronRight className="h-4 w-4 rotate-180" />
                  <span>Back to Dashboard</span>
                </Button>
              </div>

              {/* Action Buttons - Hidden for reader users */}
              {!isReaderOnly && (
                <div className="flex items-center space-x-3">
                  {canManageProject && (
                    <Button
                      variant="outline"
                      className="flex items-center space-x-2"
                      onClick={() => handleEditProject(selectedProject)}
                    >
                      <Edit className="h-4 w-4" />
                      <span>Edit</span>
                    </Button>
                  )}
                  {canManageProject && (
                    <Button
                      variant="outline"
                      className="flex items-center space-x-2"
                      onClick={() => setIsAssignUsersDialogOpen(true)}
                    >
                      <UserPlus className="h-4 w-4" />
                      <span>Assign Users</span>
                    </Button>
                  )}
                  {canShareProject && (
                    <Button variant="outline" className="flex items-center space-x-2">
                      <Share2 className="h-4 w-4" />
                      <span>Share</span>
                    </Button>
                  )}
                  {canImportDatasets && (
                    <Button
                      variant="outline"
                      className="flex items-center space-x-2"
                      onClick={() => setIsImportDialogOpen(true)}
                    >
                      <Download className="h-4 w-4" />
                      <span>Import Datasets</span>
                    </Button>
                  )}
                  {canCreateJobs && (
                    <Button
                      className="flex items-center space-x-2"
                      onClick={() => setIsCreateTaskDialogOpen(true)}
                    >
                      <Plus className="h-4 w-4" />
                      <span>Create Job</span>
                    </Button>
                  )}
                </div>
              )}
            </div>

            {/* Unified Project Overview Card with Tabs */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-2xl font-bold text-gray-900">{selectedProject.name}</CardTitle>
                    <CardDescription className="text-gray-600 mt-1">
                      {selectedProject.client} • {selectedProject.organizationName}
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(selectedProject.status)}
                    <Badge variant={selectedProject.type === 'private' ? 'default' : 'secondary'} className="flex items-center space-x-1">
                      {selectedProject.type === 'private' ? (
                        <>
                          <Lock className="h-3 w-3" />
                          <span>Private</span>
                        </>
                      ) : (
                        <>
                          <Globe className="h-3 w-3" />
                          <span>Shared</span>
                        </>
                      )}
                    </Badge>
                    <Badge variant={selectedProject.clientType === 'government' ? 'outline' : 'secondary'}>
                      {selectedProject.clientType === 'commercial' ? 'Commercial' : 'Government'}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Project Description */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Description</h3>
                  <div className="text-gray-600 leading-relaxed">
                    {selectedProject.description.length > 200 ? (
                      <>
                        <p>
                          {isDescriptionExpanded
                            ? selectedProject.description
                            : `${selectedProject.description.substring(0, 200)}...`
                          }
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="mt-2 p-0 h-auto text-blue-600 hover:text-blue-800 hover:bg-transparent"
                          onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                        >
                          {isDescriptionExpanded ? 'Show less' : 'Show more'}
                        </Button>
                      </>
                    ) : (
                      <p>{selectedProject.description}</p>
                    )}
                  </div>
                </div>
                
                {/* Statistics Grid */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Project Statistics</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                    <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                          <Database className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <p className="text-sm text-green-700 font-medium">Datasets</p>
                          <p className="text-2xl font-bold text-green-800">{selectedProject.datasetCount || 0}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                          <Image className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <p className="text-sm text-purple-700 font-medium">Images</p>
                          <p className="text-2xl font-bold text-purple-800">{selectedProject.imageCount.toLocaleString()}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                          <HardDrive className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <p className="text-sm text-blue-700 font-medium">Storage</p>
                          <p className="text-2xl font-bold text-blue-800">{selectedProject.totalSize}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-r from-orange-50 to-orange-100 p-4 rounded-lg border border-orange-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                          <FileType className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <p className="text-sm text-orange-700 font-medium">Metadata</p>
                          <p className="text-2xl font-bold text-orange-800">{selectedProject.metadataSize}</p>
                          <Button 
                            size="sm" 
                            variant="ghost" 
                            className="mt-1 h-6 px-2 text-xs text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                            onClick={() => {
                              // Simulate metadata download
                              const element = document.createElement('a');
                              element.href = `data:text/plain;charset=utf-8,${encodeURIComponent('# Project Metadata\n\nProject: ' + selectedProject.name + '\nMetadata Files: ' + selectedProject.metadataCount + '\nTotal Size: ' + selectedProject.metadataSize + '\nGenerated: ' + new Date().toISOString())}`;
                              element.download = `${selectedProject.name.replace(/\s+/g, '-').toLowerCase()}-metadata.txt`;
                              document.body.appendChild(element);
                              element.click();
                              document.body.removeChild(element);
                            }}
                          >
                            <Download className="h-3 w-3 mr-1" />
                            Download
                          </Button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-r from-amber-50 to-amber-100 p-4 rounded-lg border border-amber-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-amber-500 rounded-lg flex items-center justify-center">
                          <Users className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <p className="text-sm text-amber-700 font-medium">Collaborators</p>
                          <p className="text-2xl font-bold text-amber-800">{selectedProject.collaborators.length}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Activity Status */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Current Activity</h3>
                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg border">
                    {selectedProject.isProcessing ? (
                      <Loader2 className="h-6 w-6 text-blue-500 animate-spin" />
                    ) : (
                      <Clock className="h-6 w-6 text-gray-500" />
                    )}
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{selectedProject.currentActivity}</p>
                      <p className="text-sm text-gray-600">
                        {selectedProject.isProcessing ? 'Processing' : 'Idle'} • Last updated {selectedProject.lastUpdated}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={selectedProject.isProcessing ? 'default' : 'secondary'}>
                        {selectedProject.isProcessing ? 'Active' : 'Idle'}
                      </Badge>
                      {selectedProject.hasAlerts && (
                        <AlertTriangle className="h-5 w-5 text-amber-500" />
                      )}
                    </div>
                  </div>
                </div>

                {/* Project Detail Tabs */}
                <div className="border-t pt-6">
                  <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-5 mb-6">
                      <TabsTrigger value="activity" className="flex items-center space-x-2">
                        <Activity className="h-4 w-4" />
                        <span>Activity</span>
                      </TabsTrigger>
                      <TabsTrigger value="jobs" className="flex items-center space-x-2">
                        <Zap className="h-4 w-4" />
                        <span>Jobs</span>
                      </TabsTrigger>
                      <TabsTrigger value="datasets" className="flex items-center space-x-2">
                        <Database className="h-4 w-4" />
                        <span>Datasets</span>
                      </TabsTrigger>
                      <TabsTrigger value="collaborators" className="flex items-center space-x-2">
                        <Users className="h-4 w-4" />
                        <span>Collaborators</span>
                      </TabsTrigger>
                      <TabsTrigger value="configuration" className="flex items-center space-x-2">
                        <Settings className="h-4 w-4" />
                        <span>Configuration</span>
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="activity" className="space-y-4">
                      <div className="border rounded-lg p-4">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-semibold">Execution History & Activity</h3>
                            <div className="flex items-center space-x-2">
                              <Badge variant="secondary" className="text-xs">
                                {selectedProject.jobExecutions.filter(e => e.status === 'running').length} running
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {selectedProject.jobExecutions.filter(e => e.status === 'completed').length} completed
                              </Badge>
                            </div>
                          </div>

                          {selectedProject.jobExecutions.length > 0 ? (
                            <div className="space-y-3">
                              {selectedProject.jobExecutions.map((execution) => (
                                <div key={execution.id} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg border">
                                  <div className="flex-shrink-0">
                                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                                      execution.status === 'completed' ? 'bg-green-100' :
                                      execution.status === 'running' ? 'bg-blue-100' :
                                      execution.status === 'failed' ? 'bg-red-100' : 'bg-gray-100'
                                    }`}>
                                      {execution.status === 'completed' ? (
                                        <CheckCircle className={`h-5 w-5 text-green-600`} />
                                      ) : execution.status === 'running' ? (
                                        <Loader2 className={`h-5 w-5 text-blue-600 animate-spin`} />
                                      ) : execution.status === 'failed' ? (
                                        <AlertTriangle className={`h-5 w-5 text-red-600`} />
                                      ) : (
                                        <Clock className={`h-5 w-5 text-gray-600`} />
                                      )}
                                    </div>
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between">
                                      <h4 className="text-sm font-medium text-gray-900">
                                        {execution.status === 'running' ? 'Executing: ' : execution.status === 'completed' ? 'Completed: ' : 'Failed: '}
                                        {execution.jobName}
                                      </h4>
                                      <span className="text-xs text-gray-500">
                                        {new Date(execution.startTime).toLocaleString()}
                                      </span>
                                    </div>
                                    <p className="text-sm text-gray-600 mt-1">
                                      <span className="font-medium">{execution.service}</span> •
                                      {execution.status === 'running' ? `${execution.progress}% complete` :
                                       execution.status === 'completed' ? `Finished ${execution.endTime ? new Date(execution.endTime).toLocaleString() : ''} ${execution.duration ? `(${execution.duration})` : ''}` :
                                       execution.status === 'failed' ? 'Execution failed' : 'Processing'}
                                    </p>
                                    {execution.status === 'running' && execution.progress > 0 && (
                                      <div className="mt-2">
                                        <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                                          <span>Progress</span>
                                          <span>{execution.progress}%</span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-2">
                                          <div
                                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                            style={{ width: `${execution.progress}%` }}
                                          ></div>
                                        </div>
                                      </div>
                                    )}
                                    {execution.outputDataset && (
                                      <p className="text-xs text-gray-500 mt-1">
                                        Output: <span className="font-medium">{execution.outputDataset}</span>
                                      </p>
                                    )}
                                  </div>
                                  <div className="flex-shrink-0">
                                    <div className="flex flex-col items-end space-y-1">
                                      {execution.status === 'completed' ? (
                                        <Badge variant="default" className="bg-green-100 text-green-800">
                                          Completed
                                        </Badge>
                                      ) : execution.status === 'running' ? (
                                        <Badge variant="default" className="bg-blue-100 text-blue-800">
                                          Running
                                        </Badge>
                                      ) : execution.status === 'failed' ? (
                                        <Badge variant="destructive">
                                          Failed
                                        </Badge>
                                      ) : (
                                        <Badge variant="secondary">
                                          Cancelled
                                        </Badge>
                                      )}
                                      <Badge variant="outline" className="text-xs">
                                        {execution.triggeredBy}
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="text-center py-8">
                              <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                              <h3 className="text-lg font-medium text-gray-900 mb-2">No Execution History</h3>
                              <p className="text-gray-600 mb-4">Job executions and activity will appear here once you run jobs or import data.</p>
                              <div className="flex items-center justify-center space-x-3">
                                {canCreateJobs && (
                                  <Button
                                    onClick={() => setIsCreateTaskDialogOpen(true)}
                                    className="flex items-center space-x-2"
                                  >
                                    <Plus className="h-4 w-4" />
                                    <span>Create Job</span>
                                  </Button>
                                )}
                                {canImportDatasets && (
                                  <Button
                                    variant="outline"
                                    onClick={() => setIsImportDialogOpen(true)}
                                    className="flex items-center space-x-2"
                                  >
                                    <Download className="h-4 w-4" />
                                    <span>Import Data</span>
                                  </Button>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="jobs" className="space-y-4">
                      <div className="border rounded-lg p-4">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-semibold">Configured Jobs</h3>
                            <div className="flex items-center space-x-2">
                              <Badge variant="secondary" className="text-xs">
                                {selectedProject.tasks.length} total jobs
                              </Badge>
                              {canCreateJobs && (
                                <Button
                                  size="sm"
                                  onClick={() => setIsCreateTaskDialogOpen(true)}
                                  className="flex items-center space-x-2"
                                >
                                  <Plus className="h-4 w-4" />
                                  <span>New Job</span>
                                </Button>
                              )}
                            </div>
                          </div>

                          {selectedProject.tasks.length > 0 ? (
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Job Name</TableHead>
                                  <TableHead>Service</TableHead>
                                  <TableHead>Type</TableHead>
                                  <TableHead>Status</TableHead>
                                  <TableHead>Schedule/Trigger</TableHead>
                                  <TableHead>Created</TableHead>
                                  <TableHead>Actions</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {selectedProject.tasks.map((task) => (
                                  <TableRow key={task.id}>
                                    <TableCell className="font-medium">{task.name}</TableCell>
                                    <TableCell>
                                      <div className="flex items-center space-x-2">
                                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                          <Zap className="h-4 w-4 text-blue-600" />
                                        </div>
                                        <span>{task.service}</span>
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      <Badge variant={task.type === 'event' ? 'default' : 'secondary'}>
                                        {task.type === 'event' ? 'On-Demand' : 'Scheduled'}
                                      </Badge>
                                    </TableCell>
                                    <TableCell>
                                      {task.status === 'completed' ? (
                                        <Badge variant="default" className="bg-green-100 text-green-800">
                                          Configured
                                        </Badge>
                                      ) : task.status === 'running' ? (
                                        <Badge variant="default" className="bg-blue-100 text-blue-800">
                                          Running
                                        </Badge>
                                      ) : task.status === 'failed' ? (
                                        <Badge variant="destructive">
                                          Failed
                                        </Badge>
                                      ) : (
                                        <Badge variant="secondary">
                                          Pending
                                        </Badge>
                                      )}
                                    </TableCell>
                                    <TableCell className="text-sm text-gray-600">
                                      {task.type === 'scheduled' ? 'Scheduled' : 'Manual/Event'}
                                    </TableCell>
                                    <TableCell className="text-sm text-gray-600">
                                      {task.startTime ? new Date(task.startTime).toLocaleDateString() : 'Not started'}
                                    </TableCell>
                                    <TableCell>
                                      <div className="flex items-center space-x-2">
                                        <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        {canManageProject && (
                                          <>
                                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                                              <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0 text-red-600 hover:text-red-700">
                                              <Trash2 className="h-4 w-4" />
                                            </Button>
                                          </>
                                        )}
                                      </div>
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          ) : (
                            <div className="text-center py-8">
                              <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                              <h3 className="text-lg font-medium text-gray-900 mb-2">No Jobs Configured</h3>
                              <p className="text-gray-600 mb-4">Create your first job to start processing data.</p>
                              {canCreateJobs && (
                                <Button
                                  onClick={() => setIsCreateTaskDialogOpen(true)}
                                  className="flex items-center space-x-2"
                                >
                                  <Plus className="h-4 w-4" />
                                  <span>Create First Job</span>
                                </Button>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="datasets" className="space-y-4">
                      <div className="border rounded-lg p-4">
                        {selectedProject.datasets.length > 0 ? (
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Description</TableHead>
                                <TableHead>Tag</TableHead>
                                <TableHead>Last Updated</TableHead>
                                <TableHead>Images</TableHead>
                                <TableHead>Size</TableHead>
                                <TableHead>Actions</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {selectedProject.datasets.map((dataset) => (
                                <TableRow key={dataset.id}>
                                  <TableCell className="font-medium">{dataset.name}</TableCell>
                                  <TableCell>
                                {editingDataset === dataset.id ? (
                                  <Input
                                    value={editingDatasetData.description || ""}
                                    onChange={(e) => setEditingDatasetData({...editingDatasetData, description: e.target.value})}
                                    className="w-full"
                                  />
                                ) : (
                                  dataset.description || "-"
                                )}
                              </TableCell>
                              <TableCell>
                                {editingDataset === dataset.id ? (
                                  <Input
                                    value={editingDatasetData.tag || ""}
                                    onChange={(e) => setEditingDatasetData({...editingDatasetData, tag: e.target.value})}
                                    className="w-full"
                                  />
                                ) : (
                                  dataset.tag ? (
                                    <Badge variant="secondary">{dataset.tag}</Badge>
                                  ) : (
                                    "-"
                                  )
                                )}
                              </TableCell>
                              <TableCell>{dataset.lastUpdated}</TableCell>
                              <TableCell>{dataset.images.toLocaleString()}</TableCell>
                              <TableCell>{dataset.size}</TableCell>
                              <TableCell>
                                <div className="flex items-center space-x-2">
                                  {editingDataset === dataset.id ? (
                                    <>
                                      <Button
                                        size="sm"
                                        onClick={() => handleSaveDataset(dataset.id)}
                                        className="h-8 w-8 p-0"
                                      >
                                        <Check className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={handleCancelEdit}
                                        className="h-8 w-8 p-0"
                                      >
                                        <X className="h-4 w-4" />
                                      </Button>
                                    </>
                                  ) : (
                                    <>
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Button
                                              size="sm"
                                              variant="ghost"
                                              onClick={() => handleEditDataset(dataset)}
                                              className="h-8 w-8 p-0"
                                            >
                                              <FileText className="h-4 w-4" />
                                            </Button>
                                          </TooltipTrigger>
                                          <TooltipContent>Edit Dataset</TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Button
                                              size="sm"
                                              variant="ghost"
                                              onClick={() => handleDeleteDataset(dataset.id)}
                                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                            >
                                              <Trash2 className="h-4 w-4" />
                                            </Button>
                                          </TooltipTrigger>
                                          <TooltipContent>Delete Dataset</TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </>
                                  )}
                                  </div>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        ) : (
                          <div className="text-center py-8">
                            <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No datasets found</h3>
                            <p className="text-gray-500">This project doesn't have any datasets yet.</p>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="configuration" className="space-y-4">
                      <div className="border rounded-lg p-4 space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label className="text-sm font-medium">Organization</Label>
                            <p className="text-sm text-gray-600">{selectedProject.organizationName}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium">Tenant</Label>
                            <p className="text-sm text-gray-600">{selectedProject.tenantName}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium">Created</Label>
                            <p className="text-sm text-gray-600">{selectedProject.createdAt}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium">Status</Label>
                            <div className="mt-1">{getStatusBadge(selectedProject.status)}</div>
                          </div>
                        </div>
                      </div>
                    </TabsContent>
                    

                    <TabsContent value="collaborators" className="space-y-4">
                      <div className="border rounded-lg p-4">
                        {selectedProject.collaborators.length > 0 ? (
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Email</TableHead>
                                <TableHead>Role</TableHead>
                                <TableHead>Organization</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {selectedProject.collaborators.map((collaborator) => (
                                <TableRow key={collaborator.id}>
                                  <TableCell className="font-medium">{collaborator.name}</TableCell>
                                  <TableCell>{collaborator.email}</TableCell>
                                  <TableCell>
                                    <Badge variant="outline">{collaborator.role}</Badge>
                                  </TableCell>
                                  <TableCell>{collaborator.organization}</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        ) : (
                          <div className="text-center py-8">
                            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-500">No collaborators assigned</p>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              </CardContent>
            </Card>

            {/* Detailed Logs */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-gray-600" />
                <span className="text-lg font-semibold text-gray-900">Detailed Logs</span>
              </div>
              
              <div className="bg-white border rounded-lg p-4 font-mono text-sm max-h-96 overflow-y-auto">
                {selectedProject.logs.length > 0 ? (
                  <div className="space-y-1">
                    {selectedProject.logs.map((log) => (
                      <div key={log.id} className="flex items-start space-x-3">
                        <span className="text-gray-600 text-xs min-w-0 flex-shrink-0">
                          {new Date(log.timestamp).toISOString().replace('T', ' ').substring(0, 19)}
                        </span>
                        <span className={`text-xs min-w-0 flex-shrink-0 w-8 ${
                          log.level === 'info' ? 'text-blue-600' :
                          log.level === 'warning' ? 'text-yellow-600' :
                          log.level === 'error' ? 'text-red-600' :
                          log.level === 'success' ? 'text-green-600' :
                          'text-gray-600'
                        }`}>
                          {log.level.toUpperCase()}
                        </span>
                        <span className="text-gray-900 flex-1 min-w-0">
                          {log.message}
                          {log.details && (
                            <span className="text-gray-600"> - {log.details}</span>
                          )}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <span className="text-gray-500">No logs available</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )
      )}

      {/* Import Image Dialog */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Datasets</DialogTitle>
            <DialogDescription>
              Enter the URL of the dataset repository you want to import
            </DialogDescription>
          </DialogHeader>
          {!isImportInProgress ? (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="datasetUrl">Dataset URL</Label>
                <Input
                  id="datasetUrl"
                  placeholder="https://example.com/satellite-datasets/repository.zip"
                  value={importUrl}
                  onChange={(e) => setImportUrl(e.target.value)}
                />
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="font-medium">Importing datasets...</p>
                <p className="text-sm text-gray-600">Dataset download is in progress, you may safely close this dialog</p>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{importProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${importProgress}%` }}
                  ></div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            {!isImportInProgress ? (
              <>
                <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleImportImage} disabled={!importUrl}>
                  Import Datasets
                </Button>
              </>
            ) : (
              <Button disabled>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Importing datasets...
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Job Dialog */}
      <Dialog open={isCreateTaskDialogOpen} onOpenChange={setIsCreateTaskDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>Create Job</DialogTitle>
            <DialogDescription>
              Select a service, dataset, and configure the job parameters
            </DialogDescription>
          </DialogHeader>
          {!isTaskScheduling ? (
            <div className="flex-1 overflow-y-auto pr-6 -mr-6">
            <div className="space-y-6">
              {/* Service Selection */}
              <div className="space-y-2">
                <Label>Service</Label>
                <Select
                  value={selectedService?.id || ""}
                  onValueChange={(value) => {
                    const service = predefinedServices.find(s => s.id === value);
                    setSelectedService(service || null);
                    setTaskArgs(service?.defaultArgs || "");
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a service" />
                  </SelectTrigger>
                  <SelectContent>
                    {predefinedServices.map((service) => (
                      <SelectItem key={service.id} value={service.id}>
                        <div>
                          <div className="font-medium">{service.name}</div>
                          <div className="text-sm text-gray-500">{service.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* Source Dataset Selection */}
              <div className="space-y-2">
                <Label>Source Dataset</Label>
                <Select
                  value={selectedDataset?.id || ""}
                  onValueChange={(value) => {
                    const dataset = selectedProject?.datasets.find(d => d.id === value);
                    setSelectedDataset(dataset || null);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a dataset to process" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedProject?.datasets.map((dataset) => (
                      <SelectItem key={dataset.id} value={dataset.id}>
                        <div>
                          <div className="font-medium">{dataset.name}</div>
                          <div className="text-sm text-gray-500">
                            {dataset.images.toLocaleString()} images • {dataset.size}
                            {dataset.tag && ` • ${dataset.tag}`}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* Target Dataset Name */}
              <div className="space-y-2">
                <Label>Target Dataset Name (Optional)</Label>
                <Input
                  value={targetDatasetName}
                  onChange={(e) => setTargetDatasetName(e.target.value)}
                  placeholder="Enter name for output dataset (leave empty to auto-generate)"
                />
                <p className="text-sm text-gray-500">
                  If not specified, a name will be generated based on the service and timestamp
                </p>
              </div>
              
              {/* Execution Trigger Section */}
              <div className="space-y-4 border-t pt-4">
                <div className="space-y-2">
                  <Label>Execution Trigger</Label>
                  <Select value={executionTrigger} onValueChange={(value: 'now' | 'event' | 'scheduled') => setExecutionTrigger(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="now">
                        <div className="flex items-center space-x-2">
                          <Zap className="h-4 w-4 text-green-600" />
                          <div>
                            <div className="font-medium">Run Now</div>
                            <div className="text-sm text-gray-500">Execute immediately</div>
                          </div>
                        </div>
                      </SelectItem>
                      <SelectItem value="event">
                        <div className="flex items-center space-x-2">
                          <Activity className="h-4 w-4 text-blue-600" />
                          <div>
                            <div className="font-medium">Event Based</div>
                            <div className="text-sm text-gray-500">Trigger on specific events</div>
                          </div>
                        </div>
                      </SelectItem>
                      <SelectItem value="scheduled">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-purple-600" />
                          <div>
                            <div className="font-medium">Scheduled</div>
                            <div className="text-sm text-gray-500">Run on CRON schedule</div>
                          </div>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Event Selection */}
                {executionTrigger === 'event' && (
                  <div className="space-y-2">
                    <Label>Event Type</Label>
                    <Select value={selectedEvent} onValueChange={setSelectedEvent}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select an event trigger" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="dataset_upload">
                          <div>
                            <div className="font-medium">Successful Dataset Upload</div>
                            <div className="text-sm text-gray-500">Triggers when new datasets are uploaded</div>
                          </div>
                        </SelectItem>
                        <SelectItem value="file_upload">
                          <div>
                            <div className="font-medium">File Upload Complete</div>
                            <div className="text-sm text-gray-500">Triggers when individual files are uploaded</div>
                          </div>
                        </SelectItem>
                        <SelectItem value="dataset_threshold">
                          <div>
                            <div className="font-medium">Dataset Size Threshold</div>
                            <div className="text-sm text-gray-500">Triggers when dataset reaches certain size</div>
                          </div>
                        </SelectItem>
                        <SelectItem value="quality_check">
                          <div>
                            <div className="font-medium">Quality Check Passed</div>
                            <div className="text-sm text-gray-500">Triggers after successful quality validation</div>
                          </div>
                        </SelectItem>
                        <SelectItem value="metadata_complete">
                          <div>
                            <div className="font-medium">Metadata Processing Complete</div>
                            <div className="text-sm text-gray-500">Triggers when metadata extraction finishes</div>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    {selectedEvent && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div className="flex items-start space-x-2">
                          <Activity className="h-4 w-4 text-blue-600 mt-0.5" />
                          <div className="text-sm">
                            <p className="font-medium text-blue-900">Event Configuration</p>
                            <p className="text-blue-700 mt-1">
                              {selectedEvent === 'dataset_upload' && 'Job will automatically execute when a new dataset is successfully uploaded to this project.'}
                              {selectedEvent === 'file_upload' && 'Job will trigger for each individual file upload completion.'}
                              {selectedEvent === 'dataset_threshold' && 'Job will execute when the dataset size reaches the configured threshold (default: 1GB).'}
                              {selectedEvent === 'quality_check' && 'Job will run after datasets pass automated quality validation checks.'}
                              {selectedEvent === 'metadata_complete' && 'Job will trigger when metadata extraction and processing is completed.'}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
                
                {/* CRON Schedule Configuration */}
                {executionTrigger === 'scheduled' && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>CRON Expression</Label>
                      <div className="flex space-x-2">
                        <Input
                          value={cronExpression}
                          onChange={(e) => {
                            setCronExpression(e.target.value);
                            setCronDescription(parseCronExpression(e.target.value));
                          }}
                          placeholder="0 0 * * 0"
                          className="font-mono"
                        />
                        <Button variant="outline" onClick={() => {
                          setCronExpression('0 0 * * 0');
                          setCronDescription('Weekly on Sunday at midnight');
                        }}>
                          Reset
                        </Button>
                      </div>
                      <p className="text-sm text-gray-600">{cronDescription}</p>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Quick Presets</Label>
                      <div className="grid grid-cols-2 gap-2">
                        <Button variant="outline" size="sm" onClick={() => {
                          setCronExpression('0 */1 * * *');
                          setCronDescription('Every hour');
                        }}>
                          Hourly
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => {
                          setCronExpression('0 0 * * *');
                          setCronDescription('Daily at midnight');
                        }}>
                          Daily
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => {
                          setCronExpression('0 0 * * 0');
                          setCronDescription('Weekly on Sunday at midnight');
                        }}>
                          Weekly
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => {
                          setCronExpression('0 0 1 * *');
                          setCronDescription('Monthly on the 1st at midnight');
                        }}>
                          Monthly
                        </Button>
                      </div>
                    </div>
                    
                    <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                      <div className="flex items-start space-x-2">
                        <Calendar className="h-4 w-4 text-purple-600 mt-0.5" />
                        <div className="text-sm">
                          <p className="font-medium text-purple-900">Schedule Format</p>
                          <p className="text-purple-700 mt-1">CRON format: minute hour day-of-month month day-of-week</p>
                          <p className="text-purple-600 text-xs mt-1">Example: "0 2 * * 1" = Every Monday at 2:00 AM</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Job Arguments */}
              <div className="space-y-2">
                <Label>Job Arguments</Label>
                <Textarea
                  value={taskArgs}
                  onChange={(e) => setTaskArgs(e.target.value)}
                  rows={8}
                  className="font-mono text-sm"
                  placeholder="Enter job configuration..."
                />
              </div>
            </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="font-medium">Scheduling job...</p>
                <p className="text-sm text-gray-600">Setting up the job execution environment</p>
              </div>
            </div>
          )}
          <DialogFooter>
            {!isTaskScheduling ? (
              <>
                <Button variant="outline" onClick={() => {
                  setIsCreateTaskDialogOpen(false);
                  setSelectedService(null);
                  setSelectedDataset(null);
                  setTargetDatasetName("");
                  setTaskArgs("");
                  setExecutionTrigger('now');
                  setSelectedEvent('');
                  setCronExpression('0 0 * * 0');
                  setCronDescription('Weekly on Sunday at midnight');
                }}>
                  Cancel
                </Button>
                <Button onClick={handleCreateTask} disabled={!selectedService || !selectedDataset || (executionTrigger === 'event' && !selectedEvent)}>
                  {executionTrigger === 'now' ? 'Create & Run Job' : 'Create Job'}
                </Button>
              </>
            ) : (
              <Button disabled>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Scheduling...
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Project Dialog */}
      <Dialog open={isEditProjectDialogOpen} onOpenChange={setIsEditProjectDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Project</DialogTitle>
            <DialogDescription>
              Update project details and configuration
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-name">Project Name</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="edit-client">Client</Label>
                <Input
                  id="edit-client"
                  value={formData.client}
                  onChange={(e) => setFormData({...formData, client: e.target.value})}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsEditProjectDialogOpen(false);
              setProjectToEdit(null);
              resetCreateForm();
            }}>
              Cancel
            </Button>
            <Button onClick={handleUpdateProject} disabled={!formData.name || !formData.client}>
              Update Project
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Project</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{projectToDelete?.name}"? This action cannot be undone.
              All associated data, jobs, and configurations will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setIsDeleteDialogOpen(false);
              setProjectToDelete(null);
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteProject} className="bg-red-600 hover:bg-red-700">
              Delete Project
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Assign Users Dialog */}
      <Dialog open={isAssignUsersDialogOpen} onOpenChange={setIsAssignUsersDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Assign User</DialogTitle>
            <DialogDescription>
              Manage users who have access to {selectedProject?.name}
            </DialogDescription>
          </DialogHeader>
          {selectedProject && (
            <AssignUsersContent
              project={selectedProject}
              availableUsers={availableUsers}
              onAssignUsers={handleAssignUsers}
              onClose={() => setIsAssignUsersDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Share Project Dialog */}
      <Dialog open={isShareProjectDialogOpen} onOpenChange={setIsShareProjectDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Share Project</DialogTitle>
            <DialogDescription>
              Manage organization access for collaborative projects
            </DialogDescription>
          </DialogHeader>
          {selectedProject && (
            <ShareProjectContent
              project={selectedProject}
              availableOrganizations={organizations}
              onShareProject={handleShareProject}
              onClose={() => setIsShareProjectDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Project Creation Status Dialog */}
      <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Creating Project</DialogTitle>
            <DialogDescription>
              Setting up your satellite image processing project
            </DialogDescription>
          </DialogHeader>
          <ProjectCreationStatusContent 
            projectData={newProjectData}
            projectCreationComplete={projectCreationComplete}
            tenantProvisioningComplete={tenantProvisioningComplete}
            onClose={() => setIsStatusDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Project Creation Status Component
function ProjectCreationStatusContent({ 
  projectData, 
  projectCreationComplete, 
  tenantProvisioningComplete,
  onClose 
}: {
  projectData: { project: Project; tenant: Tenant; isNewTenant: boolean } | null;
  projectCreationComplete: boolean;
  tenantProvisioningComplete: boolean;
  onClose: () => void;
}) {
  if (!projectData) return null;

  const allComplete = projectCreationComplete && tenantProvisioningComplete;
  const isNewTenant = projectData.isNewTenant;

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        {/* Tenant Provisioning Status */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-8 h-8">
            {tenantProvisioningComplete ? (
              <CheckCircle className="h-6 w-6 text-green-600" />
            ) : (
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            )}
          </div>
          <div>
            <p className="font-bold text-gray-900">
              {tenantProvisioningComplete ? (
                isNewTenant ? (
                  `Tenant "${projectData.tenant.name}" provisioned successfully!`
                ) : (
                  `Tenant "${projectData.tenant.name}" is ready`
                )
              ) : (
                isNewTenant ? (
                  "Tenant is being provisioned..."
                ) : (
                  "Configuring tenant access..."
                )
              )}
            </p>
            {tenantProvisioningComplete && (
              <p className="text-sm text-gray-600 mt-1">
                {isNewTenant ? (
                  "Tenant infrastructure has been provisioned"
                ) : (
                  "Project has been configured with existing tenant"
                )}
              </p>
            )}
          </div>
        </div>

        {/* Project Creation Status */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-8 h-8">
            {projectCreationComplete ? (
              <CheckCircle className="h-6 w-6 text-green-600" />
            ) : (
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            )}
          </div>
          <div>
            <p className="font-bold text-gray-900">
              {projectCreationComplete ? (
                `Project "${projectData.project.name}" created successfully!`
              ) : (
                "Project is being created..."
              )}
            </p>
            {projectCreationComplete && (
              <p className="text-sm text-gray-600 mt-1">
                Project infrastructure has been set up
              </p>
            )}
          </div>
        </div>
      </div>
      
      {allComplete && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <p className="text-sm font-medium text-green-800">
              Project "{projectData.project.name}" is ready for use!
            </p>
          </div>
          <p className="text-sm text-green-700 mt-2">
            You can now start managing users, uploading files, and running satellite image processing jobs.
          </p>
        </div>
      )}
      
      <DialogFooter>
        <Button onClick={onClose} disabled={!allComplete}>
          {allComplete ? 'Done' : 'Please wait...'}
        </Button>
      </DialogFooter>
    </div>
  );
}

// Assign Users Dialog Content Component
function AssignUsersContent({ 
  project, 
  availableUsers, 
  onAssignUsers, 
  onClose 
}: {
  project: Project;
  availableUsers: ProjectUser[];
  onAssignUsers: (projectId: string, users: ProjectUser[]) => void;
  onClose: () => void;
}) {
  const [selectedUsers, setSelectedUsers] = useState<ProjectUser[]>(project.assignedUsers);
  const [userRoles, setUserRoles] = useState<Record<string, string>>(
    project.assignedUsers.reduce((acc, user) => ({ ...acc, [user.id]: user.role }), {})
  );

  const handleUserToggle = (user: ProjectUser, checked: boolean, role: string = 'reader') => {
    if (checked) {
      setSelectedUsers([...selectedUsers, { ...user, role: role as ProjectUser['role'] }]);
      setUserRoles({ ...userRoles, [user.id]: role });
    } else {
      setSelectedUsers(selectedUsers.filter(u => u.id !== user.id));
      const newRoles = { ...userRoles };
      delete newRoles[user.id];
      setUserRoles(newRoles);
    }
  };

  const handleRoleChange = (userId: string, role: string) => {
    setUserRoles({ ...userRoles, [userId]: role });
    setSelectedUsers(selectedUsers.map(user => 
      user.id === userId ? { ...user, role: role as ProjectUser['role'] } : user
    ));
  };

  const handleRemoveUser = (userId: string) => {
    setSelectedUsers(selectedUsers.filter(u => u.id !== userId));
    const newRoles = { ...userRoles };
    delete newRoles[userId];
    setUserRoles(newRoles);
  };

  const handleSave = () => {
    const usersWithRoles = selectedUsers.map(user => ({
      ...user,
      role: userRoles[user.id] as ProjectUser['role'],
      permissions: getRolePermissions(userRoles[user.id])
    }));
    onAssignUsers(project.id, usersWithRoles);
    onClose();
  };

  const getRolePermissions = (role: string): string[] => {
    switch (role) {
      case 'admin':
        return ['read', 'write', 'delete', 'manage_users'];
      case 'editor':
        return ['read', 'write'];
      case 'viewer':
        return ['read'];
      case 'reader':
        return ['read'];
      default:
        return ['read'];
    }
  };

  // Get users not currently assigned to project
  const unassignedUsers = availableUsers.filter(user => 
    !selectedUsers.some(assigned => assigned.id === user.id)
  );

  return (
    <div className="space-y-6">
      {/* Currently Assigned Users */}
      <div className="space-y-4">
        <h3 className="font-semibold">Users with Access</h3>
        {selectedUsers.length > 0 ? (
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead className="w-12">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {selectedUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Select value={userRoles[user.id] || 'reader'} onValueChange={(value) => handleRoleChange(user.id, value)}>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="admin">Project Admin</SelectItem>
                          <SelectItem value="editor">Contributor</SelectItem>
                          <SelectItem value="reader">Reader</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveUser(user.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-8 border rounded-lg border-dashed">
            <Users className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No users assigned to this project</p>
          </div>
        )}
      </div>

      {/* Add New Users */}
      {unassignedUsers.length > 0 && (
        <AddUserSection 
          unassignedUsers={unassignedUsers}
          onAddUser={(user, checked, role) => handleUserToggle(user, checked, role)}
        />
      )}

      <DialogFooter>
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={handleSave}>
          Update Access
        </Button>
      </DialogFooter>
    </div>
  );
}

// Share Project Dialog Content Component
function ShareProjectContent({ 
  project, 
  availableOrganizations, 
  onShareProject, 
  onClose 
}: {
  project: Project;
  availableOrganizations: Organization[];
  onShareProject: (projectId: string, organizationIds: string[]) => void;
  onClose: () => void;
}) {
  const [sharedOrganizations, setSharedOrganizations] = useState<string[]>(project.sharedOrganizations);

  const handleOrganizationToggle = (orgId: string, checked: boolean) => {
    if (checked) {
      setSharedOrganizations([...sharedOrganizations, orgId]);
    } else {
      setSharedOrganizations(sharedOrganizations.filter(id => id !== orgId));
    }
  };

  const handleSave = () => {
    onShareProject(project.id, sharedOrganizations);
    onClose();
  };

  const removeSharedOrganization = (orgId: string) => {
    setSharedOrganizations(sharedOrganizations.filter(id => id !== orgId));
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div>
          <h3 className="font-semibold mb-2">Current Shared Organizations</h3>
          {sharedOrganizations.length > 0 ? (
            <div className="space-y-2">
              {sharedOrganizations.map(orgId => {
                const org = availableOrganizations.find(o => o.id === orgId);
                return org ? (
                  <div key={orgId} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">{org.name}</div>
                      <div className="text-sm text-gray-500">{org.description}</div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSharedOrganization(orgId)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : null;
              })}
            </div>
          ) : (
            <div className="text-sm text-gray-500 p-3 border rounded-lg border-dashed">
              No organizations currently have access to this project
            </div>
          )}
        </div>

        <div>
          <h3 className="font-semibold mb-2">Available Organizations</h3>
          <div className="max-h-48 overflow-y-auto border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">Share</TableHead>
                  <TableHead>Organization</TableHead>
                  <TableHead>Description</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {availableOrganizations
                  .filter(org => org.id !== "1") // Exclude current organization
                  .map((org) => (
                    <TableRow key={org.id}>
                      <TableCell>
                        <Checkbox
                          checked={sharedOrganizations.includes(org.id)}
                          onCheckedChange={(checked) => handleOrganizationToggle(org.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{org.name}</div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-gray-600">{org.description}</div>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
        </div>

        <div className="text-sm text-gray-600">
          Project will be marked as {sharedOrganizations.length > 0 ? 'Shared' : 'Private'}
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={handleSave}>
          Update Sharing
        </Button>
      </DialogFooter>
    </div>
  );
}

// Add User Section Component with Search
function AddUserSection({ 
  unassignedUsers, 
  onAddUser 
}: {
  unassignedUsers: ProjectUser[];
  onAddUser: (user: ProjectUser, checked: boolean, role?: string) => void;
}) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState<string>("reader");
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [selectedUsersToAdd, setSelectedUsersToAdd] = useState<ProjectUser[]>([]);

  const filteredUsers = unassignedUsers.filter(user => 
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleUserSelect = (user: ProjectUser) => {
    const isSelected = selectedUsersToAdd.some(u => u.id === user.id);
    if (isSelected) {
      setSelectedUsersToAdd(selectedUsersToAdd.filter(u => u.id !== user.id));
    } else {
      setSelectedUsersToAdd([...selectedUsersToAdd, user]);
    }
  };

  const handleAddSelectedUsers = () => {
    selectedUsersToAdd.forEach(user => {
      onAddUser(user, true, selectedRole);
    });
    setSelectedUsersToAdd([]);
    setSearchTerm("");
    setIsSearchOpen(false);
  };

  const handleRemoveSelectedUser = (userId: string) => {
    setSelectedUsersToAdd(selectedUsersToAdd.filter(u => u.id !== userId));
  };

  return (
    <div className="space-y-4">
      <h3 className="font-semibold">Add Users</h3>
      
      {/* Search and Role Selection */}
      <div className="flex gap-3">
        <div className="flex-1 relative">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search users by name or email..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setIsSearchOpen(e.target.value.length > 0);
              }}
              onFocus={() => setIsSearchOpen(searchTerm.length > 0)}
              className="pl-10"
            />
          </div>
          
          {/* Search Results Dropdown */}
          {isSearchOpen && searchTerm.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-64 overflow-y-auto">
              {filteredUsers.length > 0 ? (
                <div className="py-1">
                  {filteredUsers.slice(0, 10).map((user) => {
                    const isSelected = selectedUsersToAdd.some(u => u.id === user.id);
                    return (
                      <div
                        key={user.id}
                        className={`px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center justify-between ${
                          isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                        }`}
                        onClick={() => handleUserSelect(user)}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            isSelected ? 'bg-blue-500' : 'bg-blue-100'
                          }`}>
                            <Users className={`h-4 w-4 ${isSelected ? 'text-white' : 'text-blue-600'}`} />
                          </div>
                          <div>
                            <div className="font-medium text-sm">{user.name}</div>
                            <div className="text-xs text-gray-500">{user.email}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {isSelected && (
                            <Badge variant="secondary" className="text-xs">
                              Selected
                            </Badge>
                          )}
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => handleUserSelect(user)}
                          />
                        </div>
                      </div>
                    );
                  })}
                  {filteredUsers.length > 10 && (
                    <div className="px-3 py-2 text-sm text-gray-500 bg-gray-50 border-t">
                      Showing first 10 results. {filteredUsers.length - 10} more found.
                    </div>
                  )}
                </div>
              ) : (
                <div className="px-3 py-2 text-sm text-gray-500">
                  No users found matching "{searchTerm}"
                </div>
              )}
            </div>
          )}
        </div>
        
        <Select value={selectedRole} onValueChange={setSelectedRole}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="admin">Project Admin</SelectItem>
            <SelectItem value="editor">Contributor</SelectItem>
            <SelectItem value="reader">Reader</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {/* Selected Users to Add */}
      {selectedUsersToAdd.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Selected Users to Add</h4>
            <Button
              onClick={handleAddSelectedUsers}
              className="flex items-center gap-2"
              size="sm"
            >
              <UserPlus className="h-4 w-4" />
              Add {selectedUsersToAdd.length} User{selectedUsersToAdd.length !== 1 ? 's' : ''}
            </Button>
          </div>
          <div className="grid grid-cols-1 gap-2">
            {selectedUsersToAdd.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-2 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <Users className="h-3 w-3 text-white" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">{user.name}</div>
                    <div className="text-xs text-gray-600">{user.email}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="text-xs">
                    {selectedRole === 'admin' ? 'Project Admin' : 
                     selectedRole === 'editor' ? 'Contributor' : 'Reader'}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => handleRemoveSelectedUser(user.id)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Instructions */}
      <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg border border-blue-200">
        <div className="flex items-start space-x-2">
          <Search className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <p className="font-medium text-blue-900">How to add users:</p>
            <ul className="mt-1 space-y-1 text-blue-800">
              <li>• Type a name or email in the search box above</li>
              <li>• Select the role you want to assign</li>
              <li>• Click on users from the dropdown to select them</li>
              <li>• Click "Add X Users" button to confirm and add selected users</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Quick Stats */}
      <div className="text-sm text-gray-500">
        {unassignedUsers.length} user{unassignedUsers.length !== 1 ? 's' : ''} available to add
      </div>
    </div>
  );
}