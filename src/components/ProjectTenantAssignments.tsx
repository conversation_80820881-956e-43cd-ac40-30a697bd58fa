import { useState } from "react";
import { Building, FolderOpen, Search, Plus, X, AlertTriangle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface Tenant {
  id: string;
  name: string;
  description: string;
  storageType: 'shared' | 'dedicated';
  status: 'active' | 'inactive' | 'maintenance';
  endpoint: string;
  azureAccountId: string;
  azureAccountName: string;
  createdAt: string;
  lastActivity: string;
  projectCount: number;
  dataSize: string;
}

interface Project {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'completed' | 'on-hold' | 'archived';
  priority: 'low' | 'medium' | 'high' | 'critical';
}

interface ProjectTenantAssignment {
  id: string;
  projectId: string;
  projectName: string;
  tenantId: string;
  tenantName: string;
  tenantStorageType: 'shared' | 'dedicated';
  assignedDate: string;
  assignedBy: string;
}

export function ProjectTenantAssignments() {
  const [tenants] = useState<Tenant[]>([
    {
      id: "1",
      name: "Agricultural Data Hub",
      description: "Storage for crop monitoring and agricultural satellite imagery",
      storageType: "dedicated",
      status: "active",
      endpoint: "https://agri-data.acme.versaplatform.com",
      azureAccountId: "1",
      azureAccountName: "Production Account - East US",
      createdAt: "2024-01-15",
      lastActivity: "2024-06-18",
      projectCount: 8,
      dataSize: "18.5 TB"
    },
    {
      id: "2",
      name: "Emergency Response Storage",
      description: "Dedicated storage for disaster response and urban planning imagery",
      storageType: "dedicated",
      status: "active",
      endpoint: "https://emergency-data.acme.versaplatform.com",
      azureAccountId: "1",
      azureAccountName: "Production Account - East US",
      createdAt: "2024-02-01",
      lastActivity: "2024-06-17",
      projectCount: 5,
      dataSize: "8.2 TB"
    },
    {
      id: "3",
      name: "Development Sandbox",
      description: "Testing environment for new satellite processing algorithms",
      storageType: "shared",
      status: "active",
      endpoint: "https://dev-sandbox.acme.versaplatform.com",
      azureAccountId: "2",
      azureAccountName: "Development Account - West Europe",
      createdAt: "2024-03-10",
      lastActivity: "2024-06-19",
      projectCount: 3,
      dataSize: "4.1 TB"
    },
    {
      id: "4",
      name: "Archive Data Lake",
      description: "Long-term storage for historical satellite imagery",
      storageType: "dedicated",
      status: "active",
      endpoint: "https://archive-lake.acme.versaplatform.com",
      azureAccountId: "1",
      azureAccountName: "Production Account - East US",
      createdAt: "2024-01-05",
      lastActivity: "2024-06-16",
      projectCount: 8,
      dataSize: "45.2 TB"
    }
  ]);

  const [projects] = useState<Project[]>([
    {
      id: "1",
      name: "Agricultural Monitoring",
      description: "Migrate legacy data to new platform",
      status: "active",
      priority: "high"
    },
    {
      id: "2",
      name: "Disaster Response",
      description: "Integrate with external partner APIs",
      status: "active",
      priority: "medium"
    },
    {
      id: "3",
      name: "Urban Planning",
      description: "Build comprehensive reporting dashboard",
      status: "completed",
      priority: "medium"
    },
    {
      id: "4",
      name: "Mobile App Development",
      description: "Develop mobile application for field data collection",
      status: "on-hold",
      priority: "low"
    },
    {
      id: "5",
      name: "Performance Optimization",
      description: "Optimize system performance",
      status: "active",
      priority: "critical"
    }
  ]);

  const [assignments, setAssignments] = useState<ProjectTenantAssignment[]>([
    {
      id: "1",
      projectId: "1",
      projectName: "Agricultural Monitoring",
      tenantId: "1",
      tenantName: "Agricultural Data Hub",
      tenantStorageType: "dedicated",
      assignedDate: "2024-01-15",
      assignedBy: "John Smith"
    },
    {
      id: "2",
      projectId: "2",
      projectName: "Disaster Response",
      tenantId: "2",
      tenantName: "Emergency Response Storage",
      tenantStorageType: "dedicated",
      assignedDate: "2024-03-01",
      assignedBy: "John Smith"
    },
    {
      id: "3",
      projectId: "3",
      projectName: "Urban Planning",
      tenantId: "4",
      tenantName: "Archive Data Lake",
      tenantStorageType: "dedicated",
      assignedDate: "2024-01-01",
      assignedBy: "John Smith"
    },
  ]);

  const [selectedProject, setSelectedProject] = useState<string>("");
  const [selectedTenant, setSelectedTenant] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState("");
  const [tenantFilter, setTenantFilter] = useState<string>("all");
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);

  const handleAssignTenant = () => {
    if (!selectedProject || !selectedTenant) return;

    const project = projects.find(p => p.id === selectedProject);
    const tenant = tenants.find(t => t.id === selectedTenant);
    if (!project || !tenant) return;

    // Check if project already has a tenant assigned
    const existingAssignment = assignments.find(a => a.projectId === selectedProject);
    if (existingAssignment) {
      // Replace existing assignment
      setAssignments(assignments.map(a => 
        a.projectId === selectedProject
          ? {
              ...a,
              tenantId: selectedTenant,
              tenantName: tenant.name,
              tenantStorageType: tenant.storageType,
              assignedDate: new Date().toISOString().split('T')[0],
              assignedBy: "Current User"
            }
          : a
      ));
    } else {
      // Create new assignment
      const newAssignment: ProjectTenantAssignment = {
        id: Date.now().toString(),
        projectId: selectedProject,
        projectName: project.name,
        tenantId: selectedTenant,
        tenantName: tenant.name,
        tenantStorageType: tenant.storageType,
        assignedDate: new Date().toISOString().split('T')[0],
        assignedBy: "Current User"
      };
      setAssignments([...assignments, newAssignment]);
    }

    setSelectedProject("");
    setSelectedTenant("");
    setIsAssignDialogOpen(false);
  };

  const handleUnassignTenant = (projectId: string) => {
    setAssignments(assignments.filter(a => a.projectId !== projectId));
  };

  const getUnassignedProjects = () => {
    const assignedProjectIds = assignments.map(a => a.projectId);
    return projects.filter(project => 
      !assignedProjectIds.includes(project.id) && 
      project.status === 'active' &&
      (searchTerm === "" || project.name.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const getProjectsForTenant = (tenantId: string) => {
    return assignments.filter(a => a.tenantId === tenantId);
  };

  const getAvailableTenants = () => {
    return tenants.filter(tenant => 
      tenant.status === 'active' &&
      (searchTerm === "" || tenant.name.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const filteredAssignments = assignments.filter(assignment => {
    const matchesTenant = tenantFilter === "all" || assignment.tenantId === tenantFilter;
    return matchesTenant;
  });

  const getStorageTypeBadge = (storageType: 'shared' | 'dedicated') => {
    const colors: Record<'shared' | 'dedicated', string> = {
      shared: 'bg-blue-100 text-blue-800',
      dedicated: 'bg-green-100 text-green-800'
    };
    return <Badge className={colors[storageType]}>{storageType}</Badge>;
  };

  const getPriorityBadge = (priority: 'low' | 'medium' | 'high' | 'critical') => {
    const colors: Record<'low' | 'medium' | 'high' | 'critical', string> = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      critical: 'bg-red-100 text-red-800'
    };
    return <Badge className={colors[priority]}>{priority}</Badge>;
  };

  const hasExistingAssignment = (projectId: string) => {
    return assignments.some(a => a.projectId === projectId);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Project Tenant Assignments</h1>
          <p className="text-gray-600 mt-2">Assign tenants to projects (one tenant per project)</p>
        </div>
        <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Assign Tenant
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Assign Tenant to Project</DialogTitle>
              <DialogDescription>
                Select a project and tenant. Each project can have only one tenant.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="project">Select Project</Label>
                  <Select value={selectedProject} onValueChange={setSelectedProject}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a project" />
                    </SelectTrigger>
                    <SelectContent>
                      {projects.filter(p => p.status === 'active').map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          <div className="flex items-center justify-between w-full">
                            <span>{project.name}</span>
                            {hasExistingAssignment(project.id) && (
                              <Badge variant="outline" className="ml-2 text-xs">
                                Has Tenant
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {selectedProject && hasExistingAssignment(selectedProject) && (
                    <div className="flex items-start space-x-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                      <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                      <div className="text-sm text-yellow-800">
                        <p className="font-medium">Project Already Has Tenant</p>
                        <p>Assigning a new tenant will replace the existing assignment.</p>
                      </div>
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tenant">Select Tenant</Label>
                  <Select value={selectedTenant} onValueChange={setSelectedTenant}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a tenant" />
                    </SelectTrigger>
                    <SelectContent>
                      {getAvailableTenants().map((tenant) => (
                        <SelectItem key={tenant.id} value={tenant.id}>
                          <div className="flex items-center space-x-2">
                            <span>{tenant.name}</span>
                            {getStorageTypeBadge(tenant.storageType)}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {selectedProject && selectedTenant && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h3 className="font-medium text-blue-900 mb-2">Assignment Summary</h3>
                  <div className="text-sm text-blue-800">
                    <p><strong>Project:</strong> {projects.find(p => p.id === selectedProject)?.name}</p>
                    <p><strong>Tenant:</strong> {tenants.find(t => t.id === selectedTenant)?.name}</p>
                    <p><strong>Storage Type:</strong> {tenants.find(t => t.id === selectedTenant)?.storageType}</p>
                  </div>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleAssignTenant}
                disabled={!selectedProject || !selectedTenant}
              >
                {selectedProject && hasExistingAssignment(selectedProject) ? 'Replace Assignment' : 'Assign Tenant'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="assignments" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="assignments">All Assignments</TabsTrigger>
          <TabsTrigger value="by-tenant">By Tenant</TabsTrigger>
          <TabsTrigger value="unassigned">Unassigned Projects</TabsTrigger>
        </TabsList>

        <TabsContent value="assignments" className="space-y-4">
          <div className="flex items-center space-x-4">
            <Select value={tenantFilter} onValueChange={setTenantFilter}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Tenants</SelectItem>
                {tenants.map((tenant) => (
                  <SelectItem key={tenant.id} value={tenant.id}>
                    {tenant.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Project-Tenant Assignments</CardTitle>
              <CardDescription>
                {filteredAssignments.length} assignment{filteredAssignments.length !== 1 ? 's' : ''} total
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Project</TableHead>
                    <TableHead>Tenant</TableHead>
                    <TableHead>Environment</TableHead>
                    <TableHead>Assigned Date</TableHead>
                    <TableHead>Assigned By</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssignments.map((assignment) => (
                    <TableRow key={assignment.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <FolderOpen className="h-4 w-4 text-purple-600" />
                          </div>
                          <div>
                            <div className="font-medium">{assignment.projectName}</div>
                            <div className="text-sm text-gray-500">
                              {projects.find(p => p.id === assignment.projectId)?.description}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <Building className="h-4 w-4 text-green-600" />
                          </div>
                          <div>
                            <div className="font-medium">{assignment.tenantName}</div>
                            <div className="text-sm text-gray-500">
                              {tenants.find(t => t.id === assignment.tenantId)?.description}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStorageTypeBadge(assignment.tenantStorageType)}
                      </TableCell>
                      <TableCell>{assignment.assignedDate}</TableCell>
                      <TableCell>{assignment.assignedBy}</TableCell>
                      <TableCell>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <X className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Unassign Tenant</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to unassign "{assignment.tenantName}" from "{assignment.projectName}"?
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleUnassignTenant(assignment.projectId)}>
                                Unassign
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="by-tenant" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Assignments by Tenant</CardTitle>
              <CardDescription>
                View project assignments organized by tenant
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {tenants.map((tenant) => {
                  const tenantProjects = getProjectsForTenant(tenant.id);
                  return (
                    <Card key={tenant.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                              <Building className="h-4 w-4 text-green-600" />
                            </div>
                            <div>
                              <h3 className="font-semibold">{tenant.name}</h3>
                              <p className="text-sm text-gray-500">{tenant.description}</p>
                              <div className="mt-1">
                                {getStorageTypeBadge(tenant.storageType)}
                              </div>
                            </div>
                          </div>
                          <Badge variant="outline">{tenantProjects.length} projects</Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {tenantProjects.length > 0 ? (
                          <div className="space-y-3">
                            {tenantProjects.map((assignment) => {
                              const project = projects.find(p => p.id === assignment.projectId);
                              return (
                                <div key={assignment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center">
                                      <FolderOpen className="h-3 w-3 text-purple-600" />
                                    </div>
                                    <div>
                                      <div className="font-medium">{assignment.projectName}</div>
                                      <div className="text-sm text-gray-500">Assigned on {assignment.assignedDate}</div>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    {project && getPriorityBadge(project.priority)}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        ) : (
                          <p className="text-gray-500 text-center py-4">No projects assigned to this tenant</p>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="unassigned" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Unassigned Projects</CardTitle>
              <CardDescription>
                Active projects that don't have a tenant assigned
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {getUnassignedProjects().length > 0 ? (
                  getUnassignedProjects().map((project) => (
                    <div key={project.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                          <FolderOpen className="h-4 w-4 text-purple-600" />
                        </div>
                        <div>
                          <div className="font-medium">{project.name}</div>
                          <div className="text-sm text-gray-500">{project.description}</div>
                          <div className="mt-1">
                            {getPriorityBadge(project.priority)}
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedProject(project.id);
                          setIsAssignDialogOpen(true);
                        }}
                      >
                        Assign Tenant
                      </Button>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">All active projects have tenants assigned</p>
                    <p className="text-sm text-gray-400">Great job keeping your projects organized!</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
