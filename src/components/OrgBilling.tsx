
import { useState } from "react";
import { Download, Calendar, DollarSign, TrendingUp, Building2, Clock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export function OrgBilling() {
  const [selectedPeriod, setSelectedPeriod] = useState("current");

  const mockTenantUsage = [
    {
      id: 1,
      name: "Production Environment",
      usage: 145.50,
      dataProcessed: "2.4TB",
      apiCalls: "1.2M",
      storageUsed: "450GB"
    },
    {
      id: 2,
      name: "Staging Environment",
      usage: 89.25,
      dataProcessed: "0.8TB",
      apiCalls: "450K",
      storageUsed: "180GB"
    },
    {
      id: 3,
      name: "Development Environment",
      usage: 45.75,
      dataProcessed: "0.3TB",
      apiCalls: "200K",
      storageUsed: "85GB"
    }
  ];

  const mockBillingHistory = [
    { period: "Dec 2024", amount: 280.50, status: "Current", dueDate: "Jan 15, 2025" },
    { period: "Nov 2024", amount: 265.25, status: "Paid", dueDate: "Dec 15, 2024" },
    { period: "Oct 2024", amount: 298.75, status: "Paid", dueDate: "Nov 15, 2024" },
    { period: "Sep 2024", amount: 312.00, status: "Paid", dueDate: "Oct 15, 2024" }
  ];

  const currentTotal = mockTenantUsage.reduce((sum, tenant) => sum + tenant.usage, 0);
  const totalDataProcessed = mockTenantUsage.reduce((sum, tenant) => sum + parseFloat(tenant.dataProcessed), 0);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold dashboard-title">Billing & Usage</h2>
          <p className="text-gray-600">Monitor your organization's usage and billing</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            December 2024
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Download Invoice
          </Button>
        </div>
      </div>

      {/* Current Usage Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Bill</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${currentTotal.toFixed(2)}</div>
            <p className="text-xs text-blue-600 font-medium">December 2024</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockTenantUsage.length}</div>
            <p className="text-xs text-green-600 font-medium">All environments</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Data Processed</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDataProcessed.toFixed(1)}TB</div>
            <p className="text-xs text-green-600 font-medium">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Next Due</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Jan 15</div>
            <p className="text-xs text-orange-600 font-medium">26 days remaining</p>
          </CardContent>
        </Card>
      </div>

      {/* Current Period Usage by Tenant */}
      <Card>
        <CardHeader>
          <CardTitle>Current Period Usage</CardTitle>
          <CardDescription>Usage breakdown by tenant for December 2024</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tenant</TableHead>
                <TableHead>Usage Cost</TableHead>
                <TableHead>Data Processed</TableHead>
                <TableHead>API Calls</TableHead>
                <TableHead>Storage</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockTenantUsage.map((tenant) => (
                <TableRow key={tenant.id}>
                  <TableCell>
                    <div className="font-medium">{tenant.name}</div>
                  </TableCell>
                  <TableCell className="font-medium">${tenant.usage.toFixed(2)}</TableCell>
                  <TableCell>{tenant.dataProcessed}</TableCell>
                  <TableCell>{tenant.apiCalls}</TableCell>
                  <TableCell>{tenant.storageUsed}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>Previous billing periods and payment status</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Billing Period</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockBillingHistory.map((bill, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{bill.period}</TableCell>
                  <TableCell>${bill.amount.toFixed(2)}</TableCell>
                  <TableCell>{bill.dueDate}</TableCell>
                  <TableCell>
                    <Badge variant={
                      bill.status === 'Paid' ? 'default' : 
                      bill.status === 'Current' ? 'secondary' : 'destructive'
                    }>
                      {bill.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
