
import { useState } from "react";
import { Plus, Search, MoreH<PERSON>zon<PERSON>, Edit, Trash2, Building2, Globe } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { OrganizationsManagement } from "./OrganizationsManagement";
import { JobLibrariesManagement } from "./JobLibrariesManagement";
import { LibraryAssignments } from "./LibraryAssignments";
import { JobDefinitionsManagement } from "./JobDefinitionsManagement";
import { GlobalUsersManagement } from "./GlobalUsersManagement";

interface RootAdminManagementProps {
  section: string;
  userRole?: 'global_admin' | 'org_admin' | 'project_admin' | 'contributor' | 'reader';
  userEmail?: string;
}

export function RootAdminManagement({ section, userRole = 'global_admin', userEmail }: RootAdminManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");

  const mockOrganizations = [
    {
      id: 1,
      name: "Versar Inc",
      description: "Portal administrator and satellite image processing company",
      tenants: 8,
      projects: 23,
      users: 45,
      status: "active",
      created: "2024-01-01",
      isInternal: true
    },
    {
      id: 2,
      name: "Acme Corporation",
      description: "Leading manufacturing company - Customer organization",
      tenants: 12,
      projects: 45,
      users: 156,
      status: "active",
      created: "2024-01-15",
      isInternal: false
    }
  ];


  const renderOrganizations = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Organization Management</h2>
          <p className="text-gray-600">Manage organizations and their configurations</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Organization
        </Button>
      </div>

      <div className="flex items-center space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search organizations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <div className="grid gap-4">
        {mockOrganizations.map((org) => (
          <Card key={org.id}>
            <CardHeader className="flex flex-row items-start justify-between">
              <div className="flex items-start space-x-3">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Building2 className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="flex items-center gap-2">
                    {org.name}
                    <Badge variant={org.status === 'active' ? 'default' : 'secondary'}>
                      {org.status}
                    </Badge>
                    {org.isInternal && (
                      <Badge variant="outline">Internal</Badge>
                    )}
                  </CardTitle>
                  <CardDescription>{org.description}</CardDescription>
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Globe className="mr-2 h-4 w-4" />
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Tenants</span>
                  <p className="text-2xl font-bold text-blue-600">{org.tenants}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Projects</span>
                  <p className="text-2xl font-bold text-green-600">{org.projects}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Users</span>
                  <p className="text-2xl font-bold text-purple-600">{org.users}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Created</span>
                  <p className="text-sm text-gray-500">{org.created}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );



  switch (section) {
    case 'organizations':
      return <OrganizationsManagement userRole={userRole} userEmail={userEmail} />;
    case 'job-libraries':
      return <JobLibrariesManagement />;
    case 'library-assignments':
      return <LibraryAssignments />;
    case 'job-definitions':
      return <JobDefinitionsManagement />;
    case 'global-users':
      return <GlobalUsersManagement />;
    default:
      return <OrganizationsManagement userRole={userRole} userEmail={userEmail} />;
  }
}
