import { useState } from "react";
import { Plus, Building2, Edit, Trash2, <PERSON>, Eye, Shield, Loader2, CheckCircle, Copy, Check, Search, UserPlus, X, Package } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface Organization {
  id: string;
  name: string;
  description: string;
  configTemplate: 'basic-commercial' | 'standard-commercial' | 'premium-commercial' | 'basic-government' | 'standard-government' | 'premium-government';
  adminUserId?: string;
  adminUserName?: string;
  adminUserEmail?: string;
  createdAt: string;
  jobLibraries: string[];
  serviceBundles: string[];
}


interface OrganizationsManagementProps {
  userRole: 'global_admin' | 'org_admin' | 'project_admin' | 'contributor' | 'reader';
  userEmail?: string;
}

export function OrganizationsManagement({ userRole, userEmail }: OrganizationsManagementProps) {
  // Permission helpers
  const canCreateOrganization = userRole === 'global_admin';
  const canEditOrganization = userRole === 'global_admin';
  const canDeleteOrganization = userRole === 'global_admin';
  const canAssignRoles = userRole === 'org_admin';
  const canViewDetails = true; // All roles can view

  const [allOrganizations] = useState<Organization[]>([
    {
      id: "1",
      name: "Versar Inc",
      description: "Portal administrator and satellite image processing company",
      configTemplate: "premium-commercial",
      adminUserId: "admin1",
      adminUserName: "Admin User",
      adminUserEmail: "<EMAIL>",
      createdAt: "2024-01-01",
      jobLibraries: ["satellite-processing", "data-analysis", "reporting"],
      serviceBundles: ["Advanced Analytics", "Real-time Processing", "Enterprise Support"]
    },
    {
      id: "2",
      name: "Acme Corporation",
      description: "Leading manufacturing company - Customer organization",
      configTemplate: "basic-commercial",
      adminUserId: "admin2",
      adminUserName: "John Smith",
      adminUserEmail: "<EMAIL>",
      createdAt: "2024-01-15",
      jobLibraries: ["data-processing", "reporting"],
      serviceBundles: ["Basic Analytics", "Standard Support"]
    }
  ]);

  // Available users for administrator assignment
  const [availableUsers] = useState([
    { id: "1", name: "Alice Johnson", email: "<EMAIL>" },
    { id: "2", name: "John Smith", email: "<EMAIL>" },
    { id: "3", name: "Mike Wilson", email: "<EMAIL>" },
    { id: "4", name: "Sarah Davis", email: "<EMAIL>" },
    { id: "5", name: "Tom Reader", email: "<EMAIL>" },
    { id: "6", name: "Lisa Chen", email: "<EMAIL>" },
    { id: "7", name: "David Park", email: "<EMAIL>" }
  ]);

  // Filter organizations based on user role and permissions
  const getFilteredOrganizations = () => {
    if (userRole === 'global_admin') {
      return allOrganizations;
    }
    if (userRole === 'org_admin') {
      return allOrganizations.filter(org => org.adminUserEmail === userEmail);
    }
    return [];
  };

  const [organizationList, setOrganizationList] = useState<Organization[]>(getFilteredOrganizations());

  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isAssignRoleDialogOpen, setIsAssignRoleDialogOpen] = useState(false);
  const [isServiceBundleDialogOpen, setIsServiceBundleDialogOpen] = useState(false);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [isCreationComplete, setIsCreationComplete] = useState(false);
  const [newOrganization, setNewOrganization] = useState<Organization | null>(null);



  const [formData, setFormData] = useState<Partial<Organization>>({
    name: "",
    description: "",
    configTemplate: "basic-commercial"
  });
  
  const [createOrgAdminEmail, setCreateOrgAdminEmail] = useState<string>("");

  const handleCreate = async () => {
    // For simplicity, we'll use the email as the admin user info
    // In a real application, this would validate the email and create/link the user
    
    // Define service bundles based on configuration template
    const getServiceBundlesForTemplate = (template: string) => {
      switch (template) {
        case 'basic-commercial':
        case 'basic-government':
          return ["Basic Analytics", "Standard Support"];
        case 'standard-commercial':
        case 'standard-government':
          return ["Standard Analytics", "Real-time Processing", "Premium Support"];
        case 'premium-commercial':
        case 'premium-government':
          return ["Advanced Analytics", "Real-time Processing", "Enterprise Support", "24/7 Support"];
        default:
          return ["Basic Analytics", "Standard Support"];
      }
    };

    const newOrg: Organization = {
      id: Date.now().toString(),
      name: formData.name || "",
      description: formData.description || "",
      configTemplate: formData.configTemplate || "basic-commercial",
      adminUserId: undefined,
      adminUserName: undefined,
      adminUserEmail: createOrgAdminEmail,
      createdAt: new Date().toISOString().split('T')[0],
      jobLibraries: [],
      serviceBundles: getServiceBundlesForTemplate(formData.configTemplate || "basic-commercial")
    };

    setOrganizationList([...organizationList, newOrg]);
    setNewOrganization(newOrg);
    setFormData({
      name: "",
      description: "",
      configTemplate: "basic-commercial"
    });
    setCreateOrgAdminEmail("");
    setIsCreateDialogOpen(false);
    
    // Show status dialog
    setIsCreationComplete(false);
    setIsStatusDialogOpen(true);
    
    // Simulate organization creation process
    setTimeout(() => {
      setIsCreationComplete(true);
    }, 10000);
  };

  const handleEdit = () => {
    if (!selectedOrg) return;

    const updatedOrgs = organizationList.map(org =>
      org.id === selectedOrg.id
        ? {
            ...org,
            name: formData.name || org.name,
            description: formData.description || org.description,
            configTemplate: formData.configTemplate || org.configTemplate
          }
        : org
    );

    setOrganizationList(updatedOrgs);
    setIsEditDialogOpen(false);
    setSelectedOrg(null);
  };

  const handleDelete = (orgId: string) => {
    setOrganizationList(organizationList.filter(org => org.id !== orgId));
  };





  return (
    <div className="p-6 space-y-6">
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Create New Organization</DialogTitle>
              <DialogDescription>
                Set up a new organization with basic information and administrator email
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter organization name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Enter organization description"
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="configTemplate">Configuration Template</Label>
                <Select
                  value={formData.configTemplate}
                  onValueChange={(value) => setFormData({ ...formData, configTemplate: value as 'basic-commercial' | 'standard-commercial' | 'premium-commercial' | 'basic-government' | 'standard-government' | 'premium-government' })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select configuration template" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="basic-commercial">Basic Commercial</SelectItem>
                    <SelectItem value="standard-commercial">Standard Commercial</SelectItem>
                    <SelectItem value="premium-commercial">Premium Commercial</SelectItem>
                    <SelectItem value="basic-government">Basic Government</SelectItem>
                    <SelectItem value="standard-government">Standard Government</SelectItem>
                    <SelectItem value="premium-government">Premium Government</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="admin-email">Administrator Email</Label>
                <Input
                  id="admin-email"
                  type="email"
                  value={createOrgAdminEmail}
                  onChange={(e) => setCreateOrgAdminEmail(e.target.value)}
                  placeholder="Enter administrator email address"
                />
                <p className="text-sm text-gray-500">This person will receive administrative access to the organization</p>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => {
                setIsCreateDialogOpen(false);
                setFormData({ name: "", description: "", configTemplate: "basic-commercial" });
                setCreateOrgAdminEmail("");
              }}>
                Cancel
              </Button>
              <Button onClick={handleCreate} disabled={!formData.name || !createOrgAdminEmail}>
                Create Organization
              </Button>
            </DialogFooter>
          </DialogContent>
      </Dialog>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Organizations</CardTitle>
              <CardDescription>
                {organizationList.length} organization{organizationList.length !== 1 ? 's' : ''} total
              </CardDescription>
            </div>
            {canCreateOrganization && (
              <Button 
                className="flex items-center gap-2"
                onClick={() => setIsCreateDialogOpen(true)}
              >
                <Plus className="h-4 w-4" />
                Add
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {organizationList.length === 0 ? (
            <div className="text-center py-12">
              <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No organizations {canCreateOrganization ? 'created' : 'available'}</h3>
              {canCreateOrganization && (
                <Button onClick={() => setIsCreateDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Organization
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Configuration</TableHead>
                  <TableHead>Administrator</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                {organizationList.map((org) => (
                <TableRow key={org.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Building2 className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium">{org.name}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Badge variant={org.configTemplate.includes('government') ? 'default' : 'secondary'}>
                        {org.configTemplate.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    {org.adminUserName ? (
                      <div>
                        <div className="font-medium">{org.adminUserName}</div>
                        <div className="text-sm text-muted-foreground">{org.adminUserEmail}</div>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">Not assigned</span>
                    )}
                  </TableCell>
                  <TableCell>{org.createdAt}</TableCell>
                  <TableCell className="text-center">
                    <TooltipProvider>
                      <div className="flex items-center justify-center space-x-2">
                        {canViewDetails && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedOrg(org);
                                  setIsViewDialogOpen(true);
                                }}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>View Details</TooltipContent>
                          </Tooltip>
                        )}
                        {canAssignRoles && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-blue-600 hover:text-blue-700"
                                onClick={() => {
                                  setSelectedOrg(org);
                                  setIsAssignRoleDialogOpen(true);
                                }}
                              >
                                <Shield className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Assign User</TooltipContent>
                          </Tooltip>
                        )}
                        {canEditOrganization && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-green-600 hover:text-green-700"
                                onClick={() => {
                                  setSelectedOrg(org);
                                  setIsServiceBundleDialogOpen(true);
                                }}
                              >
                                <Package className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Service Bundles</TooltipContent>
                          </Tooltip>
                        )}
                        {canEditOrganization && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedOrg(org);
                                  setFormData({
                                    name: org.name,
                                    description: org.description,
                                    configTemplate: org.configTemplate,
                                    adminUserEmail: org.adminUserEmail || ""
                                  });
                                  setIsEditDialogOpen(true);
                                }}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Edit Organization</TooltipContent>
                          </Tooltip>
                        )}
                        {canDeleteOrganization && (
                          <AlertDialog>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <AlertDialogTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </AlertDialogTrigger>
                              </TooltipTrigger>
                              <TooltipContent>Delete Organization</TooltipContent>
                            </Tooltip>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Organization</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete "{org.name}"? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleDelete(org.id)}>
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                      </div>
                    </TooltipProvider>
                  </TableCell>
                </TableRow>
              ))}
              </TableBody>
            </Table>
          )}
          </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Organization</DialogTitle>
            <DialogDescription>
              Update organization information
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter organization name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter organization description"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-configTemplate">Configuration Template</Label>
              <Select
                value={formData.configTemplate}
                onValueChange={(value) => setFormData({ ...formData, configTemplate: value as 'basic-commercial' | 'standard-commercial' | 'premium-commercial' | 'basic-government' | 'standard-government' | 'premium-government' })}
                disabled
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select configuration template" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="basic-commercial">Basic Commercial</SelectItem>
                  <SelectItem value="standard-commercial">Standard Commercial</SelectItem>
                  <SelectItem value="premium-commercial">Premium Commercial</SelectItem>
                  <SelectItem value="basic-government">Basic Government</SelectItem>
                  <SelectItem value="standard-government">Standard Government</SelectItem>
                  <SelectItem value="premium-government">Premium Government</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-admin-email">Administrator Email</Label>
              <Input
                id="edit-admin-email"
                type="email"
                value={formData.adminUserEmail}
                onChange={(e) => setFormData({ ...formData, adminUserEmail: e.target.value })}
                placeholder="Enter administrator email address"
                required
              />
              <p className="text-sm text-gray-500">This person will receive administrative access to the organization</p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEdit} disabled={!formData.name || !formData.adminUserEmail}>Update Organization</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Organization Details</DialogTitle>
            <DialogDescription>
              View complete organization information
            </DialogDescription>
          </DialogHeader>
          {selectedOrg && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-sm text-gray-700 mb-2">Basic Information</h3>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium">Name:</span>
                      <p className="text-sm text-gray-600">{selectedOrg.name}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Description:</span>
                      <p className="text-sm text-gray-600">{selectedOrg.description}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Configuration Template:</span>
                      <div className="mt-1">
                        <Badge variant="outline">
                          {selectedOrg.configTemplate.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Created:</span>
                      <p className="text-sm text-gray-600">{selectedOrg.createdAt}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-sm text-gray-700 mb-2">Administrator</h3>
                  {selectedOrg.adminUserEmail ? (
                    <div className="space-y-2">
                      <div>
                        <span className="text-sm font-medium">Email:</span>
                        <p className="text-sm text-gray-600">{selectedOrg.adminUserEmail}</p>
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No administrator assigned</p>
                  )}
                </div>
                <div>
                  <h3 className="font-semibold text-sm text-gray-700 mb-2">Service Bundles</h3>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium">Assigned Bundles:</span>
                      <p className="text-sm text-gray-600">
                        {selectedOrg.serviceBundles.length > 0 ? selectedOrg.serviceBundles.join(', ') : 'None assigned'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Assign Role Dialog */}
      <Dialog open={isAssignRoleDialogOpen} onOpenChange={setIsAssignRoleDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Assign User</DialogTitle>
            <DialogDescription>
              Manage users who have access to {selectedOrg?.name}
            </DialogDescription>
          </DialogHeader>
          {selectedOrg && (
            <AssignRoleContent
              organization={selectedOrg}
              onClose={() => setIsAssignRoleDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Service Bundle Assignment Dialog */}
      <Dialog open={isServiceBundleDialogOpen} onOpenChange={setIsServiceBundleDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Service Bundle Assignment</DialogTitle>
            <DialogDescription>
              Manage service bundles for {selectedOrg?.name}
            </DialogDescription>
          </DialogHeader>
          {selectedOrg && (
            <ServiceBundleContent
              organization={selectedOrg}
              onClose={() => setIsServiceBundleDialogOpen(false)}
              onUpdate={(updatedOrg) => {
                setOrganizationList(organizationList.map(org => 
                  org.id === updatedOrg.id ? updatedOrg : org
                ));
                setSelectedOrg(updatedOrg);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Organization Creation Status Dialog */}
      <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Organization Status</DialogTitle>
          </DialogHeader>
          <OrganizationStatusContent 
            organization={newOrganization}
            isComplete={isCreationComplete}
            onClose={() => setIsStatusDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

    </div>
  );
}

// Assign Role Content Component
function AssignRoleContent({ 
  organization, 
  onClose 
}: {
  organization: Organization;
  onClose: () => void;
}) {
  const [availableUsers] = useState([
    { id: "1", name: "Alice Johnson", email: "<EMAIL>" },
    { id: "2", name: "John Smith", email: "<EMAIL>" },
    { id: "3", name: "Mike Wilson", email: "<EMAIL>" },
    { id: "4", name: "Sarah Davis", email: "<EMAIL>" },
    { id: "5", name: "Tom Reader", email: "<EMAIL>" },
    { id: "6", name: "Lisa Chen", email: "<EMAIL>" },
    { id: "7", name: "David Park", email: "<EMAIL>" },
  ]);

  // Mock assigned users (in real app, this would come from props or API)
  const [selectedUsers, setSelectedUsers] = useState([
    { id: "2", name: "John Smith", email: "<EMAIL>", role: "org_admin" },
    { id: "5", name: "Tom Reader", email: "<EMAIL>", role: "reader" },
  ]);
  const [userRoles, setUserRoles] = useState<Record<string, string>>({
    "2": "org_admin",
    "5": "reader"
  });

  const handleUserToggle = (user: any, checked: boolean, role: string = 'reader') => {
    if (checked) {
      setSelectedUsers([...selectedUsers, { ...user, role }]);
      setUserRoles({ ...userRoles, [user.id]: role });
    } else {
      setSelectedUsers(selectedUsers.filter(u => u.id !== user.id));
      const newRoles = { ...userRoles };
      delete newRoles[user.id];
      setUserRoles(newRoles);
    }
  };

  const handleRoleChange = (userId: string, role: string) => {
    setUserRoles({ ...userRoles, [userId]: role });
    setSelectedUsers(selectedUsers.map(user => 
      user.id === userId ? { ...user, role } : user
    ));
  };

  const handleRemoveUser = (userId: string) => {
    setSelectedUsers(selectedUsers.filter(u => u.id !== userId));
    const newRoles = { ...userRoles };
    delete newRoles[userId];
    setUserRoles(newRoles);
  };

  const handleSave = () => {
    // Here we would save the role assignments
    console.log('Assigning roles:', { organizationId: organization.id, assignments: userRoles });
    onClose();
  };

  // Get users not currently assigned to organization
  const unassignedUsers = availableUsers.filter(user => 
    !selectedUsers.some(assigned => assigned.id === user.id)
  );

  return (
    <div className="space-y-6">
      {/* Currently Assigned Users */}
      <div className="space-y-4">
        <h3 className="font-semibold">Users with Access</h3>
        {selectedUsers.length > 0 ? (
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead className="w-12">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {selectedUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Select value={userRoles[user.id] || 'reader'} onValueChange={(value) => handleRoleChange(user.id, value)}>
                        <SelectTrigger className="w-40">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="reader">Reader</SelectItem>
                          <SelectItem value="contributor">Contributor</SelectItem>
                          <SelectItem value="project_admin">Project Admin</SelectItem>
                          <SelectItem value="org_admin">Organization Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveUser(user.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-8 border rounded-lg border-dashed">
            <Users className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No users assigned to this organization</p>
          </div>
        )}
      </div>

      {/* Add New Users */}
      {unassignedUsers.length > 0 && (
        <OrgAddUserSection 
          unassignedUsers={unassignedUsers}
          onAddUser={(user, checked, role) => handleUserToggle(user, checked, role)}
        />
      )}

      <DialogFooter>
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={handleSave}>
          Update Access
        </Button>
      </DialogFooter>
    </div>
  );
}

// Organization Status Content Component
function OrganizationStatusContent({ 
  organization, 
  isComplete, 
  onClose 
}: {
  organization: Organization | null;
  isComplete: boolean;
  onClose: () => void;
}) {
  const [credentialsCopied, setCredentialsCopied] = useState(false);
  
  const handleCopyCredentials = async () => {
    if (!organization) return;
    
    // Mock credentials - in real app, this would be actual generated credentials
    const credentials = {
      email: organization.adminUserEmail,
      temporaryPassword: "TempPass123!",
      loginUrl: "https://portal.example.com/login",
      organizationId: organization.id
    };
    
    const credentialsText = `Organization Administrator Credentials
    
Organization: ${organization.name}
Email: ${credentials.email}
Temporary Password: ${credentials.temporaryPassword}
Login URL: ${credentials.loginUrl}
Organization ID: ${credentials.organizationId}

Please change your password after first login.`;
    
    try {
      await navigator.clipboard.writeText(credentialsText);
      setCredentialsCopied(true);
      setTimeout(() => setCredentialsCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy credentials:', err);
    }
  };

  if (!organization) return null;

  return (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        {!isComplete ? (
          <>
            <div className="flex items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            </div>
            <p className="text-lg font-bold text-gray-900">
              Organization is being created...
            </p>
          </>
        ) : (
          <>
            <div className="flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <p className="text-lg font-bold text-gray-900">
              Organization created successfully!
            </p>
          </>
        )}
      </div>
      
      {isComplete && (
        <div className="space-y-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <p className="text-sm text-gray-700">
              <strong>Administrator ({organization.adminUserEmail})</strong> is assigned to the organization. 
              Administrator credentials and instructions are sent by email.
            </p>
          </div>
          
          <div className="flex justify-center">
            <Button
              onClick={handleCopyCredentials}
              variant="outline"
              className="flex items-center gap-2"
            >
              {credentialsCopied ? (
                <>
                  <Check className="h-4 w-4" />
                  Credentials Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4" />
                  Copy Credentials
                </>
              )}
            </Button>
          </div>
        </div>
      )}
      
      <DialogFooter>
        <Button onClick={onClose} disabled={!isComplete}>
          {isComplete ? 'Done' : 'Please wait...'}
        </Button>
      </DialogFooter>
    </div>
  );
}

// Organization Add User Section Component with Search
function OrgAddUserSection({ 
  unassignedUsers, 
  onAddUser 
}: {
  unassignedUsers: any[];
  onAddUser: (user: any, checked: boolean, role?: string) => void;
}) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState<string>("reader");
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [selectedUsersToAdd, setSelectedUsersToAdd] = useState<any[]>([]);

  const filteredUsers = unassignedUsers.filter(user => 
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleUserSelect = (user: any) => {
    const isSelected = selectedUsersToAdd.some(u => u.id === user.id);
    if (isSelected) {
      setSelectedUsersToAdd(selectedUsersToAdd.filter(u => u.id !== user.id));
    } else {
      setSelectedUsersToAdd([...selectedUsersToAdd, user]);
    }
  };

  const handleAddSelectedUsers = () => {
    selectedUsersToAdd.forEach(user => {
      onAddUser(user, true, selectedRole);
    });
    setSelectedUsersToAdd([]);
    setSearchTerm("");
    setIsSearchOpen(false);
  };

  const handleRemoveSelectedUser = (userId: string) => {
    setSelectedUsersToAdd(selectedUsersToAdd.filter(u => u.id !== userId));
  };

  return (
    <div className="space-y-4">
      <h3 className="font-semibold">Add Users</h3>
      
      {/* Search and Role Selection */}
      <div className="flex gap-3">
        <div className="flex-1 relative">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search users by name or email..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setIsSearchOpen(e.target.value.length > 0);
              }}
              onFocus={() => setIsSearchOpen(searchTerm.length > 0)}
              className="pl-10"
            />
          </div>
          
          {/* Search Results Dropdown */}
          {isSearchOpen && searchTerm.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-64 overflow-y-auto">
              {filteredUsers.length > 0 ? (
                <div className="py-1">
                  {filteredUsers.slice(0, 10).map((user) => {
                    const isSelected = selectedUsersToAdd.some(u => u.id === user.id);
                    return (
                      <div
                        key={user.id}
                        className={`px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center justify-between ${
                          isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                        }`}
                        onClick={() => handleUserSelect(user)}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            isSelected ? 'bg-blue-500' : 'bg-blue-100'
                          }`}>
                            <Users className={`h-4 w-4 ${isSelected ? 'text-white' : 'text-blue-600'}`} />
                          </div>
                          <div>
                            <div className="font-medium text-sm">{user.name}</div>
                            <div className="text-xs text-gray-500">{user.email}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {isSelected && (
                            <Badge variant="secondary" className="text-xs">
                              Selected
                            </Badge>
                          )}
                          <Checkbox
                            checked={isSelected}
                            onChange={() => handleUserSelect(user)}
                          />
                        </div>
                      </div>
                    );
                  })}
                  {filteredUsers.length > 10 && (
                    <div className="px-3 py-2 text-sm text-gray-500 bg-gray-50 border-t">
                      Showing first 10 results. {filteredUsers.length - 10} more found.
                    </div>
                  )}
                </div>
              ) : (
                <div className="px-3 py-2 text-sm text-gray-500">
                  No users found matching "{searchTerm}"
                </div>
              )}
            </div>
          )}
        </div>
        
        <Select value={selectedRole} onValueChange={setSelectedRole}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="reader">Reader</SelectItem>
            <SelectItem value="contributor">Contributor</SelectItem>
            <SelectItem value="project_admin">Project Admin</SelectItem>
            <SelectItem value="org_admin">Organization Admin</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {/* Selected Users to Add */}
      {selectedUsersToAdd.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Selected Users to Add</h4>
            <Button
              onClick={handleAddSelectedUsers}
              className="flex items-center gap-2"
              size="sm"
            >
              <UserPlus className="h-4 w-4" />
              Add {selectedUsersToAdd.length} User{selectedUsersToAdd.length !== 1 ? 's' : ''}
            </Button>
          </div>
          <div className="grid grid-cols-1 gap-2">
            {selectedUsersToAdd.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-2 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <Users className="h-3 w-3 text-white" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">{user.name}</div>
                    <div className="text-xs text-gray-600">{user.email}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="text-xs">
                    {selectedRole === 'org_admin' ? 'Organization Admin' : 
                     selectedRole === 'project_admin' ? 'Project Admin' : 
                     selectedRole === 'contributor' ? 'Contributor' : 'Reader'}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => handleRemoveSelectedUser(user.id)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Instructions */}
      <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg border border-blue-200">
        <div className="flex items-start space-x-2">
          <Search className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <p className="font-medium text-blue-900">How to add users:</p>
            <ul className="mt-1 space-y-1 text-blue-800">
              <li>• Type a name or email in the search box above</li>
              <li>• Select the role you want to assign</li>
              <li>• Click on users from the dropdown to select them</li>
              <li>• Click "Add X Users" button to confirm and add selected users</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Quick Stats */}
      <div className="text-sm text-gray-500">
        {unassignedUsers.length} user{unassignedUsers.length !== 1 ? 's' : ''} available to add
      </div>
    </div>
  );
}

// Service Bundle Assignment Content Component
function ServiceBundleContent({ 
  organization, 
  onClose,
  onUpdate
}: {
  organization: Organization;
  onClose: () => void;
  onUpdate: (org: Organization) => void;
}) {
  // Available service bundles based on different templates
  const allServiceBundles = [
    "Basic Analytics",
    "Advanced Analytics", 
    "Real-time Processing",
    "Standard Support",
    "Premium Support",
    "Enterprise Support",
    "GDPR Compliance",
    "Multi-Region Storage",
    "API Access",
    "Custom Integrations"
  ];

  const [selectedBundles, setSelectedBundles] = useState<string[]>(organization.serviceBundles);

  const handleBundleToggle = (bundle: string, checked: boolean) => {
    if (checked) {
      setSelectedBundles([...selectedBundles, bundle]);
    } else {
      setSelectedBundles(selectedBundles.filter(b => b !== bundle));
    }
  };

  const handleSave = () => {
    const updatedOrg = {
      ...organization,
      serviceBundles: selectedBundles
    };
    onUpdate(updatedOrg);
    onClose();
  };

  return (
    <div className="space-y-6">
      {/* Current Service Bundles */}
      <div>
        <h3 className="font-semibold text-sm text-gray-700 mb-3">Current Service Bundles</h3>
        <div className="flex flex-wrap gap-2">
          {organization.serviceBundles.map(bundle => (
            <Badge key={bundle} variant="default" className="text-xs">
              {bundle}
            </Badge>
          ))}
        </div>
      </div>

      {/* Available Service Bundles */}
      <div>
        <h3 className="font-semibold text-sm text-gray-700 mb-3">Available Service Bundles</h3>
        <div className="grid grid-cols-1 gap-3 max-h-96 overflow-y-auto">
          {allServiceBundles.map(bundle => {
            const isSelected = selectedBundles.includes(bundle);
            return (
              <div key={bundle} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                <Checkbox
                  id={bundle}
                  checked={isSelected}
                  onCheckedChange={(checked) => handleBundleToggle(bundle, checked as boolean)}
                />
                <div className="flex-1">
                  <Label htmlFor={bundle} className="text-sm font-medium cursor-pointer">
                    {bundle}
                  </Label>
                  <p className="text-xs text-gray-500 mt-1">
                    {bundle === "Basic Analytics" && "Essential data analysis and reporting capabilities"}
                    {bundle === "Advanced Analytics" && "Enhanced analytics with AI/ML insights"}
                    {bundle === "Real-time Processing" && "Process satellite imagery in real-time"}
                    {bundle === "Standard Support" && "Business hours support via email"}
                    {bundle === "Premium Support" && "24/7 priority support with phone access"}
                    {bundle === "Enterprise Support" && "Dedicated support team and SLA guarantees"}
                    {bundle === "GDPR Compliance" && "European data protection compliance features"}
                    {bundle === "Multi-Region Storage" && "Data storage across multiple geographic regions"}
                    {bundle === "API Access" && "RESTful API access for custom integrations"}
                    {bundle === "Custom Integrations" && "Tailored integration solutions"}
                  </p>
                </div>
                {isSelected && (
                  <Badge variant="secondary" className="text-xs">
                    Selected
                  </Badge>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Summary */}
      <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg border border-blue-200">
        <div className="flex items-start space-x-2">
          <Package className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <p className="font-medium text-blue-900">Service Bundle Summary:</p>
            <p className="mt-1 text-blue-800">
              {selectedBundles.length} bundle{selectedBundles.length !== 1 ? 's' : ''} selected
              {selectedBundles.length !== organization.serviceBundles.length && (
                <span className="ml-2 text-orange-600">• Changes pending</span>
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button 
          onClick={handleSave}
          disabled={selectedBundles.length === 0}
          className="flex items-center space-x-2"
        >
          <CheckCircle className="h-4 w-4" />
          <span>Save Changes</span>
        </Button>
      </div>
    </div>
  );
}
