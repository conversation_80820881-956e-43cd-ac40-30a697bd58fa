import { useState } from "react";
import { Settings, <PERSON><PERSON>er<PERSON><PERSON>, Play, Search, Plus, X, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";

interface Project {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'completed' | 'on-hold' | 'archived';
}

interface JobDefinition {
  id: string;
  name: string;
  description: string;
  type: 'single' | 'pipeline' | 'scheduled';
  status: 'active' | 'draft' | 'disabled';
  organization: string;
  libraries: string[];
}

interface ProjectJobAssignment {
  id: string;
  projectId: string;
  projectName: string;
  jobId: string;
  jobName: string;
  jobType: 'single' | 'pipeline' | 'scheduled';
  executionTrigger: 'manual' | 'scheduled' | 'event';
  priority: 'high' | 'medium' | 'low';
  assignedDate: string;
  assignedBy: string;
  status: 'assigned' | 'running' | 'completed' | 'failed';
  lastExecuted?: string;
}

export function ProjectJobAssignments() {
  const [projects] = useState<Project[]>([
    {
      id: "1",
      name: "Agricultural Monitoring",
      description: "Migrate legacy data to new platform",
      status: "active"
    },
    {
      id: "2",
      name: "Disaster Response",
      description: "Integrate with external partner APIs",
      status: "active"
    },
    {
      id: "3",
      name: "Urban Planning",
      description: "Build comprehensive reporting dashboard",
      status: "completed"
    },
    {
      id: "4",
      name: "Urban Planning",
      description: "Optimize system performance",
      status: "active"
    }
  ]);

  const [jobDefinitions] = useState<JobDefinition[]>([
    {
      id: "1",
      name: "Data Validation Pipeline",
      description: "Validates incoming data integrity",
      type: "pipeline",
      status: "active",
      organization: "ACME Corp",
      libraries: ["data-utils", "validation-lib"]
    },
    {
      id: "2",
      name: "ETL Process",
      description: "Extract, transform, and load data",
      type: "pipeline",
      status: "active",
      organization: "ACME Corp",
      libraries: ["etl-tools", "data-utils"]
    },
    {
      id: "3",
      name: "Report Generator",
      description: "Generates scheduled reports",
      type: "scheduled",
      status: "active",
      organization: "ACME Corp",
      libraries: ["reporting-lib"]
    },
    {
      id: "4",
      name: "System Health Check",
      description: "Monitors system performance",
      type: "single",
      status: "active",
      organization: "ACME Corp",
      libraries: ["monitoring-lib"]
    },
    {
      id: "5",
      name: "Cache Optimizer",
      description: "Optimizes application cache",
      type: "single",
      status: "active",
      organization: "ACME Corp",
      libraries: ["cache-lib", "performance-tools"]
    }
  ]);

  const [assignments, setAssignments] = useState<ProjectJobAssignment[]>([
    {
      id: "1",
      projectId: "1",
      projectName: "Agricultural Monitoring",
      jobId: "1",
      jobName: "Data Validation Pipeline",
      jobType: "pipeline",
      executionTrigger: "event",
      priority: "high",
      assignedDate: "2024-01-15",
      assignedBy: "John Smith",
      status: "assigned",
      lastExecuted: "2024-06-15"
    },
    {
      id: "2",
      projectId: "1",
      projectName: "Agricultural Monitoring",
      jobId: "2",
      jobName: "ETL Process",
      jobType: "pipeline",
      executionTrigger: "scheduled",
      priority: "high",
      assignedDate: "2024-01-20",
      assignedBy: "John Smith",
      status: "running",
      lastExecuted: "2024-06-18"
    },
    {
      id: "3",
      projectId: "2",
      projectName: "Disaster Response",
      jobId: "4",
      jobName: "System Health Check",
      jobType: "single",
      executionTrigger: "manual",
      priority: "medium",
      assignedDate: "2024-03-01",
      assignedBy: "John Smith",
      status: "completed",
      lastExecuted: "2024-06-10"
    },
    {
      id: "4",
      projectId: "3",
      projectName: "Urban Planning",
      jobId: "3",
      jobName: "Report Generator",
      jobType: "scheduled",
      executionTrigger: "scheduled",
      priority: "medium",
      assignedDate: "2024-01-01",
      assignedBy: "System Admin",
      status: "completed",
      lastExecuted: "2024-06-17"
    },
    {
      id: "5",
      projectId: "4",
      projectName: "Urban Planning",
      jobId: "5",
      jobName: "Cache Optimizer",
      jobType: "single",
      executionTrigger: "manual",
      priority: "low",
      assignedDate: "2024-06-01",
      assignedBy: "John Smith",
      status: "assigned"
    }
  ]);

  const [selectedProject, setSelectedProject] = useState<string>("");
  const [selectedJobs, setSelectedJobs] = useState<string[]>([]);
  const [executionTrigger, setExecutionTrigger] = useState<'manual' | 'scheduled' | 'event'>('manual');
  const [priority, setPriority] = useState<'high' | 'medium' | 'low'>('medium');
  const [searchTerm, setSearchTerm] = useState("");
  const [projectFilter, setProjectFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);

  const handleAssignJobs = () => {
    if (!selectedProject || selectedJobs.length === 0) return;

    const project = projects.find(p => p.id === selectedProject);
    if (!project) return;

    const newAssignments: ProjectJobAssignment[] = selectedJobs.map(jobId => {
      const job = jobDefinitions.find(j => j.id === jobId);
      return {
        id: Date.now().toString() + jobId,
        projectId: selectedProject,
        projectName: project.name,
        jobId,
        jobName: job?.name || "",
        jobType: job?.type || "single",
        executionTrigger,
        priority,
        assignedDate: new Date().toISOString().split('T')[0],
        assignedBy: "Current User",
        status: "assigned"
      };
    });

    setAssignments([...assignments, ...newAssignments]);
    setSelectedJobs([]);
    setSelectedProject("");
    setExecutionTrigger('manual');
    setPriority('medium');
    setIsAssignDialogOpen(false);
  };

  const handleUnassignJob = (assignmentId: string) => {
    setAssignments(assignments.filter(a => a.id !== assignmentId));
  };

  const updateJobTrigger = (assignmentId: string, newTrigger: 'manual' | 'scheduled' | 'event') => {
    setAssignments(assignments.map(a => 
      a.id === assignmentId ? { ...a, executionTrigger: newTrigger } : a
    ));
  };

  const updateJobPriority = (assignmentId: string, newPriority: 'high' | 'medium' | 'low') => {
    setAssignments(assignments.map(a => 
      a.id === assignmentId ? { ...a, priority: newPriority } : a
    ));
  };

  const toggleJobSelection = (jobId: string) => {
    setSelectedJobs(prev =>
      prev.includes(jobId)
        ? prev.filter(id => id !== jobId)
        : [...prev, jobId]
    );
  };

  const getAvailableJobs = () => {
    if (!selectedProject) return [];
    
    const assignedJobIds = assignments
      .filter(a => a.projectId === selectedProject)
      .map(a => a.jobId);
    
    return jobDefinitions.filter(job => 
      !assignedJobIds.includes(job.id) && 
      job.status === 'active' &&
      (searchTerm === "" || job.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
       job.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const getProjectAssignments = (projectId: string) => {
    return assignments.filter(a => a.projectId === projectId);
  };

  const getJobAssignments = (jobId: string) => {
    return assignments.filter(a => a.jobId === jobId);
  };

  const filteredAssignments = assignments.filter(assignment => {
    const matchesProject = projectFilter === "all" || assignment.projectId === projectFilter;
    const matchesStatus = statusFilter === "all" || assignment.status === statusFilter;
    return matchesProject && matchesStatus;
  });

  const getStatusBadge = (status: 'assigned' | 'running' | 'completed' | 'failed') => {
    const colors: Record<string, string> = {
      assigned: 'bg-blue-100 text-blue-800',
      running: 'bg-yellow-100 text-yellow-800',
      completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800'
    };
    return <Badge className={colors[status]}>{status}</Badge>;
  };

  const getPriorityBadge = (priority: 'high' | 'medium' | 'low') => {
    const colors: Record<string, string> = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-gray-100 text-gray-800'
    };
    return <Badge className={colors[priority]}>{priority}</Badge>;
  };

  const getTypeBadge = (type: 'single' | 'pipeline' | 'scheduled') => {
    const colors: Record<string, string> = {
      single: 'bg-purple-100 text-purple-800',
      pipeline: 'bg-indigo-100 text-indigo-800',
      scheduled: 'bg-green-100 text-green-800'
    };
    return <Badge className={colors[type]}>{type}</Badge>;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Project Job Assignments</h1>
          <p className="text-gray-600 mt-2">Assign job definitions to projects with execution configurations</p>
        </div>
        <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Assign Jobs
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Assign Jobs to Project</DialogTitle>
              <DialogDescription>
                Select a project and job definitions to assign with execution configuration
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="project">Select Project</Label>
                  <Select value={selectedProject} onValueChange={setSelectedProject}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a project" />
                    </SelectTrigger>
                    <SelectContent>
                      {projects.filter(p => p.status === 'active').map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          {project.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="trigger">Execution Trigger</Label>
                  <Select value={executionTrigger} onValueChange={(value) => setExecutionTrigger(value as 'manual' | 'scheduled' | 'event')}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="manual">Manual Trigger</SelectItem>
                      <SelectItem value="scheduled">Scheduled Trigger</SelectItem>
                      <SelectItem value="event">Event Trigger</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">Priority Level</Label>
                <Select value={priority} onValueChange={(value) => setPriority(value as 'high' | 'medium' | 'low')}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">High Priority</SelectItem>
                    <SelectItem value="medium">Medium Priority</SelectItem>
                    <SelectItem value="low">Low Priority</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="jobSearch">Search Job Definitions</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="jobSearch"
                    placeholder="Search available job definitions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {selectedProject && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Available Job Definitions</h3>
                    <Button 
                      onClick={handleAssignJobs}
                      disabled={selectedJobs.length === 0}
                    >
                      Assign Selected ({selectedJobs.length})
                    </Button>
                  </div>
                  
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {getAvailableJobs().map((job) => (
                      <div key={job.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                        <Checkbox
                          checked={selectedJobs.includes(job.id)}
                          onCheckedChange={() => toggleJobSelection(job.id)}
                        />
                        <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                          <Settings className="h-4 w-4 text-indigo-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{job.name}</span>
                            {getTypeBadge(job.type)}
                          </div>
                          <p className="text-sm text-gray-500">{job.description}</p>
                        </div>
                      </div>
                    ))}
                    
                    {getAvailableJobs().length === 0 && (
                      <div className="text-center py-8">
                        <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">No available job definitions to assign</p>
                        <p className="text-sm text-gray-400">All active job definitions are already assigned to this project</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
                Cancel
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="assignments" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="assignments">All Assignments</TabsTrigger>
          <TabsTrigger value="by-project">By Project</TabsTrigger>
          <TabsTrigger value="by-job">By Job Definition</TabsTrigger>
        </TabsList>

        <TabsContent value="assignments" className="space-y-4">
          <div className="flex items-center space-x-4">
            <Select value={projectFilter} onValueChange={setProjectFilter}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Projects</SelectItem>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="assigned">Assigned</SelectItem>
                <SelectItem value="running">Running</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Job Assignments</CardTitle>
              <CardDescription>
                {filteredAssignments.length} assignment{filteredAssignments.length !== 1 ? 's' : ''} total
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Job Definition</TableHead>
                    <TableHead>Project</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Trigger</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Executed</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssignments.map((assignment) => (
                    <TableRow key={assignment.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <Settings className="h-4 w-4 text-indigo-600" />
                          </div>
                          <div>
                            <div className="font-medium">{assignment.jobName}</div>
                            <div className="text-sm text-gray-500">Job ID: {assignment.jobId}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <FolderOpen className="h-4 w-4 text-purple-600" />
                          <span className="font-medium">{assignment.projectName}</span>
                        </div>
                      </TableCell>
                      <TableCell>{getTypeBadge(assignment.jobType)}</TableCell>
                      <TableCell>
                        <Select 
                          value={assignment.executionTrigger} 
                          onValueChange={(value) => updateJobTrigger(assignment.id, value as 'manual' | 'scheduled' | 'event')}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="manual">Manual</SelectItem>
                            <SelectItem value="scheduled">Scheduled</SelectItem>
                            <SelectItem value="event">Event</SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        <Select 
                          value={assignment.priority} 
                          onValueChange={(value) => updateJobPriority(assignment.id, value as 'high' | 'medium' | 'low')}
                        >
                          <SelectTrigger className="w-24">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="low">Low</SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>{getStatusBadge(assignment.status)}</TableCell>
                      <TableCell>{assignment.lastExecuted || 'Never'}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm">
                            <Play className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleUnassignJob(assignment.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="by-project" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Assignments by Project</CardTitle>
              <CardDescription>
                View job assignments organized by project
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {projects.map((project) => {
                  const projectAssignments = getProjectAssignments(project.id);
                  return (
                    <Card key={project.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                              <FolderOpen className="h-4 w-4 text-purple-600" />
                            </div>
                            <div>
                              <h3 className="font-semibold">{project.name}</h3>
                              <p className="text-sm text-gray-500">{project.description}</p>
                            </div>
                          </div>
                          <Badge variant="outline">{projectAssignments.length} jobs</Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {projectAssignments.length > 0 ? (
                          <div className="space-y-3">
                            {projectAssignments.map((assignment) => (
                              <div key={assignment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <div className="w-6 h-6 bg-indigo-100 rounded-lg flex items-center justify-center">
                                    <Settings className="h-3 w-3 text-indigo-600" />
                                  </div>
                                  <div>
                                    <div className="font-medium">{assignment.jobName}</div>
                                    <div className="text-sm text-gray-500">Trigger: {assignment.executionTrigger}</div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  {getTypeBadge(assignment.jobType)}
                                  {getPriorityBadge(assignment.priority)}
                                  {getStatusBadge(assignment.status)}
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-500 text-center py-4">No job definitions assigned to this project</p>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="by-job" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Assignments by Job Definition</CardTitle>
              <CardDescription>
                View project assignments organized by job definition
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {jobDefinitions.map((job) => {
                  const jobAssignments = getJobAssignments(job.id);
                  return (
                    <Card key={job.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                              <Settings className="h-4 w-4 text-indigo-600" />
                            </div>
                            <div>
                              <h3 className="font-semibold">{job.name}</h3>
                              <p className="text-sm text-gray-500">{job.description}</p>
                            </div>
                            {getTypeBadge(job.type)}
                          </div>
                          <Badge variant="outline">{jobAssignments.length} projects</Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {jobAssignments.length > 0 ? (
                          <div className="space-y-3">
                            {jobAssignments.map((assignment) => (
                              <div key={assignment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <div className="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <FolderOpen className="h-3 w-3 text-purple-600" />
                                  </div>
                                  <div>
                                    <div className="font-medium">{assignment.projectName}</div>
                                    <div className="text-sm text-gray-500">Assigned on {assignment.assignedDate}</div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  {getPriorityBadge(assignment.priority)}
                                  {getStatusBadge(assignment.status)}
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-500 text-center py-4">No project assignments for this job definition</p>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}