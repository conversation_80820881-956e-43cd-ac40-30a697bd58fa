
import { useState } from "react";
import { 
  Settings, 
  Globe,
  ChevronDown,
  ChevronRight,
  Building,
  BarChart3
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface SideNavigationProps {
  userRole: 'global_admin' | 'org_admin' | 'project_admin' | 'contributor' | 'reader';
  activeSection: string;
  onSectionChange: (section: string) => void;
}

interface MenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  children: { id: string; label: string; badge?: string | number }[];
  badge?: string | number;
}

export function SideNavigation({ userRole, activeSection, onSectionChange }: SideNavigationProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>(
    userRole === 'global_admin' ? ['pipeline-management', 'administration'] : ['pipeline-management', 'administration']
  );

  const toggleSection = (section: string) => {
    setExpandedSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const globalAdminMenuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: BarChart3,
      children: []
    },
    {
      id: 'organizations',
      label: 'Organizations',
      icon: Globe,
      children: []
    },
    {
      id: 'administration',
      label: 'Administration',
      icon: Settings,
      children: [
        { id: 'global-users', label: 'Users' }
      ]
    }
  ];

  const orgAdminMenuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: BarChart3,
      children: []
    },
    {
      id: 'organizations',
      label: 'Organization',
      icon: Building,
      children: []
    },
    {
      id: 'administration',
      label: 'Administration',
      icon: Settings,
      children: [
        { id: 'users', label: 'Users' }
      ]
    }
  ];

  const projectAdminMenuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: BarChart3,
      children: []
    },
    {
      id: 'administration',
      label: 'Administration',
      icon: Settings,
      children: [
        { id: 'users', label: 'Users' }
      ]
    }
  ];

  const contributorMenuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: BarChart3,
      children: []
    },
    {
      id: 'tools',
      label: 'Tools',
      icon: Settings,
      children: []
    }
  ];

  const readerMenuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: BarChart3,
      children: []
    },
    {
      id: 'tools',
      label: 'Tools',
      icon: Settings,
      children: []
    }
  ];

  const getMenuItems = () => {
    switch (userRole) {
      case 'global_admin':
        return globalAdminMenuItems;
      case 'org_admin':
        return orgAdminMenuItems;
      case 'project_admin':
        return projectAdminMenuItems;
      case 'contributor':
        return contributorMenuItems;
      case 'reader':
        return readerMenuItems;
      default:
        return readerMenuItems;
    }
  };

  const menuItems = getMenuItems();


  return (
    <div className="bg-sidebar border-r border-sidebar-border h-full flex flex-col shadow-sm w-64">


      {/* Navigation menu */}
      <nav className="flex-1 overflow-y-auto p-2">
        <div className="space-y-1">
          {menuItems.map((item) => (
            <div key={item.id}>
              {item.children.length > 0 ? (
                <div>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-between group transition-all duration-200 hover:bg-sidebar-accent text-sidebar-foreground",
                      expandedSections.includes(item.id) && !activeSection.startsWith(item.id) && "bg-sidebar-accent",
                      activeSection === item.id && "bg-sidebar-primary text-sidebar-primary-foreground border-l-4 border-sidebar-ring",
                      // Highlight parent section when child is active
                      activeSection !== item.id && item.children.some(child => child.id === activeSection) && 
                      "bg-sidebar-accent text-sidebar-accent-foreground border-l-2 border-sidebar-ring/50"
                    )}
                    onClick={() => toggleSection(item.id)}
                  >
                    <div className="flex items-center min-w-0">
                      <item.icon className="h-5 w-5 flex-shrink-0 mr-3" />
                      <span className="truncate">{item.label}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center">
                      {expandedSections.includes(item.id) ? (
                        <ChevronDown className="h-4 w-4 flex-shrink-0" />
                      ) : (
                        <ChevronRight className="h-4 w-4 flex-shrink-0" />
                      )}
                    </div>
                  </Button>
                  {expandedSections.includes(item.id) && (
                    <div className="ml-4 mt-1 space-y-1 border-l-2 border-sidebar-border pl-2">
                      {item.children.map((child) => (
                        <Button
                          key={child.id}
                          variant="ghost"
                          size="sm"
                          className={cn(
                            "w-full justify-start text-sm transition-all duration-200 hover:bg-sidebar-accent text-sidebar-foreground relative",
                            activeSection === child.id && "bg-sidebar-primary text-sidebar-primary-foreground font-medium shadow-sm border-l-4 border-sidebar-ring"
                          )}
                          onClick={() => onSectionChange(child.id)}
                        >
                          <span className="truncate">{child.label}</span>
                          {child.badge && (
                            <Badge 
                              variant={typeof child.badge === 'string' ? 'default' : 'secondary'} 
                              className="ml-auto text-xs"
                            >
                              {child.badge}
                            </Badge>
                          )}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full justify-start group transition-all duration-200 hover:bg-sidebar-accent text-sidebar-foreground",
                    activeSection === item.id && "bg-sidebar-primary text-sidebar-primary-foreground border-l-4 border-sidebar-ring font-medium"
                  )}
                  onClick={() => onSectionChange(item.id)}
                >
                  <div className="flex items-center min-w-0">
                    <item.icon className="h-5 w-5 flex-shrink-0 mr-3" />
                    <span className="truncate">{item.label}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="ml-auto text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </div>
                </Button>
              )}
            </div>
          ))}
        </div>
      </nav>
    </div>
  );
}
