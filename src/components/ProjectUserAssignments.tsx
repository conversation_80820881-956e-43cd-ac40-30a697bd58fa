import { useState } from "react";
import { User, <PERSON><PERSON><PERSON><PERSON><PERSON>, Shield, Eye, Search, Plus, X, User<PERSON>he<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";

interface OrgUser {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'member';
  status: 'active' | 'inactive' | 'pending';
  organizationRoles: {
    organizationId: string;
    role: 'admin' | 'member';
    permissions: string[];
  }[];
  projectRoles: {
    projectId: string;
    role: 'project_admin' | 'contributor' | 'reader';
    permissions: string[];
  }[];
}

interface Project {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'completed' | 'on-hold' | 'archived';
}

interface ProjectUserAssignment {
  id: string;
  projectId: string;
  projectName: string;
  userId: string;
  userName: string;
  userEmail: string;
  userRole: 'admin' | 'member';
  projectRole: 'project_admin' | 'contributor' | 'reader';
  projectAccess: 'admin' | 'read';
  assignedDate: string;
  assignedBy: string;
}

export function ProjectUserAssignments() {
  const [users] = useState<OrgUser[]>([
    {
      id: "1",
      name: "John Smith",
      email: "<EMAIL>",
      role: "admin",
      status: "active",
      organizationRoles: [{ organizationId: "1", role: "admin", permissions: ["manage_users"] }],
      projectRoles: [{ projectId: "1", role: "project_admin", permissions: ["manage_project"] }]
    },
    {
      id: "2",
      name: "Sarah Johnson",
      email: "<EMAIL>",
      role: "member",
      status: "active",
      organizationRoles: [{ organizationId: "1", role: "member", permissions: ["view_org"] }],
      projectRoles: [{ projectId: "1", role: "contributor", permissions: ["contribute"] }]
    },
    {
      id: "3",
      name: "Mike Davis",
      email: "<EMAIL>",
      role: "member",
      status: "active",
      organizationRoles: [{ organizationId: "1", role: "member", permissions: ["view_org"] }],
      projectRoles: []
    },
    {
      id: "4",
      name: "Lisa Wilson",
      email: "<EMAIL>",
      role: "member",
      status: "active",
      organizationRoles: [{ organizationId: "1", role: "member", permissions: ["view_org"] }],
      projectRoles: []
    }
  ]);

  const [projects] = useState<Project[]>([
    {
      id: "1",
      name: "Agricultural Monitoring",
      description: "Migrate legacy data to new platform",
      status: "active"
    },
    {
      id: "2",
      name: "Disaster Response",
      description: "Integrate with external partner APIs",
      status: "active"
    },
    {
      id: "3",
      name: "Urban Planning",
      description: "Build comprehensive reporting dashboard",
      status: "completed"
    },
    {
      id: "5",
      name: "Urban Planning",
      description: "Optimize system performance",
      status: "active"
    }
  ]);

  const [assignments, setAssignments] = useState<ProjectUserAssignment[]>([
    {
      id: "1",
      projectId: "1",
      projectName: "Agricultural Monitoring",
      userId: "1",
      userName: "John Smith",
      userEmail: "<EMAIL>",
      userRole: "admin",
      projectRole: "project_admin",
      projectAccess: "admin",
      assignedDate: "2024-01-15",
      assignedBy: "System Admin"
    },
    {
      id: "2",
      projectId: "1",
      projectName: "Agricultural Monitoring",
      userId: "2",
      userName: "Sarah Johnson",
      userEmail: "<EMAIL>",
      userRole: "member",
      projectRole: "contributor",
      projectAccess: "read",
      assignedDate: "2024-01-20",
      assignedBy: "John Smith"
    },
    {
      id: "3",
      projectId: "2",
      projectName: "Disaster Response",
      userId: "3",
      userName: "Mike Davis",
      userEmail: "<EMAIL>",
      userRole: "member",
      projectRole: "reader",
      projectAccess: "read",
      assignedDate: "2024-03-01",
      assignedBy: "John Smith"
    },
    {
      id: "4",
      projectId: "3",
      projectName: "Urban Planning",
      userId: "1",
      userName: "John Smith",
      userEmail: "<EMAIL>",
      userRole: "admin",
      projectRole: "project_admin",
      projectAccess: "admin",
      assignedDate: "2024-01-01",
      assignedBy: "System Admin"
    },
    {
      id: "5",
      projectId: "5",
      projectName: "Urban Planning",
      userId: "4",
      userName: "Lisa Wilson",
      userEmail: "<EMAIL>",
      userRole: "member",
      projectRole: "contributor",
      projectAccess: "read",
      assignedDate: "2024-06-01",
      assignedBy: "John Smith"
    }
  ]);

  const [selectedProject, setSelectedProject] = useState<string>("");
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [accessLevel, setAccessLevel] = useState<'admin' | 'read'>('read');
  const [searchTerm, setSearchTerm] = useState("");
  const [projectFilter, setProjectFilter] = useState<string>("all");
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);

  const handleAssignUsers = () => {
    if (!selectedProject || selectedUsers.length === 0) return;

    const project = projects.find(p => p.id === selectedProject);
    if (!project) return;

    const newAssignments: ProjectUserAssignment[] = selectedUsers.map(userId => {
      const user = users.find(u => u.id === userId);
      return {
        id: Date.now().toString() + userId,
        projectId: selectedProject,
        projectName: project.name,
        userId,
        userName: user?.name || "",
        userEmail: user?.email || "",
        userRole: user?.role || "member",
        projectRole: "reader",
        projectAccess: accessLevel,
        assignedDate: new Date().toISOString().split('T')[0],
        assignedBy: "Current User"
      };
    });

    setAssignments([...assignments, ...newAssignments]);
    setSelectedUsers([]);
    setSelectedProject("");
    setAccessLevel('read');
    setIsAssignDialogOpen(false);
  };

  const handleUnassignUser = (assignmentId: string) => {
    setAssignments(assignments.filter(a => a.id !== assignmentId));
  };

  const updateUserRole = (assignmentId: string, newRole: 'project_admin' | 'contributor' | 'reader') => {
    setAssignments(assignments.map(a => 
      a.id === assignmentId ? { ...a, projectRole: newRole } : a
    ));
  };

  const toggleUserSelection = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const getAvailableUsers = () => {
    if (!selectedProject) return [];
    
    const assignedUserIds = assignments
      .filter(a => a.projectId === selectedProject)
      .map(a => a.userId);
    
    return users.filter(user => 
      !assignedUserIds.includes(user.id) &&
      (searchTerm === "" || user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
       user.email.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const getProjectAssignments = (projectId: string) => {
    return assignments.filter(a => a.projectId === projectId);
  };

  const getUserAssignments = (userId: string) => {
    return assignments.filter(a => a.userId === userId);
  };

  const filteredAssignments = assignments.filter(assignment => {
    const matchesProject = projectFilter === "all" || assignment.projectId === projectFilter;
    return matchesProject;
  });


  const getRolesBadges = (user: OrgUser) => {
    const badges = [];
    
    // Organization roles
    user.organizationRoles?.forEach(orgRole => {
      badges.push(
        <Badge key={`org-${orgRole.organizationId}`} variant="default">
          {orgRole.role.replace('_', ' ')} (Org)
        </Badge>
      );
    });
    
    // Project roles count
    if (user.projectRoles?.length > 0) {
      badges.push(
        <Badge key="project-roles" variant="outline">
          {user.projectRoles.length} Project Role{user.projectRoles.length !== 1 ? 's' : ''}
        </Badge>
      );
    }
    
    return badges.length > 0 ? badges : [<Badge key="no-roles" variant="secondary">No Roles</Badge>];
  };

  const getRoleBadge = (role: 'admin' | 'member') => {
    return (
      <Badge variant={role === 'admin' ? 'default' : 'secondary'}>
        {role}
      </Badge>
    );
  };

  const getAccessBadge = (access: 'admin' | 'read') => {
    return (
      <Badge variant={access === 'admin' ? 'default' : 'outline'}>
        <Eye className="h-3 w-3 mr-1" />
        {access === 'admin' ? 'Admin' : 'Read'}
      </Badge>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Project User Assignments</h1>
          <p className="text-gray-600 mt-2">Assign users to projects with appropriate access levels</p>
        </div>
        <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Assign Users
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Assign Users to Project</DialogTitle>
              <DialogDescription>
                Select a project and users to assign with their access level
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="project">Select Project</Label>
                  <Select value={selectedProject} onValueChange={setSelectedProject}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a project" />
                    </SelectTrigger>
                    <SelectContent>
                      {projects.filter(p => p.status === 'active').map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          {project.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="access">Access Level</Label>
                  <Select value={accessLevel} onValueChange={(value) => setAccessLevel(value as 'admin' | 'read')}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="read">Read Access</SelectItem>
                      <SelectItem value="admin">Admin Access</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="userSearch">Search Users</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="userSearch"
                    placeholder="Search available users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {selectedProject && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Available Users</h3>
                    <Button 
                      onClick={handleAssignUsers}
                      disabled={selectedUsers.length === 0}
                    >
                      Assign Selected ({selectedUsers.length})
                    </Button>
                  </div>
                  
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {getAvailableUsers().map((user) => (
                      <div key={user.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                        <Checkbox
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={() => toggleUserSelection(user.id)}
                        />
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          <User className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{user.name}</span>
                            <div className="flex flex-wrap gap-1">
                              {getRolesBadges(user)}
                            </div>
                          </div>
                          <p className="text-sm text-gray-500">{user.email}</p>
                        </div>
                      </div>
                    ))}
                    
                    {getAvailableUsers().length === 0 && (
                      <div className="text-center py-8">
                        <UserCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">No available users to assign</p>
                        <p className="text-sm text-gray-400">All active users are already assigned to this project</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
                Cancel
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="assignments" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="assignments">All Assignments</TabsTrigger>
          <TabsTrigger value="by-project">By Project</TabsTrigger>
          <TabsTrigger value="by-user">By User</TabsTrigger>
        </TabsList>

        <TabsContent value="assignments" className="space-y-4">
          <div className="flex items-center space-x-4">
            <Select value={projectFilter} onValueChange={setProjectFilter}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Projects</SelectItem>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>User Assignments</CardTitle>
              <CardDescription>
                {filteredAssignments.length} assignment{filteredAssignments.length !== 1 ? 's' : ''} total
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Project</TableHead>
                    <TableHead>Project Role</TableHead>
                    <TableHead>Assigned Date</TableHead>
                    <TableHead>Assigned By</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssignments.map((assignment) => (
                    <TableRow key={assignment.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <User className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium">{assignment.userName}</div>
                            <div className="text-sm text-gray-500">{assignment.userEmail}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <FolderOpen className="h-4 w-4 text-purple-600" />
                          <span className="font-medium">{assignment.projectName}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={assignment.projectRole === 'project_admin' ? 'default' : assignment.projectRole === 'contributor' ? 'secondary' : 'outline'}>
                          {assignment.projectRole.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>{assignment.assignedDate}</TableCell>
                      <TableCell>{assignment.assignedBy}</TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleUnassignUser(assignment.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="by-project" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Assignments by Project</CardTitle>
              <CardDescription>
                View user assignments organized by project
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {projects.map((project) => {
                  const projectAssignments = getProjectAssignments(project.id);
                  return (
                    <Card key={project.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                              <FolderOpen className="h-4 w-4 text-purple-600" />
                            </div>
                            <div>
                              <h3 className="font-semibold">{project.name}</h3>
                              <p className="text-sm text-gray-500">{project.description}</p>
                            </div>
                          </div>
                          <Badge variant="outline">{projectAssignments.length} users</Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {projectAssignments.length > 0 ? (
                          <div className="space-y-3">
                            {projectAssignments.map((assignment) => (
                              <div key={assignment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <User className="h-3 w-3 text-blue-600" />
                                  </div>
                                  <div>
                                    <div className="font-medium">{assignment.userName}</div>
                                    <div className="text-sm text-gray-500">{assignment.userEmail}</div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  {getRoleBadge(assignment.userRole)}
                                  {getAccessBadge(assignment.projectAccess)}
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-500 text-center py-4">No users assigned to this project</p>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="by-user" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Assignments by User</CardTitle>
              <CardDescription>
                View project assignments organized by user
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {users.map((user) => {
                  const userAssignments = getUserAssignments(user.id);
                  return (
                    <Card key={user.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                              <User className="h-4 w-4 text-blue-600" />
                            </div>
                            <div>
                              <h3 className="font-semibold">{user.name}</h3>
                              <p className="text-sm text-gray-500">{user.email}</p>
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {getRolesBadges(user)}
                            </div>
                          </div>
                          <Badge variant="outline">{userAssignments.length} projects</Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {userAssignments.length > 0 ? (
                          <div className="space-y-3">
                            {userAssignments.map((assignment) => (
                              <div key={assignment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <div className="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <FolderOpen className="h-3 w-3 text-purple-600" />
                                  </div>
                                  <div>
                                    <div className="font-medium">{assignment.projectName}</div>
                                    <div className="text-sm text-gray-500">Assigned on {assignment.assignedDate}</div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  {getAccessBadge(assignment.projectAccess)}
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-500 text-center py-4">No project assignments for this user</p>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}