
export interface UserProject {
  id: string;
  name: string;
  description: string;
  role: 'admin' | 'reader';
  status: 'active' | 'completed' | 'on-hold';
  imageType: string;
  region: string;
  tenantName: string;
  jobCount: number;
  dataSize: string;
  lastActivity: string;
  adlsEndpoint: string;
  containerName: string;
}

export interface JobExecution {
  id: string;
  projectId: string;
  jobName: string;
  jobType: 'manual' | 'scheduled' | 'event';
  trigger?: string;
  status: 'running' | 'completed' | 'failed' | 'queued' | 'scheduled';
  startTime: string;
  endTime?: string;
  duration?: string;
  imagesProcessed: number;
  outputSize: string;
  nextRun?: string;
  schedule?: string;
  eventType?: string;
}

export interface AnalyticsData {
  totalImagesProcessed: number;
  totalJobsRun: number;
  averageProcessingTime: string;
  dataUploaded: string;
  dataGenerated: string;
  successRate: number;
}

export interface FileItem {
  id: string;
  name: string;
  type: 'file' | 'folder';
  size?: string;
  uploadDate?: string;
  status: 'uploading' | 'uploaded' | 'processing' | 'processed' | 'failed';
  progress?: number;
  parentPath: string;
  fullPath: string;
  projectId: string;
  associatedJobId?: string;
  autoTriggerEnabled?: boolean;
  processingResults?: {
    outputFiles: string[];
    jobId: string;
    completedAt: string;
  };
}

export interface ThickClientSession {
  id: string;
  projectId: string;
  clientVersion: string;
  platform: string;
  username: string;
  ipAddress: string;
  status: 'connected' | 'disconnected' | 'idle';
  lastHeartbeat: string;
  connectedAt: string;
  apiToken: string;
  activeUploads: number;
  totalUploaded: string;
}

export interface ApiToken {
  id: string;
  name: string;
  token: string;
  createdAt: string;
  lastUsed?: string;
  expiresAt: string;
  permissions: string[];
  status: 'active' | 'expired' | 'revoked';
}
