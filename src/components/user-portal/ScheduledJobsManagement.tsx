import { Search, Plus, Calendar, Clock, AlertCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { getStatusBadge, getJobTypeBadge } from "@/lib/badge-variants";
import type { JobExecution, UserProject } from "./types";

interface ScheduledJobsManagementProps {
  jobExecutions: JobExecution[];
  userProjects: UserProject[];
}

export function ScheduledJobsManagement({ jobExecutions, userProjects }: ScheduledJobsManagementProps) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Scheduled Jobs</h2>
          <p className="text-gray-600">Manage automated and event-based job executions</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Schedule
        </Button>
      </div>

      <div className="flex items-center space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search scheduled jobs..."
            className="pl-10"
          />
        </div>
      </div>

      {/* Scheduled Jobs Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Schedules</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{jobExecutions.filter(j => j.jobType === 'scheduled' && j.status !== 'failed').length}</div>
            <p className="text-xs text-muted-foreground">Automated job schedules</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Event Triggers</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{jobExecutions.filter(j => j.jobType === 'event').length}</div>
            <p className="text-xs text-muted-foreground">Event-based automations</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Next Execution</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">Tomorrow 06:00</div>
            <p className="text-xs text-muted-foreground">Daily NDVI Monitoring</p>
          </CardContent>
        </Card>
      </div>

      {/* Scheduled Jobs List */}
      <Card>
        <CardHeader>
          <CardTitle>Scheduled & Event-Based Jobs</CardTitle>
          <CardDescription>Automated job executions and triggers</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Job Name</TableHead>
                <TableHead>Project</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Schedule/Trigger</TableHead>
                <TableHead>Last Run</TableHead>
                <TableHead>Next Run</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Success Rate</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {jobExecutions
                .filter(job => job.jobType === 'scheduled' || job.jobType === 'event')
                .map((job) => {
                  const project = userProjects.find(p => p.id === job.projectId);
                  return (
                    <TableRow key={job.id}>
                      <TableCell className="font-medium">{job.jobName}</TableCell>
                      <TableCell>{project?.name}</TableCell>
                      <TableCell>{getJobTypeBadge(job.jobType)}</TableCell>
                      <TableCell>
                        {job.jobType === 'scheduled' ? (
                          <div>
                            <div className="text-sm font-medium">{job.schedule}</div>
                          </div>
                        ) : (
                          <div>
                            <div className="text-sm font-medium">{job.trigger}</div>
                            <div className="text-xs text-muted-foreground">{job.eventType}</div>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {job.status !== 'scheduled' ? job.startTime : 'Not executed yet'}
                      </TableCell>
                      <TableCell>
                        {job.nextRun || (job.jobType === 'event' ? 'On trigger' : 'N/A')}
                      </TableCell>
                      <TableCell>{getStatusBadge(job.status)}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium">92%</div>
                          <div className="w-16 h-2 bg-muted rounded-full">
                            <div className="w-14 h-2 bg-success rounded-full"></div>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}