import {
  Database,
  CheckCircle,
  Upload,
  Clock,
  FolderOpen,
  Play,
  ExternalLink,
  Wifi,
  WifiOff,
  Monitor,
  File
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { getStatusBadge } from "@/lib/badge-variants";
import type { UserProject, JobExecution, AnalyticsData, ThickClientSession } from "./types";

interface AnalyticsTabProps {
  analyticsData: AnalyticsData;
  userProjects: UserProject[];
  jobExecutions: JobExecution[];
  thickClientSessions?: ThickClientSession[];
}

export function AnalyticsTab({ analyticsData, userProjects, jobExecutions, thickClientSessions = [] }: AnalyticsTabProps) {
  return (
    <div className="space-y-6">
      {/* Key Metrics Grid - Following Admin <PERSON>board <PERSON>tern */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Images Processed</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.totalImagesProcessed.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Total satellite images analyzed</p>
            <div className="text-xs text-green-600 font-medium mt-1">+20.1% from last month</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Job Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.successRate}%</div>
            <p className="text-xs text-muted-foreground">Processing success rate</p>
            <div className="text-xs text-green-600 font-medium mt-1">****% improvement</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Data Uploaded</CardTitle>
            <Upload className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.dataUploaded}</div>
            <p className="text-xs text-muted-foreground">Raw satellite imagery</p>
            <div className="text-xs text-info font-medium mt-1">via Thick Client</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.averageProcessingTime}</div>
            <p className="text-xs text-muted-foreground">Average per job execution</p>
            <div className="text-xs text-green-600 font-medium mt-1">-8% faster</div>
          </CardContent>
        </Card>
      </div>

      {/* Project Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Project Performance</CardTitle>
          <CardDescription>Processing metrics across your assigned projects</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {userProjects.map(project => {
              const projectJobs = jobExecutions.filter(job => job.projectId === project.id);
              const completedJobs = projectJobs.filter(job => job.status === 'completed').length;
              const totalJobs = projectJobs.length;
              const successRate = totalJobs > 0 ? Math.round((completedJobs / totalJobs) * 100) : 0;
              
              return (
                <div key={project.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg border">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      project.role === 'admin' ? 'bg-primary/10' : 'bg-info/10'
                    }`}>
                      <FolderOpen className={`h-4 w-4 ${
                        project.role === 'admin' ? 'text-primary' : 'text-info'
                      }`} />
                    </div>
                    <div>
                      <p className="text-sm font-medium">{project.name}</p>
                      <p className="text-xs text-muted-foreground">{project.imageType} • {project.region}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">{successRate}%</span>
                      <div className="w-16 h-2 bg-muted rounded-full">
                        <div 
                          className={`h-2 rounded-full ${
                            successRate >= 90 ? 'bg-success' : 
                            successRate >= 75 ? 'bg-warning' : 'bg-destructive'
                          }`}
                          style={{ width: `${successRate}%` }}
                        />
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">{totalJobs} jobs total</p>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Thick Client Status */}
      <Card>
          <CardHeader>
            <CardTitle>Thick Client Status</CardTitle>
            <CardDescription>Connected desktop clients and session information</CardDescription>
          </CardHeader>
          <CardContent>
            {thickClientSessions.length > 0 ? (
              <div className="space-y-3">
                {thickClientSessions.map(session => {
                  const isConnected = session.status === 'connected';
                  const lastHeartbeat = new Date(session.lastHeartbeat);
                  const now = new Date();
                  const timeDiff = now.getTime() - lastHeartbeat.getTime();
                  const minutesAgo = Math.floor(timeDiff / 60000);
                  
                  return (
                    <div key={session.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg border">
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                          isConnected ? 'bg-success/10' : 'bg-destructive/10'
                        }`}>
                          {isConnected ? (
                            <Wifi className="h-4 w-4 text-success" />
                          ) : (
                            <WifiOff className="h-4 w-4 text-destructive" />
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium">{session.username} - {session.platform}</p>
                          <p className="text-xs text-muted-foreground">
                            Version {session.clientVersion} • {session.ipAddress}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${
                            isConnected ? 'bg-success' : 'bg-destructive'
                          }`} />
                          <span className="text-sm font-medium capitalize">{session.status}</span>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {isConnected ? `Active uploads: ${session.activeUploads}` : `Last seen: ${minutesAgo}m ago`}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Total uploaded: {session.totalUploaded}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <Monitor className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-sm text-muted-foreground">No thick clients connected</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Download and configure the thick client to start uploading files
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Activity Feed */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest processing events and uploads</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                {
                  action: "File Upload Completed",
                  entity: "IMG_003_multispectral.tif",
                  project: "Agricultural Monitoring",
                  time: "2 minutes ago",
                  icon: Upload,
                  color: "bg-success/10",
                  iconColor: "text-success"
                },
                {
                  action: "Job Processing Started", 
                  entity: "Crop Health Assessment",
                  project: "Agricultural Monitoring",
                  time: "15 minutes ago",
                  icon: Play,
                  color: "bg-primary/10",
                  iconColor: "text-primary"
                },
                {
                  action: "NDVI Analysis Completed",
                  entity: "IMG_001_multispectral.tif",
                  project: "Agricultural Monitoring", 
                  time: "1 hour ago",
                  icon: CheckCircle,
                  color: "bg-success/10",
                  iconColor: "text-success"
                },
                {
                  action: "Thick Client Connected",
                  entity: "Windows Desktop Client",
                  project: "Session Management",
                  time: "2 hours ago",
                  icon: Wifi,
                  color: "bg-info/10",
                  iconColor: "text-info"
                },
                {
                  action: "Data Portal Access",
                  entity: "Results Download",
                  project: "Urban Planning Analysis",
                  time: "3 hours ago",
                  icon: ExternalLink,
                  color: "bg-info/10", 
                  iconColor: "text-info"
                }
              ].map((activity, idx) => (
                <div key={idx} className="flex items-start space-x-3 p-3 bg-muted/30 rounded-lg border hover:bg-muted/50 transition-colors">
                  <div className={`p-2 rounded-lg ${activity.color}`}>
                    <activity.icon className={`h-4 w-4 ${activity.iconColor}`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">{activity.action}</p>
                    <p className="text-sm text-muted-foreground">{activity.entity}</p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-muted-foreground">{activity.project}</span>
                      <span className="text-xs text-muted-foreground">{activity.time}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

      {/* Detailed Analytics Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Storage Usage */}
        <Card>
          <CardHeader>
            <CardTitle>Storage Usage</CardTitle>
            <CardDescription>Data consumption across tenants and projects</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tenant</TableHead>
                  <TableHead>Project</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium">Agricultural Data Hub</TableCell>
                  <TableCell>Agricultural Monitoring</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">1.2 TB</span>
                      <div className="w-16 h-2 bg-muted rounded-full">
                        <div className="w-12 h-2 bg-primary rounded-full" />
                      </div>
                      <span className="text-xs text-muted-foreground">75%</span>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge("active")}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Emergency Response</TableCell>
                  <TableCell>Urban Planning</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">850 GB</span>
                      <div className="w-16 h-2 bg-muted rounded-full">
                        <div className="w-8 h-2 bg-success rounded-full" />
                      </div>
                      <span className="text-xs text-muted-foreground">50%</span>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge("active")}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Processing Statistics */}
        <Card>
          <CardHeader>
            <CardTitle>Processing Statistics</CardTitle>
            <CardDescription>Job execution metrics and performance trends</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-muted-foreground">Total Jobs</span>
                  <p className="text-2xl font-bold text-primary">{analyticsData.totalJobsRun}</p>
                </div>
                <div>
                  <span className="font-medium text-muted-foreground">Data Generated</span>
                  <p className="text-2xl font-bold text-success">{analyticsData.dataGenerated}</p>
                </div>
                <div>
                  <span className="font-medium text-muted-foreground">Manual Jobs</span>
                  <p className="text-lg font-bold text-info">{jobExecutions.filter(j => j.jobType === 'manual').length}</p>
                </div>
                <div>
                  <span className="font-medium text-muted-foreground">Automated Jobs</span>
                  <p className="text-lg font-bold text-warning">{jobExecutions.filter(j => j.jobType !== 'manual').length}</p>
                </div>
              </div>
              
              <div className="pt-4 border-t">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Processing Efficiency</span>
                  <span className="text-sm text-muted-foreground">{analyticsData.successRate}%</span>
                </div>
                <div className="w-full h-2 bg-muted rounded-full">
                  <div 
                    className="h-2 bg-success rounded-full"
                    style={{ width: `${analyticsData.successRate}%` }}
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-1">Based on completed vs failed jobs</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}