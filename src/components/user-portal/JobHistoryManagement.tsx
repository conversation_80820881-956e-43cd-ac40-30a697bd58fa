import { useState } from "react";
import { Search, Download, Filter, Play, RefreshCw } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { getStatusBadge, getJobTypeBadge } from "@/lib/badge-variants";
import type { JobExecution, UserProject } from "./types";

interface JobHistoryManagementProps {
  jobExecutions: JobExecution[];
  userProjects: UserProject[];
  userRole?: 'admin' | 'reader';
}

export function JobHistoryManagement({ jobExecutions, userProjects, userRole = 'admin' }: JobHistoryManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProjectId, setSelectedProjectId] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [jobTypeFilter, setJobTypeFilter] = useState<string>("all");

  // RBAC permissions
  const canTriggerJobs = userRole === 'admin';
  const canExportHistory = userRole === 'admin';

  // Filter jobs based on project selection and other filters
  const filteredJobs = jobExecutions.filter(job => {
    const matchesSearch = searchTerm === "" || 
      job.jobName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.trigger?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesProject = selectedProjectId === "all" || job.projectId === selectedProjectId;
    const matchesStatus = statusFilter === "all" || job.status === statusFilter;
    const matchesJobType = jobTypeFilter === "all" || job.jobType === jobTypeFilter;
    
    // Additional RBAC: readers can only see jobs from projects they have access to
    if (userRole === 'reader') {
      const userProject = userProjects.find(p => p.id === job.projectId);
      if (!userProject) return false;
    }
    
    return matchesSearch && matchesProject && matchesStatus && matchesJobType;
  });

  // Get accessible projects for current user
  const accessibleProjects = userRole === 'admin' 
    ? userProjects 
    : userProjects.filter(p => p.role === 'admin' || p.role === 'reader');

  const handleTriggerJob = (job: JobExecution) => {
    console.log(`Triggering job: ${job.jobName}`);
    // Implementation would trigger the job
  };
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Job History</h2>
          <p className="text-gray-600">Track and manage your image processing job executions</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          {canExportHistory && (
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export History
            </Button>
          )}
        </div>
      </div>

      {/* Project Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Job Filtering</CardTitle>
          <CardDescription>Filter jobs by project and other criteria</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Project</label>
              <Select value={selectedProjectId} onValueChange={setSelectedProjectId}>
                <SelectTrigger>
                  <SelectValue placeholder="All Projects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Projects</SelectItem>
                  {accessibleProjects.map(project => (
                    <SelectItem key={project.id} value={project.id}>
                      <div className="flex items-center space-x-2">
                        <span>{project.name}</span>
                        <Badge variant={project.role === 'admin' ? 'default' : 'secondary'} className="text-xs">
                          {project.role}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="running">Running</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Job Type</label>
              <Select value={jobTypeFilter} onValueChange={setJobTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="manual">Manual</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="event">Event-based</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button 
                variant="outline" 
                onClick={() => {
                  setSelectedProjectId("all");
                  setStatusFilter("all");
                  setJobTypeFilter("all");
                  setSearchTerm("");
                }}
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex items-center space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search job executions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="text-sm text-muted-foreground">
          Showing {filteredJobs.length} of {jobExecutions.length} jobs
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Job Execution History</CardTitle>
          <CardDescription>Track your image processing jobs and their status</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Job Name</TableHead>
                <TableHead>Project</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Trigger/Schedule</TableHead>
                <TableHead>Images Processed</TableHead>
                <TableHead>Output Size</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Started / Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredJobs.length > 0 ? filteredJobs.map((job) => {
                const project = userProjects.find(p => p.id === job.projectId);
                const userProjectRole = project?.role;
                return (
                  <TableRow key={job.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <span>{job.jobName}</span>
                        {userRole === 'reader' && (
                          <Badge variant="outline" className="text-xs">
                            {userProjectRole}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <span>{project?.name}</span>
                        <Badge variant={project?.role === 'admin' ? 'default' : 'secondary'} className="text-xs">
                          {project?.role}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>{getJobTypeBadge(job.jobType)}</TableCell>
                    <TableCell>{getStatusBadge(job.status)}</TableCell>
                    <TableCell>
                      {job.jobType === 'scheduled' && (
                        <div>
                          <div className="text-sm">{job.schedule}</div>
                          {job.nextRun && job.status === 'scheduled' && (
                            <div className="text-xs text-muted-foreground">Next: {job.nextRun}</div>
                          )}
                        </div>
                      )}
                      {job.jobType === 'event' && (
                        <div>
                          <div className="text-sm">{job.trigger}</div>
                          <div className="text-xs text-muted-foreground">Event: {job.eventType}</div>
                        </div>
                      )}
                      {job.jobType === 'manual' && (
                        <span className="text-sm text-muted-foreground">Manual execution</span>
                      )}
                    </TableCell>
                    <TableCell>{job.imagesProcessed}</TableCell>
                    <TableCell>{job.outputSize}</TableCell>
                    <TableCell>{job.duration || (job.status === 'running' ? 'Running...' : job.status === 'scheduled' ? 'Pending' : 'N/A')}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm">{job.startTime}</span>
                        {canTriggerJobs && job.jobType === 'manual' && job.status === 'completed' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleTriggerJob(job)}
                            title="Re-run this job"
                          >
                            <Play className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                );
              }) : (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    <div className="text-muted-foreground">
                      <p>No jobs found matching the current filters.</p>
                      <p className="text-sm mt-1">Try adjusting your search criteria or filters.</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}