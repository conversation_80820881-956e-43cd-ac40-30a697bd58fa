import { Search, Plus, Download, ExternalLink, Key } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ToolsTab } from "./ToolsTab";
import type { ApiToken } from "./types";

interface ToolsManagementProps {
  apiTokens: ApiToken[];
  isTokenDialogOpen: boolean;
  onTokenDialogChange: (open: boolean) => void;
  onCreateToken: () => void;
  onRevokeToken: (tokenId: string) => void;
  onDownloadClient: () => void;
  onOpenDataPortal: () => void;
  tokenFormData: {
    name: string;
    permissions: string[];
    expiresIn: string;
  };
  onTokenFormDataChange: (data: any) => void;
}

export function ToolsManagement({
  apiTokens,
  isTokenDialogOpen,
  onTokenDialogChange,
  onCreateToken,
  onRevokeToken,
  onDownloadClient,
  onOpenDataPortal,
  tokenFormData,
  onTokenFormDataChange
}: ToolsManagementProps) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Tools & Access</h2>
          <p className="text-gray-600">Manage API tokens, download clients, and access data tools</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={onDownloadClient}>
            <Download className="h-4 w-4 mr-2" />
            Download Thick Client
          </Button>
          <Button variant="outline" onClick={onOpenDataPortal}>
            <ExternalLink className="h-4 w-4 mr-2" />
            Data Portal
          </Button>
          <Button onClick={() => onTokenDialogChange(true)}>
            <Key className="h-4 w-4 mr-2" />
            Create API Token
          </Button>
        </div>
      </div>

      <ToolsTab
        apiTokens={apiTokens}
        isTokenDialogOpen={isTokenDialogOpen}
        onTokenDialogChange={onTokenDialogChange}
        onCreateToken={onCreateToken}
        onRevokeToken={onRevokeToken}
        onDownloadClient={onDownloadClient}
        onOpenDataPortal={onOpenDataPortal}
        tokenFormData={tokenFormData}
        onTokenFormDataChange={onTokenFormDataChange}
      />
    </div>
  );
}