
import { useState } from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { FilesTab } from "./FilesTab";
import { FileDetailsDialog } from "./FileDetailsDialog";
import { getMockFileSystemByProject } from "./data";
import type { FileItem, ThickClientSession, UserProject } from "./types";

interface FilesManagementProps {
  expandedFolders: Set<string>;
  selectedFile: FileItem | null;
  thickClientSessions: ThickClientSession[];
  userProjects: UserProject[];
  userRole: 'admin' | 'reader';
  onToggleFolder: (path: string) => void;
  onSelectFile: (file: FileItem | null) => void;
  onTriggerJob?: (file: FileItem) => void;
}

export function FilesManagement({
  expandedFolders,
  selectedFile,
  thickClientSessions,
  userProjects,
  userRole,
  onToggleFolder,
  onSelectFile,
  onTriggerJob
}: FilesManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [viewDetailsFile, setViewDetailsFile] = useState<FileItem | null>(null);
  const [selectedProjectId, setSelectedProjectId] = useState<string>(userProjects[0]?.id || "1");
  const [projectFileSystem, setProjectFileSystem] = useState<FileItem[]>(() => 
    getMockFileSystemByProject(selectedProjectId)
  );

  // Update file system when project changes
  const handleProjectChange = (projectId: string) => {
    setSelectedProjectId(projectId);
    setProjectFileSystem(getMockFileSystemByProject(projectId));
  };


  const handleUploadImages = () => {
    // Simulate file upload dialog
    console.log('Opening file upload dialog...');
    alert('File upload dialog would open here. Files would be uploaded to the current project.');
  };

  const handleCreateFolder = () => {
    // Simulate create folder dialog
    console.log('Opening create folder dialog...');
    const folderName = prompt('Enter folder name:');
    if (folderName) {
      alert(`Folder "${folderName}" would be created in the current location.`);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold">Image Management</h2>
          <p className="text-gray-600">Browse and manage your satellite imagery</p>
        </div>
        <div className="flex items-center space-x-3">
          {/* Project Selection with Clear Label */}
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-muted-foreground whitespace-nowrap">Project </span>
            <div className="w-64">
              <Select value={selectedProjectId} onValueChange={handleProjectChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                  {userProjects.map(project => (
                    <SelectItem key={project.id} value={project.id}>
                      <div className="flex items-center space-x-2">
                        <span>{project.name}</span>
                        <Badge variant={project.role === 'admin' ? 'default' : 'secondary'}>
                          {project.role}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search images and folders..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Image Browser - Full Height */}
      <div className="flex-1 min-h-0">
        <FilesTab
          fileSystem={projectFileSystem}
          expandedFolders={expandedFolders}
          selectedFile={selectedFile}
          thickClientSessions={thickClientSessions}
          userRole={userRole}
          onToggleFolder={onToggleFolder}
          onSelectFile={onSelectFile}
          onViewDetails={setViewDetailsFile}
          onTriggerJob={onTriggerJob}
          onUploadImages={handleUploadImages}
          onCreateFolder={handleCreateFolder}
          searchTerm={searchTerm}
          fullHeight={true}
        />
      </div>

      {/* Image Details Dialog */}
      <FileDetailsDialog
        file={viewDetailsFile}
        open={viewDetailsFile !== null}
        onOpenChange={(open) => !open && setViewDetailsFile(null)}
      />
    </div>
  );
}
