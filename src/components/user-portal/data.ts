
import type { UserProject, JobExecution, AnalyticsData, FileItem, ThickClientSession, ApiToken } from "./types";

// Mock data for user's accessible projects
export const mockUserProjects: UserProject[] = [
  {
    id: "1",
    name: "Agricultural Monitoring",
    description: "Crop health assessment using multispectral imagery",
    role: "admin",
    status: "active",
    imageType: "Multispectral",
    region: "Midwest USA",
    tenantName: "Agricultural Data Hub",
    jobCount: 12,
    dataSize: "1.2 TB",
    lastActivity: "2024-06-18",
    adlsEndpoint: "https://agridatahubstorage.dfs.core.windows.net",
    containerName: "agricultural-monitoring"
  },
  {
    id: "2",
    name: "Urban Planning Analysis",
    description: "City development pattern analysis using SAR data",
    role: "reader",
    status: "active",
    imageType: "Synthetic Aperture Radar",
    region: "European Cities",
    tenantName: "Emergency Response Storage",
    jobCount: 8,
    dataSize: "850 GB",
    lastActivity: "2024-06-17",
    adlsEndpoint: "https://emergencyresponsestorage.dfs.core.windows.net",
    containerName: "urban-planning"
  }
];

// Mock job execution data - aligned with file system data
export const mockJobExecutions: JobExecution[] = [
  {
    id: "1",
    projectId: "1",
    jobName: "NDVI Analysis Pipeline",
    jobType: "event",
    eventType: "file_upload",
    trigger: "Auto-trigger on IMG_001_multispectral.tif upload",
    status: "completed",
    startTime: "2024-06-18 09:30:00",
    endTime: "2024-06-18 10:15:00",
    duration: "45 minutes",
    imagesProcessed: 1,
    outputSize: "158.1 KB"
  },
  {
    id: "2",
    projectId: "1",
    jobName: "Crop Health Assessment",
    jobType: "event",
    eventType: "file_upload", 
    trigger: "Auto-trigger on IMG_002_multispectral.tif upload",
    status: "running",
    startTime: "2024-06-18 14:45:00",
    imagesProcessed: 1,
    outputSize: "0 GB"
  },
  {
    id: "3",
    projectId: "2",
    jobName: "Urban Growth Detection",
    jobType: "manual",
    status: "failed",
    startTime: "2024-06-17 16:20:00",
    endTime: "2024-06-17 16:25:00",
    duration: "5 minutes",
    imagesProcessed: 0,
    outputSize: "0 GB"
  },
  {
    id: "4",
    projectId: "1",
    jobName: "Daily NDVI Monitoring",
    jobType: "scheduled",
    schedule: "Daily at 06:00 UTC",
    status: "scheduled",
    startTime: "2024-06-19 06:00:00",
    nextRun: "2024-06-19 06:00:00",
    imagesProcessed: 0,
    outputSize: "0 GB"
  },
  {
    id: "5",
    projectId: "1",
    jobName: "Weekly Crop Assessment",
    jobType: "scheduled",
    schedule: "Weekly (Monday 08:00 UTC)",
    status: "completed",
    startTime: "2024-06-17 08:00:00",
    endTime: "2024-06-17 09:15:00",
    duration: "75 minutes",
    nextRun: "2024-06-24 08:00:00",
    imagesProcessed: 342,
    outputSize: "4.8 GB"
  },
  {
    id: "6",
    projectId: "2",
    jobName: "New Image Upload Handler",
    jobType: "event",
    eventType: "file_upload",
    trigger: "On new satellite image upload",
    status: "completed",
    startTime: "2024-06-18 11:22:00",
    endTime: "2024-06-18 11:35:00",
    duration: "13 minutes",
    imagesProcessed: 1,
    outputSize: "156 MB"
  },
  {
    id: "7",
    projectId: "2",
    jobName: "Change Detection Alert",
    jobType: "event",
    eventType: "threshold_exceeded",
    trigger: "Change detection > 15%",
    status: "completed",
    startTime: "2024-06-17 14:45:00",
    endTime: "2024-06-17 14:52:00",
    duration: "7 minutes",
    imagesProcessed: 23,
    outputSize: "890 MB"
  },
  {
    id: "8",
    projectId: "1",
    jobName: "Legacy Archive Processing",
    jobType: "manual",
    status: "completed",
    startTime: "2024-06-10 08:00:00",
    endTime: "2024-06-10 09:30:00",
    duration: "90 minutes",
    imagesProcessed: 1,
    outputSize: "245 MB"
  }
];

// Get job executions by project
export const getJobExecutionsByProject = (projectId: string): JobExecution[] => {
  return mockJobExecutions.filter(job => job.projectId === projectId);
};

// Available job definitions for manual execution
export const getAvailableJobDefinitions = (projectId: string) => {
  if (projectId === "1") { // Agricultural Monitoring
    return [
      { id: "ndvi-analysis", name: "NDVI Analysis Pipeline", description: "Vegetation health analysis using NDVI calculations" },
      { id: "crop-health", name: "Crop Health Assessment", description: "Comprehensive crop condition analysis" },
      { id: "moisture-analysis", name: "Soil Moisture Analysis", description: "Soil moisture content estimation" },
      { id: "yield-prediction", name: "Crop Yield Prediction", description: "AI-based yield forecasting" },
      { id: "pest-detection", name: "Pest and Disease Detection", description: "Automated detection of crop health issues" }
    ];
  } else if (projectId === "2") { // Urban Planning
    return [
      { id: "urban-growth", name: "Urban Growth Detection", description: "Monitor urban expansion and development" },
      { id: "change-detection", name: "Change Detection Alert", description: "Detect significant land use changes" },
      { id: "building-detection", name: "Building Detection", description: "Identify new construction and structures" },
      { id: "road-mapping", name: "Road Network Mapping", description: "Extract and update road network data" },
      { id: "land-classification", name: "Land Cover Classification", description: "Classify land use types and coverage" }
    ];
  }
  return [];
};

// Calculate analytics data from job executions
export const getAnalyticsData = (jobExecutions: JobExecution[]): AnalyticsData => ({
  totalImagesProcessed: jobExecutions.reduce((sum, job) => sum + job.imagesProcessed, 0),
  totalJobsRun: jobExecutions.length,
  averageProcessingTime: "32 minutes",
  dataUploaded: "24.0 TB",
  dataGenerated: "8.7 TB",
  successRate: Math.round((jobExecutions.filter(j => j.status === 'completed').length / jobExecutions.filter(j => j.status !== 'scheduled').length) * 100)
});

// Get mock file system data by project
export const getMockFileSystemByProject = (projectId: string): FileItem[] => {
  const baseStructure: FileItem[] = [
    // Input folder structure
    { id: `${projectId}-input`, name: "input", type: "folder" as const, parentPath: "", fullPath: "input", status: "uploaded" as const, projectId },
    { id: `${projectId}-input-images`, name: "satellite-images-2024-06", type: "folder" as const, parentPath: "input", fullPath: "input/satellite-images-2024-06", status: "uploaded" as const, projectId },
    
    // Output folder structure  
    { id: `${projectId}-output`, name: "output", type: "folder" as const, parentPath: "", fullPath: "output", status: "uploaded" as const, projectId },
    { id: `${projectId}-output-processed`, name: "processed", type: "folder" as const, parentPath: "output", fullPath: "output/processed", status: "uploaded" as const, projectId },
    
    // Archive folder
    { id: `${projectId}-archive`, name: "archive", type: "folder" as const, parentPath: "", fullPath: "archive", status: "uploaded" as const, projectId }
  ];

  // Project-specific files
  if (projectId === "1") { // Agricultural Monitoring
    return [
      ...baseStructure,
      { id: "file1", name: "IMG_001_multispectral.tif", type: "file" as const, size: "2.1 GB", uploadDate: "2024-06-18 09:30", status: "processed" as const, parentPath: "input/satellite-images-2024-06", fullPath: "input/satellite-images-2024-06/IMG_001_multispectral.tif", associatedJobId: "1", autoTriggerEnabled: true, projectId, processingResults: { outputFiles: ["output/processed/IMG_001_NDVI.tif", "output/processed/IMG_001_analysis.json"], jobId: "1", completedAt: "2024-06-18 10:15" }},
      { id: "file2", name: "IMG_002_multispectral.tif", type: "file" as const, size: "2.3 GB", uploadDate: "2024-06-18 14:45", status: "processing" as const, progress: 65, parentPath: "input/satellite-images-2024-06", fullPath: "input/satellite-images-2024-06/IMG_002_multispectral.tif", associatedJobId: "2", autoTriggerEnabled: true, projectId },
      { id: "file3", name: "IMG_003_multispectral.tif", type: "file" as const, size: "1.9 GB", uploadDate: "2024-06-18 16:20", status: "uploading" as const, progress: 23, parentPath: "input/satellite-images-2024-06", fullPath: "input/satellite-images-2024-06/IMG_003_multispectral.tif", autoTriggerEnabled: true, projectId },
      { id: "out1", name: "IMG_001_NDVI.tif", type: "file" as const, size: "156 MB", uploadDate: "2024-06-18 10:15", status: "processed" as const, parentPath: "output/processed", fullPath: "output/processed/IMG_001_NDVI.tif", projectId },
      { id: "out2", name: "IMG_001_analysis.json", type: "file" as const, size: "2.1 KB", uploadDate: "2024-06-18 10:15", status: "processed" as const, parentPath: "output/processed", fullPath: "output/processed/IMG_001_analysis.json", projectId },
      { id: "arch1", name: "IMG_legacy_001.tif", type: "file" as const, size: "1.8 GB", uploadDate: "2024-06-10 08:00", status: "processed" as const, parentPath: "archive", fullPath: "archive/IMG_legacy_001.tif", projectId }
    ];
  } else if (projectId === "2") { // Urban Planning Analysis
    return [
      ...baseStructure,
      { id: "file4", name: "SAR_001_urban.tif", type: "file" as const, size: "1.5 GB", uploadDate: "2024-06-17 11:30", status: "processed" as const, parentPath: "input/satellite-images-2024-06", fullPath: "input/satellite-images-2024-06/SAR_001_urban.tif", associatedJobId: "6", autoTriggerEnabled: true, projectId },
      { id: "file5", name: "SAR_002_urban.tif", type: "file" as const, size: "1.7 GB", uploadDate: "2024-06-17 14:20", status: "processed" as const, parentPath: "input/satellite-images-2024-06", fullPath: "input/satellite-images-2024-06/SAR_002_urban.tif", associatedJobId: "7", autoTriggerEnabled: true, projectId },
      { id: "out3", name: "SAR_001_change_detection.tif", type: "file" as const, size: "89 MB", uploadDate: "2024-06-17 11:35", status: "processed" as const, parentPath: "output/processed", fullPath: "output/processed/SAR_001_change_detection.tif", projectId },
      { id: "out4", name: "SAR_002_urban_analysis.json", type: "file" as const, size: "1.5 KB", uploadDate: "2024-06-17 14:52", status: "processed" as const, parentPath: "output/processed", fullPath: "output/processed/SAR_002_urban_analysis.json", projectId }
    ];
  }
  
  return baseStructure; // Empty project
};

// Legacy export for backward compatibility
export const mockFileSystem: FileItem[] = getMockFileSystemByProject("1");

// Mock thick client sessions
export const mockThickClientSessions: ThickClientSession[] = [
  {
    id: "session1",
    projectId: "1", 
    clientVersion: "2.1.4",
    platform: "Windows 11",
    username: "john.smith",
    ipAddress: "*************",
    status: "connected",
    lastHeartbeat: "2024-06-18 16:45:32",
    connectedAt: "2024-06-18 14:20:15",
    apiToken: "tc_live_1k8f9d2h3j7s9",
    activeUploads: 1,
    totalUploaded: "15.2 GB"
  },
  {
    id: "session2", 
    projectId: "1",
    clientVersion: "2.1.3",
    platform: "macOS 14.5",
    username: "sarah.davis", 
    ipAddress: "*************",
    status: "idle",
    lastHeartbeat: "2024-06-18 16:42:18",
    connectedAt: "2024-06-18 09:15:42",
    apiToken: "tc_live_7n2m5k9p4x1e",
    activeUploads: 0,
    totalUploaded: "8.8 GB"
  }
];

// Mock API tokens
export const mockApiTokens: ApiToken[] = [
  {
    id: "token1",
    name: "John's Desktop Client",
    token: "tc_live_1k8f9d2h3j7s9a8c6v4b2n0m1",
    createdAt: "2024-06-15 10:30:00",
    lastUsed: "2024-06-18 16:45:32",
    expiresAt: "2024-12-15 10:30:00",
    permissions: ["upload", "download", "job_trigger", "status_read"],
    status: "active"
  },
  {
    id: "token2", 
    name: "Sarah's MacBook Client",
    token: "tc_live_7n2m5k9p4x1e3q5w8r2t6y9u4i",
    createdAt: "2024-06-10 14:15:00",
    lastUsed: "2024-06-18 16:42:18", 
    expiresAt: "2024-12-10 14:15:00",
    permissions: ["upload", "download", "status_read"],
    status: "active"
  }
];
