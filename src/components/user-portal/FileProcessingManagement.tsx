import { useState } from "react";
import { Search, Filter, Play, Download, RefreshCw, Zap, Eye, Images, Clock, AlertCircle, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { getStatusBadge } from "@/lib/badge-variants";
import { getMockFileSystemByProject } from "./data";
import type { FileItem, UserProject } from "./types";

interface FileProcessingManagementProps {
  fileSystem: FileItem[];
  userProjects: UserProject[];
  userRole?: 'admin' | 'reader';
}

export function FileProcessingManagement({ fileSystem, userProjects, userRole = 'admin' }: FileProcessingManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [userFilter, setUserFilter] = useState("all");
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
  const [isTriggerDialogOpen, setIsTriggerDialogOpen] = useState(false);
  const [isProcessingDetailsOpen, setIsProcessingDetailsOpen] = useState(false);
  const [selectedFileForDetails, setSelectedFileForDetails] = useState<FileItem | null>(null);
  const [selectedProjectId, setSelectedProjectId] = useState<string>(userProjects[0]?.id || "1");
  const [projectFileSystem, setProjectFileSystem] = useState<FileItem[]>(() => 
    getMockFileSystemByProject(selectedProjectId)
  );

  // Update file system when project changes
  const handleProjectChange = (projectId: string) => {
    setSelectedProjectId(projectId);
    setProjectFileSystem(getMockFileSystemByProject(projectId));
  };

  // Get current project details
  const currentProject = userProjects.find(p => p.id === selectedProjectId) || userProjects[0];
  
  // RBAC permissions
  const canTriggerJobs = userRole === 'admin' && currentProject?.role === 'admin';
  const canRefreshStatus = true; // All users can refresh status

  // Mock users for demonstration
  const mockUsers = [
    { id: "user1", name: "John Smith", email: "<EMAIL>" },
    { id: "user2", name: "Sarah Davis", email: "<EMAIL>" },
    { id: "user3", name: "Alice Johnson", email: "<EMAIL>" }
  ];

  // Helper function to get user for a file (mock implementation)
  const getFileUser = (file: FileItem) => {
    // Assign users based on file name pattern (mock logic)
    if (file.name.includes('001')) return mockUsers[0];
    if (file.name.includes('002')) return mockUsers[1];
    if (file.name.includes('003')) return mockUsers[1];
    if (file.name.includes('legacy')) return mockUsers[2];
    return mockUsers[0]; // default
  };

  // Filter to only show files (not folders) and apply search/status/user filters
  const processingFiles = projectFileSystem
    .filter(item => item.type === 'file')
    .filter(file => {
      const matchesSearch = searchTerm === "" || 
        file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        file.fullPath.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === "all" || file.status === statusFilter;
      
      const fileUser = getFileUser(file);
      const matchesUser = userFilter === "all" || fileUser.id === userFilter;
      
      return matchesSearch && matchesStatus && matchesUser;
    });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'uploaded': return 'bg-blue-500';
      case 'uploading': return 'bg-yellow-500';
      case 'processing': return 'bg-orange-500';
      case 'processed': return 'bg-green-500';
      case 'failed': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getProjectName = (file: FileItem) => {
    return currentProject?.name || 'Unknown Project';
  };

  const handleTriggerProcessing = (file: FileItem) => {
    setSelectedFile(file);
    setIsTriggerDialogOpen(true);
  };

  const confirmTriggerProcessing = () => {
    if (selectedFile) {
      console.log(`Triggering processing for ${selectedFile.name}`);
      // Here you would trigger the actual processing
      setIsTriggerDialogOpen(false);
      setSelectedFile(null);
    }
  };

  const handleViewProcessingDetails = (file: FileItem) => {
    setSelectedFileForDetails(file);
    setIsProcessingDetailsOpen(true);
  };

  // Generate mock processing logs for demonstration
  const getProcessingLogs = (file: FileItem) => {
    const baseLogs = [
      { timestamp: "2024-06-18 09:30:15", level: "INFO", message: `Image upload initiated for ${file.name}` },
      { timestamp: "2024-06-18 09:30:45", level: "INFO", message: "Image validation completed successfully" },
      { timestamp: "2024-06-18 09:31:00", level: "INFO", message: "Extracting metadata from satellite imagery" }
    ];

    if (file.status === 'processing') {
      return [
        ...baseLogs,
        { timestamp: "2024-06-18 09:31:30", level: "INFO", message: "Starting NDVI analysis pipeline" },
        { timestamp: "2024-06-18 09:32:15", level: "INFO", message: "Processing band calculations: Red, NIR" },
        { timestamp: "2024-06-18 09:33:00", level: "WARN", message: "Cloud coverage detected: 15% - applying mask" },
        { timestamp: "2024-06-18 09:33:45", level: "INFO", message: "Generating vegetation index maps..." }
      ];
    } else if (file.status === 'processed') {
      return [
        ...baseLogs,
        { timestamp: "2024-06-18 09:31:30", level: "INFO", message: "Starting NDVI analysis pipeline" },
        { timestamp: "2024-06-18 09:32:15", level: "INFO", message: "Processing band calculations: Red, NIR" },
        { timestamp: "2024-06-18 09:33:00", level: "WARN", message: "Cloud coverage detected: 15% - applying mask" },
        { timestamp: "2024-06-18 09:34:30", level: "INFO", message: "Vegetation index calculation completed" },
        { timestamp: "2024-06-18 09:35:15", level: "INFO", message: "Generating output images: NDVI.tif, analysis.json" },
        { timestamp: "2024-06-18 10:15:00", level: "SUCCESS", message: "Processing completed successfully" }
      ];
    } else if (file.status === 'failed') {
      return [
        ...baseLogs,
        { timestamp: "2024-06-18 09:31:30", level: "INFO", message: "Starting analysis pipeline" },
        { timestamp: "2024-06-18 09:32:15", level: "ERROR", message: "Insufficient image quality: corrupted bands detected" },
        { timestamp: "2024-06-18 09:32:30", level: "ERROR", message: "Processing failed: Invalid raster data" }
      ];
    }
    
    return baseLogs;
  };

  // Generate mock change detection for demonstration
  const getChangeDetection = (file: FileItem) => {
    if (file.name.includes('multispectral')) {
      return {
        vegetationChange: "+12.5%",
        waterBodyChange: "-3.2%",
        urbanExpansion: "+8.7%",
        soilMoisture: "Optimal (65%)",
        cropHealth: "Good",
        anomaliesDetected: 2,
        changedPixels: "23,456 pixels",
        confidenceScore: "94.2%"
      };
    } else if (file.name.includes('SAR')) {
      return {
        surfaceChange: "+15.8%",
        buildingDetection: "47 new structures",
        roadNetwork: "+2.3km",
        deforestation: "12.4 hectares",
        landslideRisk: "Low",
        floodExtent: "None detected",
        changedPixels: "31,289 pixels",
        confidenceScore: "89.7%"
      };
    }
    return null;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Image Processing</h2>
          <p className="text-gray-600">Monitor upload status and processing progress for your satellite imagery</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Status
          </Button>
        </div>
      </div>

      {/* Project Selection */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Project Selection</CardTitle>
          <CardDescription>Select a project to view its image processing status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <Select value={selectedProjectId} onValueChange={handleProjectChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                  {userProjects.map(project => (
                    <SelectItem key={project.id} value={project.id}>
                      <div className="flex items-center space-x-2">
                        <span>{project.name}</span>
                        <Badge variant={project.role === 'admin' ? 'default' : 'secondary'}>
                          {project.role}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {currentProject && (
              <div className="text-sm text-muted-foreground">
                <p><strong>Type:</strong> {currentProject.imageType}</p>
                <p><strong>Region:</strong> {currentProject.region}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search images..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="border rounded-md px-3 py-2 text-sm"
        >
          <option value="all">All Status</option>
          <option value="uploading">Uploading</option>
          <option value="uploaded">Uploaded</option>
          <option value="processing">Processing</option>
          <option value="processed">Processed</option>
          <option value="failed">Failed</option>
        </select>
        <select
          value={userFilter}
          onChange={(e) => setUserFilter(e.target.value)}
          className="border rounded-md px-3 py-2 text-sm"
        >
          <option value="all">All Users</option>
          {mockUsers.map(user => (
            <option key={user.id} value={user.id}>{user.name}</option>
          ))}
        </select>
      </div>

      {/* Processing Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        {/* Total Images Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Images</CardTitle>
            <Images className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{processingFiles.length}</div>
            <p className="text-xs text-muted-foreground">in project</p>
          </CardContent>
        </Card>
        
        {/* Status Cards */}
        {['uploading', 'uploaded', 'processing', 'processed', 'failed'].map(status => {
          const count = processingFiles.filter(f => f.status === status).length;
          return (
            <Card key={status}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium capitalize">{status}</CardTitle>
                <div className={`w-3 h-3 rounded-full ${getStatusColor(status)}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{count}</div>
                <p className="text-xs text-muted-foreground">images</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Images Processing Table */}
      <Card>
        <CardHeader>
          <CardTitle>Image Processing Status</CardTitle>
          <CardDescription>Track upload progress and processing status for all images</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Image Name</TableHead>
                <TableHead>Project</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Size</TableHead>
                <TableHead>Upload Date</TableHead>
                <TableHead>Auto-Trigger</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {processingFiles.map((file) => {
                const fileUser = getFileUser(file);
                return (
                  <TableRow key={file.id}>
                    <TableCell className="font-medium">{file.name}</TableCell>
                    <TableCell>{getProjectName(file)}</TableCell>
                    <TableCell>
                      <div>
                        <p className="text-sm font-medium">{fileUser.name}</p>
                        <p className="text-xs text-muted-foreground">{fileUser.email}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${getStatusColor(file.status)}`} />
                      <span className="capitalize">{file.status}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {file.progress !== undefined ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-16 h-2 bg-muted rounded-full">
                          <div 
                            className="h-2 bg-primary rounded-full transition-all duration-300" 
                            style={{ width: `${file.progress}%` }}
                          />
                        </div>
                        <span className="text-xs text-muted-foreground">{file.progress}%</span>
                      </div>
                    ) : (
                      <span className="text-xs text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>{file.size || 'N/A'}</TableCell>
                  <TableCell>{file.uploadDate || 'N/A'}</TableCell>
                  <TableCell>
                    {file.autoTriggerEnabled ? (
                      <div className="flex items-center space-x-1">
                        <Zap className="h-3 w-3 text-yellow-500" />
                        <span className="text-xs">Enabled</span>
                      </div>
                    ) : (
                      <span className="text-xs text-muted-foreground">Disabled</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {canTriggerJobs && file.status === 'uploaded' && !file.autoTriggerEnabled && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleTriggerProcessing(file)}
                        >
                          <Play className="h-3 w-3 mr-1" />
                          Process
                        </Button>
                      )}
                      {file.status === 'processed' && (
                        <Button size="sm" variant="outline">
                          <Download className="h-3 w-3 mr-1" />
                          Download
                        </Button>
                      )}
                      <Button 
                        size="sm" 
                        variant="ghost"
                        onClick={() => handleViewProcessingDetails(file)}
                        title="View processing details"
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Trigger Processing Dialog */}
      <Dialog open={isTriggerDialogOpen} onOpenChange={setIsTriggerDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Trigger Image Processing</DialogTitle>
            <DialogDescription>
              Start processing for {selectedFile?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-muted-foreground">
                This will start the satellite image processing workflow for this image.
                Processing typically takes 15-30 minutes depending on image size and complexity.
              </p>
            </div>
            {selectedFile && (
              <div className="bg-muted p-3 rounded-lg">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium">Image:</span> {selectedFile.name}
                  </div>
                  <div>
                    <span className="font-medium">Size:</span> {selectedFile.size}
                  </div>
                  <div>
                    <span className="font-medium">Project:</span> {getProjectName(selectedFile)}
                  </div>
                  <div>
                    <span className="font-medium">Status:</span> {selectedFile.status}
                  </div>
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsTriggerDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={confirmTriggerProcessing}>
              <Play className="h-4 w-4 mr-2" />
              Start Processing
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Processing Details Dialog */}
      <Dialog open={isProcessingDetailsOpen} onOpenChange={setIsProcessingDetailsOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Processing Details - {selectedFileForDetails?.name}</DialogTitle>
            <DialogDescription>
              Detailed processing information, logs, and change detection results
            </DialogDescription>
          </DialogHeader>
          
          {selectedFileForDetails && (
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="logs">Processing Logs</TabsTrigger>
                <TabsTrigger value="changes">Change Detection</TabsTrigger>
                <TabsTrigger value="metadata">Metadata</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Image Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div><strong>Name:</strong> {selectedFileForDetails.name}</div>
                        <div><strong>Size:</strong> {selectedFileForDetails.size}</div>
                        <div><strong>Status:</strong> 
                          <Badge className="ml-2" variant={selectedFileForDetails.status === 'processed' ? 'default' : selectedFileForDetails.status === 'failed' ? 'destructive' : 'secondary'}>
                            {selectedFileForDetails.status}
                          </Badge>
                        </div>
                        <div><strong>Upload Date:</strong> {selectedFileForDetails.uploadDate}</div>
                        <div><strong>Auto-trigger:</strong> {selectedFileForDetails.autoTriggerEnabled ? 'Yes' : 'No'}</div>
                        <div><strong>Progress:</strong> {selectedFileForDetails.progress || 100}%</div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Processing Summary</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="text-sm space-y-2">
                        {selectedFileForDetails.status === 'processed' && (
                          <div className="flex items-center space-x-2 text-green-600">
                            <CheckCircle className="h-4 w-4" />
                            <span>Processing completed successfully</span>
                          </div>
                        )}
                        {selectedFileForDetails.status === 'processing' && (
                          <div className="flex items-center space-x-2 text-blue-600">
                            <Clock className="h-4 w-4" />
                            <span>Currently processing...</span>
                          </div>
                        )}
                        {selectedFileForDetails.status === 'failed' && (
                          <div className="flex items-center space-x-2 text-red-600">
                            <AlertCircle className="h-4 w-4" />
                            <span>Processing failed</span>
                          </div>
                        )}
                        {selectedFileForDetails.associatedJobId && (
                          <p><strong>Job ID:</strong> {selectedFileForDetails.associatedJobId}</p>
                        )}
                        {selectedFileForDetails.processingResults && (
                          <div>
                            <p><strong>Output Images:</strong></p>
                            <ul className="list-disc list-inside ml-4">
                              {selectedFileForDetails.processingResults.outputFiles.map((file, idx) => (
                                <li key={idx} className="text-xs">{file}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="logs" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Processing Logs</CardTitle>
                    <CardDescription>Real-time processing logs and status updates</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-96 w-full">
                      <div className="space-y-2">
                        {getProcessingLogs(selectedFileForDetails).map((log, idx) => (
                          <div key={idx} className="flex items-start space-x-3 p-2 bg-muted/30 rounded-lg">
                            <div className="text-xs text-muted-foreground font-mono w-32">
                              {log.timestamp}
                            </div>
                            <Badge 
                              variant={log.level === 'ERROR' ? 'destructive' : log.level === 'WARN' ? 'secondary' : log.level === 'SUCCESS' ? 'default' : 'outline'}
                              className="text-xs"
                            >
                              {log.level}
                            </Badge>
                            <div className="text-sm flex-1">{log.message}</div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="changes" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Change Detection Results</CardTitle>
                    <CardDescription>Automated analysis of changes detected in satellite imagery</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {getChangeDetection(selectedFileForDetails) ? (
                      <div className="grid grid-cols-2 gap-4">
                        {Object.entries(getChangeDetection(selectedFileForDetails)!).map(([key, value]) => (
                          <div key={key} className="flex justify-between items-center p-2 bg-muted/30 rounded-lg">
                            <span className="text-sm font-medium capitalize">
                              {key.replace(/([A-Z])/g, ' $1').trim()}
                            </span>
                            <span className="text-sm font-bold">{value}</span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No change detection data available for this image type</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="metadata" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Image Metadata</CardTitle>
                    <CardDescription>Technical specifications and imagery metadata</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="space-y-2">
                        <h4 className="font-semibold">Image Properties</h4>
                        <div className="space-y-1">
                          <div><strong>Format:</strong> GeoTIFF</div>
                          <div><strong>Bands:</strong> {selectedFileForDetails.name.includes('multispectral') ? '4 (R, G, B, NIR)' : '2 (VV, VH)'}</div>
                          <div><strong>Resolution:</strong> {selectedFileForDetails.name.includes('multispectral') ? '10m/pixel' : '20m/pixel'}</div>
                          <div><strong>Projection:</strong> WGS84 / UTM Zone 33N</div>
                          <div><strong>Bit Depth:</strong> 16-bit</div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <h4 className="font-semibold">Acquisition Info</h4>
                        <div className="space-y-1">
                          <div><strong>Satellite:</strong> {selectedFileForDetails.name.includes('multispectral') ? 'Sentinel-2' : 'Sentinel-1'}</div>
                          <div><strong>Acquisition Date:</strong> 2024-06-15 10:30 UTC</div>
                          <div><strong>Cloud Coverage:</strong> {selectedFileForDetails.name.includes('multispectral') ? '15%' : 'N/A'}</div>
                          <div><strong>Sun Elevation:</strong> {selectedFileForDetails.name.includes('multispectral') ? '65.2°' : 'N/A'}</div>
                          <div><strong>Orbit:</strong> {selectedFileForDetails.name.includes('SAR') ? 'Descending' : 'N/A'}</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsProcessingDetailsOpen(false)}>
              Close
            </Button>
            {selectedFileForDetails?.status === 'processed' && (
              <Button>
                <Download className="h-4 w-4 mr-2" />
                Download Results
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
