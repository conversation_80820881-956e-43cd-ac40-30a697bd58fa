import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Download, Play, Zap, Calendar, Database, Activity } from "lucide-react";
import { getStatusBadge } from "@/lib/badge-variants";
import type { FileItem } from "./types";

interface FileDetailsDialogProps {
  file: FileItem | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function FileDetailsDialog({ file, open, onOpenChange }: FileDetailsDialogProps) {
  if (!file) return null;

  const formatFileSize = (bytes: string) => {
    // Convert size string (e.g., "2.1 GB") to a more detailed format
    return bytes;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {file.name}
            {getStatusBadge(file.status)}
          </DialogTitle>
          <DialogDescription>
            File details and processing information
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* File Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-muted-foreground">File Size</span>
                <p className="text-sm">{file.size || 'Unknown'}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-muted-foreground">Upload Date</span>
                <p className="text-sm">{file.uploadDate ? formatDate(file.uploadDate) : 'N/A'}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-muted-foreground">File Path</span>
                <p className="text-sm font-mono text-xs bg-muted p-2 rounded">{file.fullPath}</p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-muted-foreground">Status</span>
                <div className="mt-1">{getStatusBadge(file.status)}</div>
              </div>
              {file.progress !== undefined && (
                <div>
                  <span className="text-sm font-medium text-muted-foreground">Progress</span>
                  <div className="mt-2">
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${file.progress}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">{file.progress}% complete</p>
                  </div>
                </div>
              )}
              {file.autoTriggerEnabled && (
                <div>
                  <span className="text-sm font-medium text-muted-foreground">Auto-Processing</span>
                  <div className="flex items-center gap-1 mt-1">
                    <Zap className="h-3 w-3 text-yellow-500" />
                    <span className="text-xs">Enabled</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Processing Results */}
          {file.processingResults && (
            <div className="border-t pt-4">
              <h4 className="font-medium mb-3">Processing Results</h4>
              <div className="space-y-2">
                <div>
                  <span className="text-sm font-medium text-muted-foreground">Job ID</span>
                  <p className="text-sm font-mono">{file.processingResults.jobId}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-muted-foreground">Completed At</span>
                  <p className="text-sm">{formatDate(file.processingResults.completedAt)}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-muted-foreground">Output Files</span>
                  <div className="mt-1 space-y-1">
                    {file.processingResults.outputFiles.map((outputFile, index) => (
                      <div key={index} className="flex items-center justify-between text-xs bg-muted p-2 rounded">
                        <span className="font-mono">{outputFile}</span>
                        <Button size="sm" variant="outline" className="h-6 px-2">
                          <Download className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Associated Job */}
          {file.associatedJobId && (
            <div className="border-t pt-4">
              <h4 className="font-medium mb-3">Associated Processing Job</h4>
              <div className="flex items-center justify-between">
                <span className="text-sm font-mono">Job ID: {file.associatedJobId}</span>
                <Button size="sm" variant="outline">
                  <Activity className="h-4 w-4 mr-2" />
                  View Job Details
                </Button>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="border-t pt-4 flex justify-between">
            <div className="flex space-x-2">
              <Button size="sm" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              {file.type === 'file' && file.status === 'uploaded' && (
                <Button size="sm">
                  <Play className="h-4 w-4 mr-2" />
                  Trigger Processing
                </Button>
              )}
            </div>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}