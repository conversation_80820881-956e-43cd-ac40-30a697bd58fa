import { Search, Download, TrendingUp } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { AnalyticsTab } from "./AnalyticsTab";
import type { AnalyticsData, UserProject, JobExecution, ThickClientSession } from "./types";

interface AnalyticsManagementProps {
  analyticsData: AnalyticsData;
  userProjects: UserProject[];
  jobExecutions: JobExecution[];
  thickClientSessions?: ThickClientSession[];
}

export function AnalyticsManagement({
  analyticsData,
  userProjects,
  jobExecutions,
  thickClientSessions = []
}: AnalyticsManagementProps) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Dashboard</h2>
          <p className="text-gray-600">Monitor project performance and processing metrics</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <TrendingUp className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        </div>
      </div>

      <AnalyticsTab
        analyticsData={analyticsData}
        userProjects={userProjects}
        jobExecutions={jobExecutions}
        thickClientSessions={thickClientSessions}
      />
    </div>
  );
}