import { useState } from "react";
import {
  Download,
  Key,
  ExternalLink,
  Eye,
  Users,
  AlertCircle
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import type { ApiToken } from "./types";

interface ToolsTabProps {
  apiTokens: ApiToken[];
  isTokenDialogOpen: boolean;
  onTokenDialogChange: (open: boolean) => void;
  onCreateToken: () => void;
  onRevokeToken: (tokenId: string) => void;
  onDownloadClient: () => void;
  onOpenDataPortal: () => void;
  tokenFormData: {
    name: string;
    permissions: string[];
    expiresIn: string;
  };
  onTokenFormDataChange: (data: any) => void;
}

export function ToolsTab({
  apiTokens,
  isTokenDialogOpen,
  onTokenDialogChange,
  onCreateToken,
  onRevokeToken,
  onDownloadClient,
  onOpenDataPortal,
  tokenFormData,
  onTokenFormDataChange
}: ToolsTabProps) {
  return (
    <>
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Client Applications</CardTitle>
              <CardDescription>Download tools for data upload and processing</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full" onClick={onDownloadClient}>
                <Download className="h-4 w-4 mr-2" />
                Download Thick Client
              </Button>
              <div className="text-sm text-muted-foreground">
                <p>Latest version: v2.1.4</p>
                <p>Supports: Windows, macOS, Linux</p>
                <p>Features: Batch upload, monitoring, preprocessing</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>API Access Tokens</CardTitle>
              <CardDescription>Manage authentication tokens for Thick Client</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                className="w-full" 
                onClick={() => onTokenDialogChange(true)}
              >
                <Key className="h-4 w-4 mr-2" />
                Create New Token
              </Button>
              <div className="space-y-2">
                {apiTokens.map(token => (
                  <div key={token.id} className="flex items-center justify-between p-2 bg-muted rounded-lg">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">{token.name}</p>
                      <p className="text-xs text-muted-foreground">
                        Created: {token.createdAt}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Last used: {token.lastUsed || 'Never'}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={token.status === 'active' ? 'default' : 'destructive'}>
                        {token.status}
                      </Badge>
                      {token.status === 'active' && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => onRevokeToken(token.id)}
                        >
                          Revoke
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Data Portal</CardTitle>
              <CardDescription>Explore and download processed satellite data</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full" onClick={onOpenDataPortal}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Open Data Exploration Portal
              </Button>
              <div className="text-sm text-muted-foreground">
                <p>Browse processed imagery</p>
                <p>Download analysis results</p>
                <p>Interactive map visualization</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>API Documentation</CardTitle>
              <CardDescription>Integrate with our REST APIs</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full" variant="outline">
                <Eye className="h-4 w-4 mr-2" />
                View API Documentation
              </Button>
              <div className="text-sm text-muted-foreground">
                <p>RESTful APIs for job management</p>
                <p>WebSocket for real-time updates</p>
                <p>SDK available for Python, JavaScript</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Support Resources</CardTitle>
              <CardDescription>Get help and technical support</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full" variant="outline">
                <Users className="h-4 w-4 mr-2" />
                Contact Support
              </Button>
              <div className="text-sm text-muted-foreground">
                <p>24/7 technical support</p>
                <p>Knowledge base and tutorials</p>
                <p>Community forums</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* API Token Creation Dialog */}
      <Dialog open={isTokenDialogOpen} onOpenChange={onTokenDialogChange}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create API Access Token</DialogTitle>
            <DialogDescription>
              Generate a new authentication token for Thick Client access
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="tokenName">Token Name</Label>
              <Input
                id="tokenName"
                value={tokenFormData.name}
                onChange={(e) => onTokenFormDataChange({ ...tokenFormData, name: e.target.value })}
                placeholder="John's Desktop Client"
              />
            </div>
            
            <div className="space-y-2">
              <Label>Permissions</Label>
              <div className="grid grid-cols-2 gap-2">
                {['upload', 'download', 'job_trigger', 'status_read', 'file_delete'].map(permission => (
                  <div key={permission} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={permission}
                      checked={tokenFormData.permissions.includes(permission)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          onTokenFormDataChange({
                            ...tokenFormData,
                            permissions: [...tokenFormData.permissions, permission]
                          });
                        } else {
                          onTokenFormDataChange({
                            ...tokenFormData,
                            permissions: tokenFormData.permissions.filter(p => p !== permission)
                          });
                        }
                      }}
                      className="rounded"
                    />
                    <Label htmlFor={permission} className="text-sm capitalize">
                      {permission.replace('_', ' ')}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="expiresIn">Expires In</Label>
              <Select value={tokenFormData.expiresIn} onValueChange={(value) => onTokenFormDataChange({ ...tokenFormData, expiresIn: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="6months">6 Months</SelectItem>
                  <SelectItem value="1year">1 Year</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="p-4 bg-muted rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <AlertCircle className="h-4 w-4 text-yellow-500" />
                <span className="font-medium text-sm">Security Notice</span>
              </div>
              <p className="text-sm text-muted-foreground">
                The token will be displayed only once after creation. Make sure to copy and securely store it in your Thick Client configuration.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => onTokenDialogChange(false)}>
              Cancel
            </Button>
            <Button onClick={onCreateToken} disabled={!tokenFormData.name.trim()}>
              <Key className="h-4 w-4 mr-2" />
              Create Token
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}