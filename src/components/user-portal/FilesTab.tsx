
import { useState } from "react";
import {
  FolderOpen,
  File,
  FileText,
  Image,
  ChevronRight,
  ChevronDown,
  RefreshCw,
  Download,
  Play,
  Trash2,
  Activity,
  Upload,
  Wifi,
  WifiOff,
  Monitor,
  Zap,
  Eye,
  Plus
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import type { FileItem, ThickClientSession } from "./types";

interface FilesTabProps {
  fileSystem: FileItem[];
  expandedFolders: Set<string>;
  selectedFile: FileItem | null;
  thickClientSessions: ThickClientSession[];
  userRole?: 'admin' | 'reader';
  onToggleFolder: (path: string) => void;
  onSelectFile: (file: FileItem | null) => void;
  onViewDetails?: (file: FileItem) => void;
  onTriggerJob?: (file: FileItem) => void;
  onUploadImages?: () => void;
  onCreateFolder?: () => void;
  searchTerm?: string;
  fullHeight?: boolean;
}

export function FilesTab({
  fileSystem,
  expandedFolders,
  selectedFile,
  thickClientSessions,
  userRole = 'admin',
  onToggleFolder,
  onSelectFile,
  onViewDetails,
  onTriggerJob,
  onUploadImages,
  onCreateFolder,
  searchTerm = "",
  fullHeight = false
}: FilesTabProps) {
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [currentFolder, setCurrentFolder] = useState<string>('');
  
  // Get current folder context for determining available actions
  const getCurrentFolderContext = () => {
    if (!currentFolder) return 'mixed';
    // Check if the current folder is input or any subfolder of input
    if (currentFolder === 'input' || currentFolder.startsWith('input/')) return 'input';
    // Check if the current folder is output or any subfolder of output
    if (currentFolder === 'output' || currentFolder.startsWith('output/')) return 'output';
    // Check if the current folder is archive or any subfolder of archive
    if (currentFolder === 'archive' || currentFolder.startsWith('archive/')) return 'archive';
    return 'mixed';
  };
  
  const folderContext = getCurrentFolderContext();
  
  
  
  // RBAC permissions
  const canDelete = userRole === 'admin';
  const canTriggerJobs = userRole === 'admin';

  const getFileIcon = (file: FileItem) => {
    if (file.type === 'folder') {
      return expandedFolders.has(file.fullPath) ? ChevronDown : ChevronRight;
    }
    if (file.name.endsWith('.tif') || file.name.endsWith('.tiff') || file.name.endsWith('.jpg') || file.name.endsWith('.png')) {
      return Image;
    }
    if (file.name.endsWith('.json') || file.name.endsWith('.txt') || file.name.endsWith('.csv')) {
      return FileText;
    }
    return File;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'uploading': return 'bg-blue-500';
      case 'uploaded': return 'bg-green-500';
      case 'processing': return 'bg-yellow-500';
      case 'processed': return 'bg-emerald-500';
      case 'failed': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const handleFileSelect = (fileId: string, checked: boolean) => {
    setSelectedFiles(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(fileId);
        // Auto-detect folder context from selected file if no current folder is set
        if (!currentFolder) {
          const file = fileSystem.find(f => f.id === fileId);
          if (file) {
            const pathParts = file.fullPath.split('/');
            if (pathParts.length > 1) {
              const detectedFolder = pathParts.slice(0, -1).join('/');
              setCurrentFolder(detectedFolder);
            }
          }
        }
      } else {
        newSet.delete(fileId);
      }
      return newSet;
    });
  };

  const handleFolderSelect = (folderPath: string, checked: boolean) => {
    // Get all files in this folder and its subfolders
    const folderFiles = fileSystem.filter(f => 
      f.type === 'file' && f.fullPath.startsWith(folderPath + '/')
    );
    
    setSelectedFiles(prev => {
      const newSet = new Set(prev);
      if (checked) {
        // Add all files in the folder
        folderFiles.forEach(file => newSet.add(file.id));
        // Set current folder context
        setCurrentFolder(folderPath);
      } else {
        // Remove all files in the folder
        folderFiles.forEach(file => newSet.delete(file.id));
      }
      return newSet;
    });
  };

  // Check folder selection state
  const getFolderSelectionState = (folderPath: string) => {
    const folderFiles = fileSystem.filter(f => 
      f.type === 'file' && f.fullPath.startsWith(folderPath + '/')
    );
    
    if (folderFiles.length === 0) return 'unchecked';
    
    const selectedInFolder = folderFiles.filter(f => selectedFiles.has(f.id));
    
    if (selectedInFolder.length === 0) return 'unchecked';
    if (selectedInFolder.length === folderFiles.length) return 'checked';
    return 'indeterminate';
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // Select all files in current folder or all files if no folder selected
      const filesToSelect = currentFolder 
        ? fileSystem.filter(f => f.type === 'file' && f.fullPath.startsWith(currentFolder))
        : fileSystem.filter(f => f.type === 'file');
      const fileIds = filesToSelect.map(f => f.id);
      setSelectedFiles(new Set(fileIds));
    } else {
      setSelectedFiles(new Set());
    }
  };

  const handleBulkDelete = () => {
    console.log('Bulk delete images:', Array.from(selectedFiles));
    setSelectedFiles(new Set());
  };

  const handleBulkTriggerJob = () => {
    const selectedFileObjects = fileSystem.filter(f => selectedFiles.has(f.id));
    console.log('Bulk trigger job for images in folder:', currentFolder, selectedFileObjects);
    setSelectedFiles(new Set());
  };

  const handleBulkDownload = () => {
    const selectedFileObjects = fileSystem.filter(f => selectedFiles.has(f.id));
    console.log('Bulk download images from folder:', currentFolder, selectedFileObjects);
    // Simulate download
    alert(`Downloading ${selectedFiles.size} files from ${folderContext} folder`);
    setSelectedFiles(new Set());
  };

  const selectedFileObjects = fileSystem.filter(f => selectedFiles.has(f.id));
  // Update selectable files to be folder-aware
  const selectableFiles = currentFolder 
    ? fileSystem.filter(f => f.type === 'file' && f.fullPath.startsWith(currentFolder))
    : fileSystem.filter(f => f.type === 'file');
  const allSelected = selectableFiles.length > 0 && selectedFiles.size === selectableFiles.length;

  const renderFileTree = (files: FileItem[], parentPath: string = "", level: number = 0) => {
    const filteredFiles = files.filter(file => file.parentPath === parentPath);
    
    console.log(`Rendering level ${level}, parentPath: "${parentPath}", found ${filteredFiles.length} images:`, filteredFiles.map(f => f.name));
    
    return filteredFiles.map(file => {
      const Icon = getFileIcon(file);
      const hasChildren = files.some(f => f.parentPath === file.fullPath);
      const isExpanded = expandedFolders.has(file.fullPath);
      const isSelected = selectedFiles.has(file.id);
      
      return (
        <div key={file.id}>
          <div 
            className={`flex items-center space-x-2 p-2 rounded-lg cursor-pointer hover:bg-muted/50 ${
              selectedFile?.id === file.id ? 'bg-muted' : ''
            } ${
              file.type === 'folder' && file.fullPath === currentFolder ? 'bg-primary/10 border border-primary/20' : ''
            }`}
            style={{ paddingLeft: `${level * 20 + 8}px` }}
          >
            {file.type === 'file' && (
              <Checkbox
                checked={isSelected}
                onCheckedChange={(checked) => handleFileSelect(file.id, checked as boolean)}
                onClick={(e) => e.stopPropagation()}
              />
            )}
            
            {file.type === 'folder' && (
              <Checkbox
                checked={getFolderSelectionState(file.fullPath) === 'checked'}
                ref={(ref) => {
                  if (ref) {
                    const state = getFolderSelectionState(file.fullPath);
                    const inputElement = ref.querySelector('input');
                    if (inputElement) {
                      inputElement.indeterminate = state === 'indeterminate';
                    }
                  }
                }}
                onCheckedChange={(checked) => handleFolderSelect(file.fullPath, checked as boolean)}
                onClick={(e) => e.stopPropagation()}
              />
            )}
            
            <div 
              className="flex items-center space-x-2 flex-1"
              onClick={() => {
                if (file.type === 'folder') {
                  onToggleFolder(file.fullPath);
                  setCurrentFolder(file.fullPath);
                  // Clear selections when switching folders
                  setSelectedFiles(new Set());
                } else {
                  onSelectFile(file);
                }
              }}
            >
              <Icon className={`h-4 w-4 ${
                file.type === 'folder' 
                  ? file.name === 'input' 
                    ? 'text-blue-500' 
                    : file.name === 'output' 
                    ? 'text-green-500' 
                    : file.name === 'archive' 
                    ? 'text-orange-500' 
                    : 'text-muted-foreground'
                  : 'text-muted-foreground'
              }`} />
              <span className="flex-1 text-sm">{file.name}</span>
              
              {file.type === 'file' && (
                <div className="flex items-center space-x-2">
                  {file.autoTriggerEnabled && (
                    <div title="Auto-trigger enabled">
                      <Zap className="h-3 w-3 text-yellow-500" />
                    </div>
                  )}
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(file.status)}`} title={file.status} />
                  {file.progress !== undefined && file.progress < 100 && (
                    <span className="text-xs text-muted-foreground">{file.progress}%</span>
                  )}
                  <span className="text-xs text-muted-foreground">{file.size}</span>
                  {canTriggerJobs && file.status === 'uploaded' && !file.autoTriggerEnabled && onTriggerJob && (
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        onTriggerJob(file);
                      }}
                      title="Trigger processing job"
                    >
                      <Play className="h-3 w-3" />
                    </Button>
                  )}
                  {onViewDetails && (
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        onViewDetails(file);
                      }}
                    >
                      <Eye className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
          
          {file.type === 'folder' && isExpanded && hasChildren && (
            <div>
              {renderFileTree(files, file.fullPath, level + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  // Filter images based on search term
  const filteredFileSystem = searchTerm
    ? fileSystem.filter(file => 
        file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        file.fullPath.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : fileSystem;

  if (fullHeight) {
    return (
      <Card className="h-full flex flex-col">
        <CardHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <span>Image Browser</span>
                {currentFolder && (
                  <Badge variant={folderContext === 'input' ? 'default' : folderContext === 'output' ? 'secondary' : 'outline'}>
                    {folderContext} folder
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                Browse and manage your satellite images
                {currentFolder && ` • Active folder: ${currentFolder}`}
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              {selectedFiles.size > 0 && (
                <>
                  <Badge variant="secondary">
                    {selectedFiles.size} selected {currentFolder && `in ${folderContext}`}
                  </Badge>
                  {/* Show folder-specific actions */}
                  {folderContext === 'input' && canTriggerJobs && selectedFileObjects.some(f => f.status === 'uploaded' || f.status === 'processed') && (
                    <Button variant="outline" size="sm" onClick={handleBulkTriggerJob}>
                      <Play className="h-4 w-4 mr-2" />
                      Process Selected
                    </Button>
                  )}
                  {(folderContext === 'output' || folderContext === 'archive') && (
                    <Button variant="outline" size="sm" onClick={handleBulkDownload}>
                      <Download className="h-4 w-4 mr-2" />
                      Download Selected
                    </Button>
                  )}
                  {folderContext === 'mixed' && canTriggerJobs && (
                    <Button variant="outline" size="sm" onClick={handleBulkTriggerJob}>
                      <Play className="h-4 w-4 mr-2" />
                      Process Selected
                    </Button>
                  )}
                  {folderContext === 'mixed' && (
                    <Button variant="outline" size="sm" onClick={handleBulkDownload}>
                      <Download className="h-4 w-4 mr-2" />
                      Download Selected
                    </Button>
                  )}
                  {canDelete && folderContext === 'input' && (
                    <Button variant="outline" size="sm" onClick={handleBulkDelete}>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Selected
                    </Button>
                  )}
                </>
              )}
              {canTriggerJobs && onUploadImages && (folderContext === 'input' || folderContext === 'mixed') && (
                <Button variant="outline" size="sm" onClick={onUploadImages}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Images
                </Button>
              )}
              {canTriggerJobs && onCreateFolder && (folderContext === 'input' || folderContext === 'mixed') && (
                <Button variant="outline" size="sm" onClick={onCreateFolder}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Folder
                </Button>
              )}
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
          {selectableFiles.length > 0 && (
            <div className="flex items-center space-x-2 pt-2">
              <Checkbox
                checked={allSelected}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-muted-foreground">
                Select all images ({selectableFiles.length}) {currentFolder && `in ${folderContext} folder`}
              </span>
            </div>
          )}
        </CardHeader>
        <CardContent className="flex-1 min-h-0">
          <div className="space-y-1 h-full overflow-y-auto">
            {renderFileTree(filteredFileSystem)}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Image Browser */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <span>Image Browser</span>
                    {currentFolder && (
                      <Badge variant={folderContext === 'input' ? 'default' : folderContext === 'output' ? 'secondary' : 'outline'}>
                        {folderContext} folder
                      </Badge>
                    )}
                  </CardTitle>
                  <CardDescription>
                    Browse and manage your satellite images
                    {currentFolder && ` • Active folder: ${currentFolder}`}
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  {selectedFiles.size > 0 && (
                    <>
                      <Badge variant="secondary">
                        {selectedFiles.size} selected {currentFolder && `in ${folderContext}`}
                      </Badge>
                      {/* Show folder-specific actions */}
                      {folderContext === 'input' && canTriggerJobs && selectedFileObjects.some(f => f.status === 'uploaded' || f.status === 'processed') && (
                        <Button variant="outline" size="sm" onClick={handleBulkTriggerJob}>
                          <Play className="h-4 w-4 mr-2" />
                          Process
                        </Button>
                      )}
                      {(folderContext === 'output' || folderContext === 'archive') && (
                        <Button variant="outline" size="sm" onClick={handleBulkDownload}>
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      )}
                      {folderContext === 'mixed' && canTriggerJobs && (
                        <Button variant="outline" size="sm" onClick={handleBulkTriggerJob}>
                          <Play className="h-4 w-4 mr-2" />
                          Process
                        </Button>
                      )}
                      {folderContext === 'mixed' && (
                        <Button variant="outline" size="sm" onClick={handleBulkDownload}>
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      )}
                      {canDelete && folderContext === 'input' && (
                        <Button variant="outline" size="sm" onClick={handleBulkDelete}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </Button>
                      )}
                    </>
                  )}
                  {canTriggerJobs && onUploadImages && (folderContext === 'input' || folderContext === 'mixed') && (
                    <Button variant="outline" size="sm" onClick={onUploadImages}>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload
                    </Button>
                  )}
                  {canTriggerJobs && onCreateFolder && (folderContext === 'input' || folderContext === 'mixed') && (
                    <Button variant="outline" size="sm" onClick={onCreateFolder}>
                      <Plus className="h-4 w-4 mr-2" />
                      New Folder
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                </div>
              </div>
              {selectableFiles.length > 0 && (
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={allSelected}
                    onCheckedChange={handleSelectAll}
                  />
                  <span className="text-sm text-muted-foreground">
                    Select all images ({selectableFiles.length}) {currentFolder && `in ${folderContext} folder`}
                  </span>
                </div>
              )}
            </CardHeader>
            <CardContent>
              <div className="space-y-1 max-h-96 overflow-y-auto">
                {renderFileTree(filteredFileSystem)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Image Details & Actions */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Image Details</CardTitle>
              <CardDescription>
                {selectedFile ? `Details for ${selectedFile.name}` : 'Select an image to view details'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedFile ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-muted-foreground">Image Name</span>
                      <p className="text-foreground">{selectedFile.name}</p>
                    </div>
                    <div>
                      <span className="font-medium text-muted-foreground">Size</span>
                      <p className="text-foreground">{selectedFile.size || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-muted-foreground">Status</span>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${getStatusColor(selectedFile.status)}`} />
                        <span className="capitalize">{selectedFile.status}</span>
                        {selectedFile.progress !== undefined && selectedFile.progress < 100 && (
                          <span>({selectedFile.progress}%)</span>
                        )}
                      </div>
                    </div>
                    <div>
                      <span className="font-medium text-muted-foreground">Upload Date</span>
                      <p className="text-foreground">{selectedFile.uploadDate || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-muted-foreground">Full Path</span>
                      <p className="text-foreground font-mono text-xs">{selectedFile.fullPath}</p>
                    </div>
                    <div>
                      <span className="font-medium text-muted-foreground">Auto-trigger</span>
                      <div className="flex items-center space-x-2">
                        {selectedFile.autoTriggerEnabled ? (
                          <>
                            <Zap className="h-3 w-3 text-yellow-500" />
                            <span className="text-success">Enabled</span>
                          </>
                        ) : (
                          <span className="text-muted-foreground">Disabled</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {selectedFile.associatedJobId && (
                    <div className="p-3 bg-muted rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <Activity className="h-4 w-4 text-primary" />
                        <span className="font-medium">Associated Job</span>
                      </div>
                      <p className="text-sm text-muted-foreground">Job ID: {selectedFile.associatedJobId}</p>
                      {selectedFile.processingResults && (
                        <div className="mt-2">
                          <p className="text-sm font-medium">Processing Results:</p>
                          <ul className="text-sm text-muted-foreground mt-1">
                            {selectedFile.processingResults.outputFiles.map((file, idx) => (
                              <li key={idx} className="flex items-center space-x-1">
                                <File className="h-3 w-3" />
                                <span>{file}</span>
                              </li>
                            ))}
                          </ul>
                          <p className="text-xs text-muted-foreground mt-1">
                            Completed: {selectedFile.processingResults.completedAt}
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    {canTriggerJobs && selectedFile.status === 'uploaded' && (
                      <Button variant="outline" size="sm">
                        <Play className="h-4 w-4 mr-2" />
                        Process
                      </Button>
                    )}
                    {canDelete && (
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FolderOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Select an image to view its details and available actions</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Upload Progress & Thick Client Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upload Progress */}
        <Card>
          <CardHeader>
            <CardTitle>Upload Progress</CardTitle>
            <CardDescription>Real-time upload status from Thick Client</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {fileSystem
                .filter(file => file.status === 'uploading' || file.status === 'processing')
                .map(file => (
                  <div key={file.id} className="flex items-center space-x-3">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">{file.name}</span>
                        <span className="text-sm text-muted-foreground">{file.progress}%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-300 ${
                            file.status === 'uploading' ? 'bg-blue-500' : 'bg-yellow-500'
                          }`}
                          style={{ width: `${file.progress}%` }}
                        />
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-muted-foreground capitalize">{file.status}</span>
                        <span className="text-xs text-muted-foreground">{file.size}</span>
                      </div>
                    </div>
                  </div>
                ))}
              {fileSystem.filter(file => file.status === 'uploading' || file.status === 'processing').length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  <Upload className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No active uploads or processing</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Thick Client Status */}
        <Card>
          <CardHeader>
            <CardTitle>Thick Client Status</CardTitle>
            <CardDescription>Connected client sessions and heartbeat monitoring</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {thickClientSessions.map(session => (
                <div key={session.id} className="flex items-center space-x-3 p-3 bg-muted rounded-lg">
                  <div className="flex-shrink-0">
                    {session.status === 'connected' ? (
                      <Wifi className="h-5 w-5 text-green-500" />
                    ) : session.status === 'idle' ? (
                      <Monitor className="h-5 w-5 text-yellow-500" />
                    ) : (
                      <WifiOff className="h-5 w-5 text-red-500" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">{session.username}</p>
                      <Badge variant={session.status === 'connected' ? 'default' : session.status === 'idle' ? 'secondary' : 'destructive'}>
                        {session.status}
                      </Badge>
                    </div>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <p>{session.platform} - v{session.clientVersion}</p>
                      <p>IP: {session.ipAddress}</p>
                      <p>Last heartbeat: {session.lastHeartbeat}</p>
                      <div className="flex items-center justify-between">
                        <span>Active uploads: {session.activeUploads}</span>
                        <span>Total: {session.totalUploaded}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
