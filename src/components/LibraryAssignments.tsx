import { useState } from "react";
import { Building2, Code, Check, X, Search, Filter } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface Organization {
  id: string;
  name: string;
  status: 'active' | 'inactive';
  isInternal: boolean;
  assignedLibraries: string[];
}

interface JobLibrary {
  id: string;
  name: string;
  description: string;
  version: string;
  type: 'code' | 'arm-template' | 'pre-built';
  status: 'active' | 'inactive' | 'draft';
  runtime: 'dotnet' | 'nodejs' | 'python' | 'java';
  assignedOrgs: string[];
}

interface Assignment {
  orgId: string;
  orgName: string;
  libraryId: string;
  libraryName: string;
  assignedDate: string;
  status: 'active' | 'inactive';
}

export function LibraryAssignments() {
  const [organizations] = useState<Organization[]>([
    {
      id: "1",
      name: "Versar Inc",
      status: "active",
      isInternal: true,
      assignedLibraries: ["1", "2"]
    },
    {
      id: "2",
      name: "Acme Corporation",
      status: "active",
      isInternal: false,
      assignedLibraries: ["3"]
    }
  ]);

  const [jobLibraries] = useState<JobLibrary[]>([
    {
      id: "1",
      name: "Data Processing Library",
      description: "Processes CSV files and transforms data",
      version: "1.2.0",
      type: "code",
      status: "active",
      runtime: "dotnet",
      assignedOrgs: ["1"]
    },
    {
      id: "2",
      name: "Reporting Engine",
      description: "Generates reports from processed data",
      version: "1.0.0",
      type: "arm-template",
      status: "active",
      runtime: "nodejs",
      assignedOrgs: ["1"]
    },
    {
      id: "3",
      name: "API Integration Service",
      description: "Integrates with external APIs",
      version: "0.9.0",
      type: "pre-built",
      status: "active",
      runtime: "python",
      assignedOrgs: ["2"]
    }
  ]);

  const [assignments, setAssignments] = useState<Assignment[]>([
    {
      orgId: "1",
      orgName: "Versar Inc",
      libraryId: "1",
      libraryName: "Data Processing Library",
      assignedDate: "2024-01-15",
      status: "active"
    },
    {
      orgId: "1",
      orgName: "Versar Inc",
      libraryId: "2",
      libraryName: "Reporting Engine",
      assignedDate: "2024-02-01",
      status: "active"
    },
    {
      orgId: "2",
      orgName: "Acme Corporation",
      libraryId: "3",
      libraryName: "API Integration Service",
      assignedDate: "2024-03-01",
      status: "active"
    }
  ]);

  const [selectedOrg, setSelectedOrg] = useState<string>("");
  const [selectedLibraries, setSelectedLibraries] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  const handleAssignLibraries = () => {
    if (!selectedOrg || selectedLibraries.length === 0) return;

    const org = organizations.find(o => o.id === selectedOrg);
    if (!org) return;

    const newAssignments: Assignment[] = selectedLibraries.map(libraryId => {
      const library = jobLibraries.find(l => l.id === libraryId);
      return {
        orgId: selectedOrg,
        orgName: org.name,
        libraryId,
        libraryName: library?.name || "",
        assignedDate: new Date().toISOString().split('T')[0],
        status: "active" as const
      };
    });

    setAssignments([...assignments, ...newAssignments]);
    setSelectedLibraries([]);
  };

  const handleUnassignLibrary = (orgId: string, libraryId: string) => {
    setAssignments(assignments.filter(a => !(a.orgId === orgId && a.libraryId === libraryId)));
  };

  const toggleLibrarySelection = (libraryId: string) => {
    setSelectedLibraries(prev =>
      prev.includes(libraryId)
        ? prev.filter(id => id !== libraryId)
        : [...prev, libraryId]
    );
  };

  const getAvailableLibraries = () => {
    if (!selectedOrg) return [];
    
    const orgAssignedLibraries = assignments
      .filter(a => a.orgId === selectedOrg)
      .map(a => a.libraryId);
    
    return jobLibraries.filter(library => 
      !orgAssignedLibraries.includes(library.id) && 
      library.status === 'active' &&
      (searchTerm === "" || library.name.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const getOrgAssignments = (orgId: string) => {
    return assignments.filter(a => a.orgId === orgId);
  };

  const getLibraryAssignments = (libraryId: string) => {
    return assignments.filter(a => a.libraryId === libraryId);
  };

  const filteredOrganizations = organizations.filter(org => {
    if (statusFilter === "all") return true;
    return org.status === statusFilter;
  });

  const getStatusBadge = (status: string) => {
    const variants: Record<string, 'default' | 'destructive' | 'secondary'> = {
      active: 'default',
      inactive: 'secondary',
      draft: 'destructive'
    };
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>;
  };

  const getTypeBadge = (type: string) => {
    const colors: Record<string, string> = {
      code: 'bg-blue-100 text-blue-800',
      'arm-template': 'bg-green-100 text-green-800',
      'pre-built': 'bg-purple-100 text-purple-800'
    };
    return <Badge className={colors[type] || 'bg-gray-100 text-gray-800'}>{type}</Badge>;
  };

  return (
    <div className="p-6 space-y-6">
      <Tabs defaultValue="assign" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="assign">Assign Libraries</TabsTrigger>
          <TabsTrigger value="org-view">By Organization</TabsTrigger>
          <TabsTrigger value="library-view">By Library</TabsTrigger>
        </TabsList>

        <TabsContent value="assign" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Assign Job Libraries</CardTitle>
              <CardDescription>
                Select an organization and choose libraries to assign
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Select Organization</label>
                  <Select value={selectedOrg} onValueChange={setSelectedOrg}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose an organization" />
                    </SelectTrigger>
                    <SelectContent>
                      {organizations.map((org) => (
                        <SelectItem key={org.id} value={org.id}>
                          {org.name} ({org.status})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Search Libraries</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search job libraries..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              {selectedOrg && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Available Libraries</h3>
                    <Button 
                      onClick={handleAssignLibraries}
                      disabled={selectedLibraries.length === 0}
                    >
                      Assign Selected ({selectedLibraries.length})
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-1 gap-4">
                    {getAvailableLibraries().map((library) => (
                      <Card key={library.id} className="cursor-pointer hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                          <div className="flex items-center space-x-4">
                            <Checkbox
                              checked={selectedLibraries.includes(library.id)}
                              onCheckedChange={() => toggleLibrarySelection(library.id)}
                            />
                            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                              <Code className="h-4 w-4 text-purple-600" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <h4 className="font-medium">{library.name}</h4>
                                <span className="text-sm text-gray-500">v{library.version}</span>
                                {getTypeBadge(library.type)}
                              </div>
                              <p className="text-sm text-gray-600 mt-1">{library.description}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                  
                  {getAvailableLibraries().length === 0 && (
                    <div className="text-center py-8">
                      <Code className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">No available libraries to assign</p>
                      <p className="text-sm text-gray-400">All active libraries are already assigned to this organization</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="org-view" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Organizations and Their Libraries</CardTitle>
              <CardDescription>
                View and manage library assignments by organization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 mb-6">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Organizations</SelectItem>
                    <SelectItem value="active">Active Only</SelectItem>
                    <SelectItem value="inactive">Inactive Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                {filteredOrganizations.map((org) => {
                  const orgAssignments = getOrgAssignments(org.id);
                  return (
                    <Card key={org.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                              <Building2 className="h-4 w-4 text-blue-600" />
                            </div>
                            <div>
                              <h3 className="font-semibold">{org.name}</h3>
                              <div className="flex items-center space-x-2 mt-1">
                                {getStatusBadge(org.status)}
                                <Badge variant="outline">{org.isInternal ? "System Default" : "User Created"}</Badge>
                              </div>
                            </div>
                          </div>
                          <div className="text-sm text-gray-500">
                            {orgAssignments.length} librar{orgAssignments.length !== 1 ? 'ies' : 'y'} assigned
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {orgAssignments.length > 0 ? (
                          <div className="space-y-2">
                            {orgAssignments.map((assignment) => (
                              <div key={`${assignment.orgId}-${assignment.libraryId}`} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <Code className="h-4 w-4 text-purple-600" />
                                  <div>
                                    <div className="font-medium">{assignment.libraryName}</div>
                                    <div className="text-sm text-gray-500">Assigned on {assignment.assignedDate}</div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  {getStatusBadge(assignment.status)}
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleUnassignLibrary(assignment.orgId, assignment.libraryId)}
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-gray-500 text-center py-4">No libraries assigned</p>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="library-view" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Libraries and Their Assignments</CardTitle>
              <CardDescription>
                View which organizations are using each library
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Library</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Assigned Organizations</TableHead>
                    <TableHead>Assignment Count</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {jobLibraries.map((library) => {
                    const libraryAssignments = getLibraryAssignments(library.id);
                    return (
                      <TableRow key={library.id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                              <Code className="h-4 w-4 text-purple-600" />
                            </div>
                            <div>
                              <div className="font-medium">{library.name}</div>
                              <div className="text-sm text-gray-500">v{library.version}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{getTypeBadge(library.type)}</TableCell>
                        <TableCell>{getStatusBadge(library.status)}</TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {libraryAssignments.length > 0 ? (
                              libraryAssignments.map((assignment) => (
                                <div key={assignment.orgId} className="flex items-center space-x-2">
                                  <Building2 className="h-3 w-3 text-blue-600" />
                                  <span className="text-sm">{assignment.orgName}</span>
                                </div>
                              ))
                            ) : (
                              <span className="text-sm text-gray-500">Not assigned</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{libraryAssignments.length}</Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}