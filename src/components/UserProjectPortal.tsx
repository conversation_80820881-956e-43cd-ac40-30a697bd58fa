import { useState, useEffect } from "react";
import { 
  Play, 
  Download, 
  Key, 
  Bar<PERSON>hart3, 
  FolderOpen, 
  ExternalLink, 
  Users, 
  Calendar,
  Database,
  Activity,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
  Settings,
  Upload,
  Search,
  Plus
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { getStatusBadge, getJobTypeBadge, getRoleBadge } from "@/lib/badge-variants";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

// Import extracted components
import { ProjectsManagement } from "./user-portal/ProjectsManagement";
import { FilesManagement } from "./user-portal/FilesManagement";
import { FileProcessingManagement } from "./user-portal/FileProcessingManagement";
import { JobHistoryManagement } from "./user-portal/JobHistoryManagement";
import { AnalyticsManagement } from "./user-portal/AnalyticsManagement";
import { ToolsManagement } from "./user-portal/ToolsManagement";
import type { UserProject, JobExecution, AnalyticsData, FileItem, ThickClientSession, ApiToken } from "./user-portal/types";
import { 
  mockUserProjects, 
  mockJobExecutions, 
  getAnalyticsData, 
  mockFileSystem, 
  mockThickClientSessions, 
  mockApiTokens,
  getAvailableJobDefinitions,
  getMockFileSystemByProject
} from "./user-portal/data";

interface UserProjectPortalProps {
  section: string;
}

export function UserProjectPortal({ section }: UserProjectPortalProps) {
  const [selectedProject, setSelectedProject] = useState<UserProject | null>(null);
  const [isJobDialogOpen, setIsJobDialogOpen] = useState(false);
  const [isKeysDialogOpen, setIsKeysDialogOpen] = useState(false);
  const [isTokenDialogOpen, setIsTokenDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['input', 'output']));
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);

  // Use imported mock data
  const [userProjects] = useState<UserProject[]>(mockUserProjects);
  const [jobExecutions] = useState<JobExecution[]>(mockJobExecutions);
  const analyticsData: AnalyticsData = getAnalyticsData(jobExecutions);
  const [fileSystem, setFileSystem] = useState<FileItem[]>(mockFileSystem);
  const [thickClientSessions, setThickClientSessions] = useState<ThickClientSession[]>(mockThickClientSessions);
  const [apiTokens, setApiTokens] = useState<ApiToken[]>(mockApiTokens);

  // Form data state
  const [jobFormData, setJobFormData] = useState({
    jobDefinition: "ndvi-analysis",
    priority: "medium",
    selectedFiles: [] as string[],
    parameters: "",
    notifyOnComplete: true
  });

  const [tokenFormData, setTokenFormData] = useState({
    name: "",
    permissions: ["upload", "download", "status_read"],
    expiresIn: "6months"
  });

  // Simulate heartbeat updates and file progress
  useEffect(() => {
    const interval = setInterval(() => {
      setThickClientSessions(sessions => 
        sessions.map(session => {
          if (session.status === 'connected') {
            const now = new Date();
            const lastHeartbeat = new Date(session.lastHeartbeat);
            const timeDiff = now.getTime() - lastHeartbeat.getTime();
            
            if (timeDiff > 120000) {
              return { ...session, status: 'disconnected' as const };
            }
            
            if (Math.random() > 0.7) {
              return { 
                ...session, 
                lastHeartbeat: now.toISOString().slice(0, 19).replace('T', ' ')
              };
            }
          }
          return session;
        })
      );
      
      setFileSystem(files => 
        files.map(file => {
          if (file.status === 'uploading' && file.progress && file.progress < 100) {
            const newProgress = Math.min(100, file.progress + Math.random() * 10);
            return { 
              ...file, 
              progress: Math.round(newProgress),
              status: newProgress >= 100 ? 'uploaded' : 'uploading'
            };
          }
          if (file.status === 'processing' && file.progress && file.progress < 100) {
            const newProgress = Math.min(100, file.progress + Math.random() * 8);
            return { 
              ...file, 
              progress: Math.round(newProgress),
              status: newProgress >= 100 ? 'processed' : 'processing'
            };
          }
          return file;
        })
      );
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // Utility functions
  const toggleFolder = (path: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(path)) {
        newSet.delete(path);
      } else {
        newSet.add(path);
      }
      return newSet;
    });
  };

  const handleTriggerJob = () => {
    console.log("Triggering job with data:", jobFormData);
    setIsJobDialogOpen(false);
  };

  const handleTriggerJobFromFile = (file: FileItem) => {
    // Pre-select the file and open job dialog
    setJobFormData(prev => ({
      ...prev,
      selectedFiles: [file.fullPath]
    }));
    setSelectedProject(userProjects.find(p => p.id === file.projectId) || userProjects[0]);
    setIsJobDialogOpen(true);
  };

  const handleCreateToken = () => {
    const newToken: ApiToken = {
      id: `token${apiTokens.length + 1}`,
      name: tokenFormData.name,
      token: `tc_live_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`,
      createdAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
      expiresAt: new Date(Date.now() + (tokenFormData.expiresIn === '6months' ? 6 : 12) * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      permissions: tokenFormData.permissions,
      status: 'active'
    };
    
    setApiTokens(prev => [...prev, newToken]);
    setTokenFormData({ name: "", permissions: ["upload", "download", "status_read"], expiresIn: "6months" });
    setIsTokenDialogOpen(false);
  };

  const revokeToken = (tokenId: string) => {
    setApiTokens(prev => prev.map(token => 
      token.id === tokenId ? { ...token, status: 'revoked' as const } : token
    ));
  };

  const handleDownloadKeys = (project: UserProject) => {
    const keysContent = `# Azure Data Lake Storage Access Configuration
# Project: ${project.name}
# Generated: ${new Date().toISOString()}

ADLS_ENDPOINT=${project.adlsEndpoint}
CONTAINER_NAME=${project.containerName}
SUBSCRIPTION_ID=********-1234-5678-9012-********9abc
TENANT_ID=********-4321-8765-2109-9********def

# SAS Token (Valid for 30 days)
SAS_TOKEN=?sv=2022-11-02&ss=bfqt&srt=sco&sp=rwdlacupx&se=2024-07-18T00:00:00Z&st=2024-06-18T00:00:00Z&spr=https&sig=SAMPLE_SIGNATURE

# Connection String
CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=storage;AccountKey=SAMPLE_KEY;EndpointSuffix=core.windows.net

# Usage Instructions:
# 1. Set these environment variables in your application
# 2. Use Azure Storage SDK with these credentials
# 3. Upload data to: ${project.containerName}/input/
# 4. Download results from: ${project.containerName}/output/
`;

    const blob = new Blob([keysContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${project.name.toLowerCase().replace(/\s+/g, '-')}-adls-keys.env`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleDownloadClient = () => {
    const clientInfo = `# Thick Client
# Version: 2.1.4
# Release Date: 2024-06-15

Download Links:
- Windows: https://releases.versargds.com/client/v2.1.4/thick-client-windows-x64.exe
- macOS: https://releases.versargds.com/client/v2.1.4/thick-client-macos-x64.dmg
- Linux: https://releases.versargds.com/client/v2.1.4/thick-client-linux-x64.tar.gz

Installation Instructions:
1. Download the appropriate version for your operating system
2. Install using your system's standard installation process
3. Configure with your project credentials using the downloaded keys
4. Run: thick-client configure --env-file your-project-keys.env

Features:
- Batch upload of satellite imagery
- Real-time processing status monitoring
- Automated result download
- Local preprocessing capabilities
`;

    const blob = new Blob([clientInfo], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'thick-client-info.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  const openDataPortal = () => {
    window.open('https://data-portal.versargds.com', '_blank');
  };

  const renderSectionContent = () => {
    switch (section) {
      case 'dashboard':
        return (
          <AnalyticsManagement
            analyticsData={analyticsData}
            userProjects={userProjects}
            jobExecutions={jobExecutions}
            thickClientSessions={thickClientSessions}
          />
        );
      case 'projects':
        return (
          <ProjectsManagement
            userProjects={userProjects}
            onTriggerJob={(project) => {
              setSelectedProject(project);
              setIsJobDialogOpen(true);
            }}
          />
        );
      case 'files':
        return (
          <FilesManagement
            expandedFolders={expandedFolders}
            selectedFile={selectedFile}
            thickClientSessions={thickClientSessions}
            userProjects={userProjects}
            userRole={userProjects.find(p => p.id === "1")?.role === 'admin' ? 'admin' : 'reader'}
            onToggleFolder={toggleFolder}
            onSelectFile={setSelectedFile}
            onTriggerJob={handleTriggerJobFromFile}
          />
        );
      case 'file-processing':
        return (
          <FileProcessingManagement
            fileSystem={fileSystem}
            userProjects={userProjects}
            userRole={userProjects.find(p => p.id === "1")?.role === 'admin' ? 'admin' : 'reader'}
          />
        );
      case 'job-history':
        return (
          <JobHistoryManagement
            jobExecutions={jobExecutions}
            userProjects={userProjects}
            userRole={userProjects.find(p => p.id === "1")?.role === 'admin' ? 'admin' : 'reader'}
          />
        );
      case 'tools':
        return (
          <ToolsManagement
            apiTokens={apiTokens}
            isTokenDialogOpen={isTokenDialogOpen}
            onTokenDialogChange={setIsTokenDialogOpen}
            onCreateToken={handleCreateToken}
            onRevokeToken={revokeToken}
            onDownloadClient={handleDownloadClient}
            onOpenDataPortal={openDataPortal}
            tokenFormData={tokenFormData}
            onTokenFormDataChange={setTokenFormData}
          />
        );
      default:
        return (
          <AnalyticsManagement
            analyticsData={analyticsData}
            userProjects={userProjects}
            jobExecutions={jobExecutions}
            thickClientSessions={thickClientSessions}
          />
        );
    }
  };

  return (
    <div className="p-6 space-y-6">
      {renderSectionContent()}


      {/* Job Trigger Dialog */}
      <Dialog open={isJobDialogOpen} onOpenChange={setIsJobDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Trigger Processing Job</DialogTitle>
            <DialogDescription>
              Configure and start a new satellite image processing job for {selectedProject?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="jobDefinition">Job Definition</Label>
                <Select value={jobFormData.jobDefinition} onValueChange={(value) => setJobFormData({ ...jobFormData, jobDefinition: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a job definition" />
                  </SelectTrigger>
                  <SelectContent>
                    {getAvailableJobDefinitions(selectedProject?.id || "1").map(job => (
                      <SelectItem key={job.id} value={job.id}>
                        <div>
                          <div className="font-medium">{job.name}</div>
                          <div className="text-xs text-muted-foreground">{job.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select value={jobFormData.priority} onValueChange={(value) => setJobFormData({ ...jobFormData, priority: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="selectedFiles">Input Files</Label>
              <Select 
                value={jobFormData.selectedFiles.length > 0 ? jobFormData.selectedFiles[0] : ""} 
                onValueChange={(value) => {
                  if (value && !jobFormData.selectedFiles.includes(value)) {
                    setJobFormData({ ...jobFormData, selectedFiles: [...jobFormData.selectedFiles, value] });
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select files to process" />
                </SelectTrigger>
                <SelectContent>
                  {getMockFileSystemByProject(selectedProject?.id || "1")
                    .filter(file => file.type === 'file' && (file.status === 'uploaded' || file.status === 'processed'))
                    .map(file => (
                      <SelectItem key={file.id} value={file.fullPath}>
                        <div className="flex items-center space-x-2">
                          <span>{file.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {file.size}
                          </Badge>
                          <Badge variant={file.status === 'uploaded' ? 'secondary' : 'default'} className="text-xs">
                            {file.status}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))
                  }
                </SelectContent>
              </Select>
              {jobFormData.selectedFiles.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm text-muted-foreground">Selected Files:</Label>
                  <div className="flex flex-wrap gap-2">
                    {jobFormData.selectedFiles.map(filePath => {
                      const fileName = filePath.split('/').pop();
                      return (
                        <Badge key={filePath} variant="secondary" className="text-xs">
                          {fileName}
                          <button
                            onClick={() => setJobFormData({ 
                              ...jobFormData, 
                              selectedFiles: jobFormData.selectedFiles.filter(f => f !== filePath) 
                            })}
                            className="ml-1 hover:text-destructive"
                          >
                            ×
                          </button>
                        </Badge>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="parameters">Job Parameters (JSON)</Label>
              <Textarea
                id="parameters"
                value={jobFormData.parameters}
                onChange={(e) => setJobFormData({ ...jobFormData, parameters: e.target.value })}
                placeholder='{"threshold": 0.5, "bands": ["red", "nir"], "output_format": "geotiff"}'
                rows={3}
              />
              <p className="text-xs text-muted-foreground">
                Output location will be automatically determined by the system based on the job type and project configuration.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsJobDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleTriggerJob}>
              <Play className="h-4 w-4 mr-2" />
              Start Job
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}