
import { useState } from "react";
import { Download, Calendar, Filter, TrendingUp, DollarSign, Building2, BarChart3 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export function BillingReports() {
  const [selectedPeriod, setSelectedPeriod] = useState("last-6-months");
  const [selectedOrg, setSelectedOrg] = useState("all");

  const mockReportData = [
    {
      period: "Dec 2024",
      totalCosts: 4520.50,
      billableRevenue: 2840.50,
      organizations: 2,
      averageCostPerOrg: 2260.25,
      averageRevenuePerOrg: 1420.25,
      growth: "****%"
    },
    {
      period: "Nov 2024",
      totalCosts: 4190.25,
      billableRevenue: 2650.00,
      organizations: 2,
      averageCostPerOrg: 2095.13,
      averageRevenuePerOrg: 1325.00,
      growth: "+12.1%"
    },
    {
      period: "Oct 2024",
      totalCosts: 3945.75,
      billableRevenue: 2495.00,
      organizations: 2,
      averageCostPerOrg: 1972.88,
      averageRevenuePerOrg: 1247.50,
      growth: "****%"
    },
    {
      period: "Sep 2024",
      totalCosts: 3703.00,
      billableRevenue: 2353.00,
      organizations: 2,
      averageCostPerOrg: 1851.50,
      averageRevenuePerOrg: 1176.50,
      growth: "+3.2%"
    },
    {
      period: "Aug 2024",
      totalCosts: 3570.50,
      billableRevenue: 2270.00,
      organizations: 2,
      averageCostPerOrg: 1785.25,
      averageRevenuePerOrg: 1135.00,
      growth: "+7.8%"
    },
    {
      period: "Jul 2024",
      totalCosts: 3321.25,
      billableRevenue: 2121.00,
      organizations: 2,
      averageCostPerOrg: 1660.63,
      averageRevenuePerOrg: 1060.50,
      growth: "+15.3%"
    }
  ];

  const mockOrgData = [
    { 
      name: "Versar Inc", 
      costs: { q4: 5040, q3: 4680, q2: 4320, q1: 3960 },
      revenue: { q4: 0, q3: 0, q2: 0, q1: 0 },
      isInternal: true
    },
    { 
      name: "Acme Corporation", 
      costs: { q4: 8520, q3: 7890, q2: 7245, q1: 6890 },
      revenue: { q4: 8520, q3: 7890, q2: 7245, q1: 6890 },
      isInternal: false
    }
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Billing Reports</h2>
          <p className="text-gray-600">Detailed revenue analytics and billing reports</p>
        </div>
        <div className="flex gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last-3-months">Last 3 months</SelectItem>
              <SelectItem value="last-6-months">Last 6 months</SelectItem>
              <SelectItem value="last-12-months">Last 12 months</SelectItem>
              <SelectItem value="custom">Custom range</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Revenue Trend */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">6-Month Costs</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$23,251</div>
            <p className="text-xs text-blue-600 font-medium">Total costs</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">6-Month Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$14,729</div>
            <p className="text-xs text-green-600 font-medium">Billable only</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Orgs</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-green-600 font-medium">Consolidated</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+8.7%</div>
            <p className="text-xs text-green-600 font-medium">6-month trend</p>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Revenue Trend */}
      <Card>
        <CardHeader>
          <CardTitle>Revenue Trend</CardTitle>
          <CardDescription>Monthly revenue and growth metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Period</TableHead>
                <TableHead>Total Costs</TableHead>
                <TableHead>Billable Revenue</TableHead>
                <TableHead>Organizations</TableHead>
                <TableHead>Avg Cost/Org</TableHead>
                <TableHead>Growth</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockReportData.map((data, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{data.period}</TableCell>
                  <TableCell>${data.totalCosts.toLocaleString()}</TableCell>
                  <TableCell>${data.billableRevenue.toLocaleString()}</TableCell>
                  <TableCell>{data.organizations}</TableCell>
                  <TableCell>${data.averageCostPerOrg.toFixed(0)}</TableCell>
                  <TableCell>
                    <Badge variant="secondary" className="text-green-600">
                      {data.growth}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Organization Cost & Revenue Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Quarterly Costs by Organization</CardTitle>
            <CardDescription>Actual usage costs for the last 4 quarters</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Organization</TableHead>
                  <TableHead>Q4 2024</TableHead>
                  <TableHead>Q3 2024</TableHead>
                  <TableHead>Q2 2024</TableHead>
                  <TableHead>Q1 2024</TableHead>
                  <TableHead>Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockOrgData.map((org, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">
                      <div>
                        <div>{org.name}</div>
                        {org.isInternal && (
                          <div className="text-xs text-blue-600">System Default</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>${org.costs.q4.toLocaleString()}</TableCell>
                    <TableCell>${org.costs.q3.toLocaleString()}</TableCell>
                    <TableCell>${org.costs.q2.toLocaleString()}</TableCell>
                    <TableCell>${org.costs.q1.toLocaleString()}</TableCell>
                    <TableCell className="font-medium">
                      ${(org.costs.q1 + org.costs.q2 + org.costs.q3 + org.costs.q4).toLocaleString()}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quarterly Revenue by Organization</CardTitle>
            <CardDescription>Billable revenue for the last 4 quarters</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Organization</TableHead>
                  <TableHead>Q4 2024</TableHead>
                  <TableHead>Q3 2024</TableHead>
                  <TableHead>Q2 2024</TableHead>
                  <TableHead>Q1 2024</TableHead>
                  <TableHead>Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockOrgData.map((org, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">
                      <div>
                        <div>{org.name}</div>
                        {org.isInternal && (
                          <div className="text-xs text-blue-600">Not Billed</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {org.isInternal ? (
                        <div className="text-gray-500">$0</div>
                      ) : (
                        `$${org.revenue.q4.toLocaleString()}`
                      )}
                    </TableCell>
                    <TableCell>
                      {org.isInternal ? (
                        <div className="text-gray-500">$0</div>
                      ) : (
                        `$${org.revenue.q3.toLocaleString()}`
                      )}
                    </TableCell>
                    <TableCell>
                      {org.isInternal ? (
                        <div className="text-gray-500">$0</div>
                      ) : (
                        `$${org.revenue.q2.toLocaleString()}`
                      )}
                    </TableCell>
                    <TableCell>
                      {org.isInternal ? (
                        <div className="text-gray-500">$0</div>
                      ) : (
                        `$${org.revenue.q1.toLocaleString()}`
                      )}
                    </TableCell>
                    <TableCell className="font-medium">
                      {org.isInternal ? (
                        <div className="text-gray-500">$0</div>
                      ) : (
                        `$${(org.revenue.q1 + org.revenue.q2 + org.revenue.q3 + org.revenue.q4).toLocaleString()}`
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
