import { useState } from "react";
import { Plus, Code, Upload, Download, Edit, Trash2, FileText, Zap, Eye, Settings } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface JobLibrary {
  id: string;
  name: string;
  description: string;
  version: string;
  type: 'code' | 'arm-template' | 'pre-built';
  status: 'active' | 'inactive' | 'draft';
  runtime: 'dotnet' | 'nodejs' | 'python' | 'java';
  functionName: string;
  resourceGroup?: string;
  codeUrl?: string;
  armTemplateUrl?: string;
  parameters: { [key: string]: string };
  createdAt: string;
  updatedAt: string;
  assignedOrgs: number;
}

export function JobLibrariesManagement() {
  const [jobLibraries, setJobLibraries] = useState<JobLibrary[]>([
    {
      id: "1",
      name: "Data Processing Library",
      description: "Processes CSV files and transforms data",
      version: "1.2.0",
      type: "code",
      status: "active",
      runtime: "dotnet",
      functionName: "DataProcessor",
      resourceGroup: "rg-data-processing",
      codeUrl: "https://storage.blob.core.windows.net/functions/data-processor.zip",
      parameters: {
        "inputContainer": "input-data",
        "outputContainer": "processed-data",
        "batchSize": "1000"
      },
      createdAt: "2024-01-15",
      updatedAt: "2024-01-20",
      assignedOrgs: 3
    },
    {
      id: "2",
      name: "Reporting Engine",
      description: "Generates reports from processed data",
      version: "1.0.0",
      type: "arm-template",
      status: "active",
      runtime: "nodejs",
      functionName: "ReportGenerator",
      resourceGroup: "rg-reporting",
      armTemplateUrl: "https://storage.blob.core.windows.net/templates/reporting-engine.json",
      parameters: {
        "reportType": "monthly",
        "outputFormat": "pdf",
        "emailNotification": "true"
      },
      createdAt: "2024-02-01",
      updatedAt: "2024-02-01",
      assignedOrgs: 2
    },
    {
      id: "3",
      name: "API Integration Service",
      description: "Integrates with external APIs",
      version: "0.9.0",
      type: "pre-built",
      status: "draft",
      runtime: "python",
      functionName: "ApiIntegrator",
      parameters: {
        "apiEndpoint": "https://api.example.com",
        "timeout": "30",
        "retryCount": "3"
      },
      createdAt: "2024-03-01",
      updatedAt: "2024-03-05",
      assignedOrgs: 0
    }
  ]);

  const [selectedLibrary, setSelectedLibrary] = useState<JobLibrary | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const [formData, setFormData] = useState<Partial<JobLibrary>>({
    name: "",
    description: "",
    version: "1.0.0",
    type: "code",
    status: "draft",
    runtime: "dotnet",
    functionName: "",
    resourceGroup: "",
    parameters: {}
  });

  const handleCreate = () => {
    const newLibrary: JobLibrary = {
      id: Date.now().toString(),
      name: formData.name || "",
      description: formData.description || "",
      version: formData.version || "1.0.0",
      type: formData.type as JobLibrary['type'],
      status: formData.status as JobLibrary['status'],
      runtime: formData.runtime as JobLibrary['runtime'],
      functionName: formData.functionName || "",
      resourceGroup: formData.resourceGroup,
      codeUrl: uploadedFile ? `https://storage.blob.core.windows.net/functions/${uploadedFile.name}` : undefined,
      parameters: formData.parameters || {},
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0],
      assignedOrgs: 0
    };

    setJobLibraries([...jobLibraries, newLibrary]);
    resetForm();
    setIsCreateDialogOpen(false);
  };

  const handleEdit = () => {
    if (!selectedLibrary) return;

    const updatedLibs = jobLibraries.map(lib =>
      lib.id === selectedLibrary.id
        ? {
            ...lib,
            ...formData,
            updatedAt: new Date().toISOString().split('T')[0],
            codeUrl: uploadedFile ? `https://storage.blob.core.windows.net/functions/${uploadedFile.name}` : lib.codeUrl
          }
        : lib
    );

    setJobLibraries(updatedLibs);
    setIsEditDialogOpen(false);
    setSelectedLibrary(null);
    resetForm();
  };

  const handleDelete = (libId: string) => {
    setJobLibraries(jobLibraries.filter(lib => lib.id !== libId));
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      version: "1.0.0",
      type: "code",
      status: "draft",
      runtime: "dotnet",
      functionName: "",
      resourceGroup: "",
      parameters: {}
    });
    setUploadedFile(null);
  };

  const openEditDialog = (library: JobLibrary) => {
    setSelectedLibrary(library);
    setFormData(library);
    setIsEditDialogOpen(true);
  };

  const openViewDialog = (library: JobLibrary) => {
    setSelectedLibrary(library);
    setIsViewDialogOpen(true);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setUploadedFile(event.target.files[0]);
    }
  };

  const getStatusBadge = (status: JobLibrary['status']) => {
    const variants: Record<JobLibrary['status'], 'default' | 'destructive' | 'secondary'> = {
      active: 'default',
      inactive: 'secondary',
      draft: 'destructive'
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const getTypeBadge = (type: JobLibrary['type']) => {
    const colors: Record<JobLibrary['type'], string> = {
      code: 'bg-blue-100 text-blue-800',
      'arm-template': 'bg-green-100 text-green-800',
      'pre-built': 'bg-purple-100 text-purple-800'
    };
    return <Badge className={colors[type]}>{type}</Badge>;
  };

  const getRuntimeBadge = (runtime: JobLibrary['runtime']) => {
    const colors: Record<JobLibrary['runtime'], string> = {
      dotnet: 'bg-blue-100 text-blue-800',
      nodejs: 'bg-green-100 text-green-800',
      python: 'bg-yellow-100 text-yellow-800',
      java: 'bg-red-100 text-red-800'
    };
    return <Badge className={colors[runtime]}>{runtime}</Badge>;
  };

  return (
    <div className="p-6 space-y-6">
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Job Library</DialogTitle>
              <DialogDescription>
                Create a new pipeline function library with code upload or ARM template
              </DialogDescription>
            </DialogHeader>
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="deployment">Deployment</TabsTrigger>
                <TabsTrigger value="parameters">Parameters</TabsTrigger>
              </TabsList>
              
              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Library Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter library name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="version">Version</Label>
                    <Input
                      id="version"
                      value={formData.version}
                      onChange={(e) => setFormData({ ...formData, version: e.target.value })}
                      placeholder="1.0.0"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Enter library description"
                  />
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type">Type</Label>
                    <Select value={formData.type} onValueChange={(value) => setFormData({ ...formData, type: value as JobLibrary['type'] })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="code">Code Upload</SelectItem>
                        <SelectItem value="arm-template">ARM Template</SelectItem>
                        <SelectItem value="pre-built">Pre-built</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="runtime">Runtime</Label>
                    <Select value={formData.runtime} onValueChange={(value) => setFormData({ ...formData, runtime: value as JobLibrary['runtime'] })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="dotnet">.NET</SelectItem>
                        <SelectItem value="nodejs">Node.js</SelectItem>
                        <SelectItem value="python">Python</SelectItem>
                        <SelectItem value="java">Java</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value as JobLibrary['status'] })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="deployment" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="functionName">Function Name</Label>
                    <Input
                      id="functionName"
                      value={formData.functionName}
                      onChange={(e) => setFormData({ ...formData, functionName: e.target.value })}
                      placeholder="Enter pipeline function name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="resourceGroup">Resource Group</Label>
                    <Input
                      id="resourceGroup"
                      value={formData.resourceGroup}
                      onChange={(e) => setFormData({ ...formData, resourceGroup: e.target.value })}
                      placeholder="Enter resource group name"
                    />
                  </div>
                </div>
                
                {formData.type === 'code' && (
                  <div className="space-y-2">
                    <Label htmlFor="codeUpload">Upload Code (ZIP file)</Label>
                    <Input
                      id="codeUpload"
                      type="file"
                      accept=".zip"
                      onChange={handleFileUpload}
                      className="cursor-pointer"
                    />
                    {uploadedFile && (
                      <p className="text-sm text-green-600">
                        File selected: {uploadedFile.name}
                      </p>
                    )}
                  </div>
                )}
                
                {formData.type === 'arm-template' && (
                  <div className="space-y-2">
                    <Label htmlFor="armTemplate">Upload ARM Template (JSON file)</Label>
                    <Input
                      id="armTemplate"
                      type="file"
                      accept=".json"
                      onChange={handleFileUpload}
                      className="cursor-pointer"
                    />
                    {uploadedFile && (
                      <p className="text-sm text-green-600">
                        Template selected: {uploadedFile.name}
                      </p>
                    )}
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="parameters" className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Function Parameters</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const key = prompt('Parameter name:');
                        const value = prompt('Parameter value:');
                        if (key && value) {
                          setFormData({
                            ...formData,
                            parameters: { ...formData.parameters, [key]: value }
                          });
                        }
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Parameter
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {Object.entries(formData.parameters || {}).map(([key, value]) => (
                      <div key={key} className="flex items-center space-x-2">
                        <Input value={key} readOnly className="flex-1" />
                        <Input 
                          value={value} 
                          onChange={(e) => setFormData({
                            ...formData,
                            parameters: { ...formData.parameters, [key]: e.target.value }
                          })}
                          className="flex-1" 
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const newParams = { ...formData.parameters };
                            delete newParams[key];
                            setFormData({ ...formData, parameters: newParams });
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreate}>Create Library</Button>
            </DialogFooter>
          </DialogContent>
        
        <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Job Libraries</CardTitle>
              <CardDescription>
                {jobLibraries.length} job librar{jobLibraries.length !== 1 ? 'ies' : 'y'} total
              </CardDescription>
            </div>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Create Job Library
              </Button>
            </DialogTrigger>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Library</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Runtime</TableHead>
                <TableHead>Version</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Assigned Orgs</TableHead>
                <TableHead>Updated</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {jobLibraries.map((library) => (
                <TableRow key={library.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Code className="h-4 w-4 text-purple-600" />
                      </div>
                      <div>
                        <div className="font-medium">{library.name}</div>
                        <div className="text-sm text-gray-500">{library.description}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getTypeBadge(library.type)}</TableCell>
                  <TableCell>{getRuntimeBadge(library.runtime)}</TableCell>
                  <TableCell>{library.version}</TableCell>
                  <TableCell>{getStatusBadge(library.status)}</TableCell>
                  <TableCell>{library.assignedOrgs}</TableCell>
                  <TableCell>{library.updatedAt}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openViewDialog(library)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditDialog(library)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Job Library</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete "{library.name}"? This will affect {library.assignedOrgs} organization(s).
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDelete(library.id)}>
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Job Library Details</DialogTitle>
            <DialogDescription>
              Complete information about the job library
            </DialogDescription>
          </DialogHeader>
          {selectedLibrary && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-sm text-gray-700 mb-3">Basic Information</h3>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium">Name:</span>
                      <p className="text-sm text-gray-600">{selectedLibrary.name}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Description:</span>
                      <p className="text-sm text-gray-600">{selectedLibrary.description}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Version:</span>
                      <p className="text-sm text-gray-600">{selectedLibrary.version}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">Type:</span>
                      {getTypeBadge(selectedLibrary.type)}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">Runtime:</span>
                      {getRuntimeBadge(selectedLibrary.runtime)}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">Status:</span>
                      {getStatusBadge(selectedLibrary.status)}
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm text-gray-700 mb-3">Deployment Details</h3>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium">Function Name:</span>
                      <p className="text-sm text-gray-600">{selectedLibrary.functionName}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Resource Group:</span>
                      <p className="text-sm text-gray-600">{selectedLibrary.resourceGroup || 'Not specified'}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Assigned Organizations:</span>
                      <p className="text-sm text-gray-600">{selectedLibrary.assignedOrgs}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Created:</span>
                      <p className="text-sm text-gray-600">{selectedLibrary.createdAt}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Last Updated:</span>
                      <p className="text-sm text-gray-600">{selectedLibrary.updatedAt}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold text-sm text-gray-700 mb-3">Parameters</h3>
                <div className="space-y-2">
                  {Object.entries(selectedLibrary.parameters).length > 0 ? (
                    Object.entries(selectedLibrary.parameters).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm font-medium">{key}:</span>
                        <span className="text-sm text-gray-600">{value}</span>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500">No parameters defined</p>
                  )}
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}