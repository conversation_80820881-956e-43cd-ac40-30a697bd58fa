
import { useState } from "react";
import { Download, Calendar, DollarSign, FileText, CheckCircle, Clock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export function OrgBillingHistory() {
  const [selectedYear, setSelectedYear] = useState("2024");

  const mockBillingHistory = [
    { 
      period: "Dec 2024", 
      amount: 280.50, 
      status: "Current", 
      dueDate: "Jan 15, 2025",
      invoiceNumber: "INV-2024-12-001",
      paymentMethod: "Credit Card (**** 4242)"
    },
    { 
      period: "Nov 2024", 
      amount: 265.25, 
      status: "Paid", 
      dueDate: "Dec 15, 2024",
      invoiceNumber: "INV-2024-11-001",
      paymentMethod: "Credit Card (**** 4242)",
      paidDate: "Dec 10, 2024"
    },
    { 
      period: "Oct 2024", 
      amount: 298.75, 
      status: "Paid", 
      dueDate: "Nov 15, 2024",
      invoiceNumber: "INV-2024-10-001",
      paymentMethod: "Credit Card (**** 4242)",
      paidDate: "Nov 8, 2024"
    },
    { 
      period: "Sep 2024", 
      amount: 312.00, 
      status: "Paid", 
      dueDate: "Oct 15, 2024",
      invoiceNumber: "INV-2024-09-001",
      paymentMethod: "Credit Card (**** 4242)",
      paidDate: "Oct 12, 2024"
    },
    { 
      period: "Aug 2024", 
      amount: 285.50, 
      status: "Paid", 
      dueDate: "Sep 15, 2024",
      invoiceNumber: "INV-2024-08-001",
      paymentMethod: "Credit Card (**** 4242)",
      paidDate: "Sep 9, 2024"
    },
    { 
      period: "Jul 2024", 
      amount: 267.25, 
      status: "Paid", 
      dueDate: "Aug 15, 2024",
      invoiceNumber: "INV-2024-07-001",
      paymentMethod: "Credit Card (**** 4242)",
      paidDate: "Aug 11, 2024"
    }
  ];

  const totalPaid = mockBillingHistory
    .filter(bill => bill.status === 'Paid')
    .reduce((sum, bill) => sum + bill.amount, 0);

  const averageMonthly = totalPaid / mockBillingHistory.filter(bill => bill.status === 'Paid').length;

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Billing History</h2>
          <p className="text-gray-600">View your organization's billing history and download invoices</p>
        </div>
        <div className="flex gap-2">
          <Select value={selectedYear} onValueChange={setSelectedYear}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Select year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2024">2024</SelectItem>
              <SelectItem value="2023">2023</SelectItem>
              <SelectItem value="2022">2022</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export All
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Year Total</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${(totalPaid + 280.50).toFixed(2)}</div>
            <p className="text-xs text-blue-600 font-medium">2024 YTD</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Monthly</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${averageMonthly.toFixed(0)}</div>
            <p className="text-xs text-green-600 font-medium">Per month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid Invoices</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockBillingHistory.filter(b => b.status === 'Paid').length}</div>
            <p className="text-xs text-green-600 font-medium">This year</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outstanding</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockBillingHistory.filter(b => b.status === 'Current')[0]?.amount.toFixed(2) || '0.00'}</div>
            <p className="text-xs text-orange-600 font-medium">Current bill</p>
          </CardContent>
        </Card>
      </div>

      {/* Billing History Table */}
      <Card>
        <CardHeader>
          <CardTitle>Invoice History</CardTitle>
          <CardDescription>Complete billing history for {selectedYear}</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Invoice</TableHead>
                <TableHead>Billing Period</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Payment Method</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockBillingHistory.map((bill, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{bill.invoiceNumber}</TableCell>
                  <TableCell>{bill.period}</TableCell>
                  <TableCell className="font-medium">${bill.amount.toFixed(2)}</TableCell>
                  <TableCell>{bill.dueDate}</TableCell>
                  <TableCell className="text-sm text-gray-600">{bill.paymentMethod}</TableCell>
                  <TableCell>
                    <div className="flex flex-col gap-1">
                      <Badge variant={
                        bill.status === 'Paid' ? 'default' : 
                        bill.status === 'Current' ? 'secondary' : 'destructive'
                      }>
                        {bill.status}
                      </Badge>
                      {bill.paidDate && (
                        <span className="text-xs text-green-600">Paid: {bill.paidDate}</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm">
                        <Download className="h-4 w-4 mr-1" />
                        PDF
                      </Button>
                      {bill.status === 'Current' && (
                        <Button variant="outline" size="sm">
                          Pay Now
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
