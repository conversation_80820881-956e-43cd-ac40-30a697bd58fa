import { useState } from "react";
import { Plus, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export function AssignmentsManagement() {
  return (
    <div className="space-y-6 p-6">
      <div>
        <h2 className="text-2xl font-bold">Assignment Management</h2>
        <p className="text-gray-600">Manage tenant-to-project and user-to-tenant assignments</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Tenant-Project Assignments</CardTitle>
            <CardDescription>Assign tenants to projects</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button variant="outline" className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                New Tenant-Project Assignment
              </Button>
              <div className="space-y-3">
                {[
                  { tenant: "Dedicated Tenant A", project: "Agricultural Monitoring" },
                  { tenant: "Dedicated Tenant A", project: "Disaster Response" },
                  { tenant: "Dedicated Tenant B", project: "Urban Planning" }
                ].map((assignment, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">{assignment.tenant}</p>
                      <p className="text-xs text-gray-600">→ {assignment.project}</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>User-Tenant Assignments</CardTitle>
            <CardDescription>Assign users to tenants with roles</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button variant="outline" className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                New User-Tenant Assignment
              </Button>
              <div className="space-y-3">
                {[
                  { user: "John Smith", tenant: "Dedicated Tenant A", role: "Admin" },
                  { user: "Sarah Johnson", tenant: "Dedicated Tenant A", role: "Manager" },
                  { user: "Mike Wilson", tenant: "Dedicated Tenant B", role: "Developer" }
                ].map((assignment, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">{assignment.user}</p>
                      <p className="text-xs text-gray-600">
                        {assignment.tenant} • {assignment.role}
                      </p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}