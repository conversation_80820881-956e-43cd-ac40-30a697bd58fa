import { useState } from "react";
import { Plus, Search, MoreHorizontal, Edit, Trash2, Users, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface OrgAdminManagementProps {
  section: string;
}

export function OrgAdminManagement({ section }: OrgAdminManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [formData, setFormData] = useState<any>({});

  // Mock data
  const mockTenants = [
    {
      id: 1,
      name: "Dedicated Tenant A",
      description: "Main production tenant for client operations",
      projects: ["Agricultural Monitoring", "Disaster Response", "Urban Planning"],
      users: 12,
      status: "Active",
      created: "2024-01-15"
    },
    {
      id: 2,
      name: "Dedicated Tenant B",
      description: "Development and testing tenant",
      projects: ["Beta Features", "Testing Suite"],
      users: 8,
      status: "Active",
      created: "2024-02-01"
    },
    {
      id: 3,
      name: "Dedicated Tenant B",
      description: "Pre-production staging environment",
      projects: ["Quality Assurance"],
      users: 5,
      status: "Inactive",
      created: "2024-02-10"
    }
  ];

  const mockProjects = [
    {
      id: 1,
      name: "Agricultural Monitoring",
      description: "Real-time analytics and reporting dashboard",
      tenants: ["Dedicated Tenant A", "Dedicated Tenant B"],
      users: 15,
      status: "Active",
      created: "2024-01-20"
    },
    {
      id: 2,
      name: "Disaster Response Pipeline",
      description: "Automated data processing and transformation",
      tenants: ["Dedicated Tenant A"],
      users: 8,
      status: "Active",
      created: "2024-01-25"
    },
    {
      id: 3,
      name: "Beta Features Testing",
      description: "Testing environment for new features",
      tenants: ["Dedicated Tenant B"],
      users: 6,
      status: "In Development",
      created: "2024-02-05"
    }
  ];

  const mockUsers = [
    {
      id: 1,
      name: "John Smith",
      email: "<EMAIL>",
      role: "Tenant Admin",
      tenants: ["Dedicated Tenant A", "Dedicated Tenant B"],
      projects: ["Agricultural Monitoring", "Disaster Response Pipeline"],
      status: "Active",
      lastLogin: "2024-06-15"
    },
    {
      id: 2,
      name: "Sarah Johnson",
      email: "<EMAIL>",
      role: "Project Manager",
      tenants: ["Dedicated Tenant A"],
      projects: ["Agricultural Monitoring"],
      status: "Active",
      lastLogin: "2024-06-14"
    },
    {
      id: 3,
      name: "Mike Wilson",
      email: "<EMAIL>",
      role: "Developer",
      tenants: ["Dedicated Tenant B"],
      projects: ["Beta Features Testing"],
      status: "Inactive",
      lastLogin: "2024-06-10"
    }
  ];

  const handleCreate = () => {
    setFormData({});
    setIsCreateDialogOpen(true);
  };

  const handleEdit = (item: any) => {
    setSelectedItem(item);
    setFormData(item);
    setIsEditDialogOpen(true);
  };

  const handleDelete = (item: any) => {
    setSelectedItem(item);
    setIsDeleteDialogOpen(true);
  };

  const handleSubmit = () => {
    console.log('Submitting:', formData);
    setIsCreateDialogOpen(false);
    setIsEditDialogOpen(false);
    setFormData({});
    setSelectedItem(null);
  };

  const handleDeleteConfirm = () => {
    console.log('Deleting:', selectedItem);
    setIsDeleteDialogOpen(false);
    setSelectedItem(null);
  };

  const renderCreateEditDialog = (isEdit: boolean) => {
    const title = isEdit ? `Edit ${section.slice(0, -1)}` : `Create ${section.slice(0, -1)}`;
    
    return (
      <Dialog open={isEdit ? isEditDialogOpen : isCreateDialogOpen} onOpenChange={isEdit ? setIsEditDialogOpen : setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            <DialogDescription>
              {isEdit ? `Update the ${section.slice(0, -1)} details below.` : `Add a new ${section.slice(0, -1)} to your organization.`}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {section === 'tenants' && (
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">Name</Label>
                  <Input
                    id="name"
                    value={formData.name || ''}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description || ''}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="status" className="text-right">Status</Label>
                  <Select value={formData.status || ''} onValueChange={(value) => setFormData({...formData, status: value})}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
            {section === 'projects' && (
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">Name</Label>
                  <Input
                    id="name"
                    value={formData.name || ''}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description || ''}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="status" className="text-right">Status</Label>
                  <Select value={formData.status || ''} onValueChange={(value) => setFormData({...formData, status: value})}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="In Development">In Development</SelectItem>
                      <SelectItem value="Inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
            {section === 'users' && (
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">Name</Label>
                  <Input
                    id="name"
                    value={formData.name || ''}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email || ''}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="role" className="text-right">Role</Label>
                  <Select value={formData.role || ''} onValueChange={(value) => setFormData({...formData, role: value})}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Tenant Admin">Tenant Admin</SelectItem>
                      <SelectItem value="Project Manager">Project Manager</SelectItem>
                      <SelectItem value="Developer">Developer</SelectItem>
                      <SelectItem value="Viewer">Viewer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="status" className="text-right">Status</Label>
                  <Select value={formData.status || ''} onValueChange={(value) => setFormData({...formData, status: value})}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
          </div>
          <DialogFooter>
            <Button type="submit" onClick={handleSubmit}>
              {isEdit ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  const renderDeleteDialog = () => (
    <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Confirm Deletion</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete "{selectedItem?.name}"? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={handleDeleteConfirm}>
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  const renderTenants = () => (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Tenant Management</h2>
          <p className="text-gray-600">Manage tenants and their configurations</p>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="h-4 w-4 mr-2" />
          Add Tenant
        </Button>
      </div>

      <div className="flex items-center space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search tenants..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <div className="grid gap-4">
        {mockTenants.map((tenant) => (
          <Card key={tenant.id}>
            <CardHeader className="flex flex-row items-start justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  {tenant.name}
                  <Badge variant={tenant.status === 'Active' ? 'default' : 'secondary'}>
                    {tenant.status}
                  </Badge>
                </CardTitle>
                <CardDescription>{tenant.description}</CardDescription>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleEdit(tenant)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(tenant)}>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium">Projects:</span>
                  <div className="mt-1">
                    {tenant.projects.map((project, index) => (
                      <Badge key={index} variant="outline" className="mr-1 mb-1">
                        {project}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <span className="font-medium">Users:</span>
                  <p className="mt-1 flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    {tenant.users}
                  </p>
                </div>
                <div>
                  <span className="font-medium">Created:</span>
                  <p className="mt-1">{tenant.created}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {renderCreateEditDialog(false)}
      {renderCreateEditDialog(true)}
      {renderDeleteDialog()}
    </div>
  );

  const renderProjects = () => (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Project Management</h2>
          <p className="text-gray-600">Manage projects and tenant assignments</p>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="h-4 w-4 mr-2" />
          Add Project
        </Button>
      </div>

      <div className="flex items-center space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Project Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Assigned Tenants</TableHead>
            <TableHead>Users</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {mockProjects.map((project) => (
            <TableRow key={project.id}>
              <TableCell className="font-medium">{project.name}</TableCell>
              <TableCell>{project.description}</TableCell>
              <TableCell>
                <div className="space-y-1">
                  {project.tenants.map((tenant, index) => (
                    <Badge key={index} variant="outline" className="mr-1">
                      {tenant}
                    </Badge>
                  ))}
                </div>
              </TableCell>
              <TableCell>{project.users}</TableCell>
              <TableCell>
                <Badge variant={project.status === 'Active' ? 'default' : 'secondary'}>
                  {project.status}
                </Badge>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleEdit(project)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Users className="mr-2 h-4 w-4" />
                      Manage Assignments
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(project)}>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {renderCreateEditDialog(false)}
      {renderCreateEditDialog(true)}
      {renderDeleteDialog()}
    </div>
  );

  const renderUsers = () => (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">User Management</h2>
          <p className="text-gray-600">Manage users and their tenant/project assignments</p>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="h-4 w-4 mr-2" />
          Add User
        </Button>
      </div>

      <div className="flex items-center space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Tenants</TableHead>
            <TableHead>Projects</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {mockUsers.map((user) => (
            <TableRow key={user.id}>
              <TableCell className="font-medium">{user.name}</TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>
                <Badge variant="outline">{user.role}</Badge>
              </TableCell>
              <TableCell>
                <div className="space-y-1">
                  {user.tenants.map((tenant, index) => (
                    <Badge key={index} variant="secondary" className="mr-1 text-xs">
                      {tenant}
                    </Badge>
                  ))}
                </div>
              </TableCell>
              <TableCell>
                <div className="space-y-1">
                  {user.projects.map((project, index) => (
                    <Badge key={index} variant="outline" className="mr-1 text-xs">
                      {project}
                    </Badge>
                  ))}
                </div>
              </TableCell>
              <TableCell>
                <Badge variant={user.status === 'Active' ? 'default' : 'secondary'}>
                  {user.status}
                </Badge>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleEdit(user)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Users className="mr-2 h-4 w-4" />
                      Manage Assignments
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(user)}>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {renderCreateEditDialog(false)}
      {renderCreateEditDialog(true)}
      {renderDeleteDialog()}
    </div>
  );

  const renderAssignments = () => (
    <div className="space-y-6 p-6">
      <div>
        <h2 className="text-2xl font-bold">Assignment Management</h2>
        <p className="text-gray-600">Manage tenant-to-project and user-to-tenant assignments</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Tenant-Project Assignments</CardTitle>
            <CardDescription>Assign tenants to projects</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button variant="outline" className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                New Tenant-Project Assignment
              </Button>
              <div className="space-y-3">
                {[
                  { tenant: "Dedicated Tenant A", project: "Agricultural Monitoring" },
                  { tenant: "Dedicated Tenant A", project: "Disaster Response Pipeline" },
                  { tenant: "Dedicated Tenant B", project: "Beta Features Testing" }
                ].map((assignment, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">{assignment.tenant}</p>
                      <p className="text-xs text-gray-600">→ {assignment.project}</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>User-Tenant Assignments</CardTitle>
            <CardDescription>Assign users to tenants with roles</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button variant="outline" className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                New User-Tenant Assignment
              </Button>
              <div className="space-y-3">
                {[
                  { user: "John Smith", tenant: "Dedicated Tenant A", role: "Admin" },
                  { user: "Sarah Johnson", tenant: "Dedicated Tenant A", role: "Manager" },
                  { user: "Mike Wilson", tenant: "Dedicated Tenant B", role: "Developer" }
                ].map((assignment, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">{assignment.user}</p>
                      <p className="text-xs text-gray-600">
                        {assignment.tenant} • {assignment.role}
                      </p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  switch (section) {
    case 'tenants':
      return renderTenants();
    case 'projects':
      return renderProjects();
    case 'users':
      return renderUsers();
    case 'assignments':
      return renderAssignments();
    default:
      return renderTenants();
  }
}
