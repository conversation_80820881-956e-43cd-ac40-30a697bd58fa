import { useState } from "react";
import { Plus, Play, Pause, Edit, Trash2, <PERSON>, ArrowRight, Clock, Zap, Settings, AlertCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface AssignedJobLibrary {
  id: string;
  name: string;
  description: string;
  version: string;
  functionName: string;
  type: 'code' | 'arm-template' | 'pre-built';
  runtime: 'dotnet' | 'nodejs' | 'python' | 'java';
}

interface JobStep {
  id: string;
  libraryId: string;
  libraryName: string;
  order: number;
  parameters: { [key: string]: string };
  condition?: string;
  onSuccess?: 'continue' | 'stop' | 'jump';
  onFailure?: 'continue' | 'stop' | 'retry' | 'jump';
  targetStepId?: string;
  retryCount?: number;
}

interface OrgJobDefinition {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'draft';
  triggerType: 'manual' | 'scheduled' | 'event';
  schedule?: string;
  eventTrigger?: string;
  steps: JobStep[];
  createdAt: string;
  updatedAt: string;
  lastRun?: string;
  createdBy: string;
  assignedProjects: number;
}

export function OrgJobDefinitions() {
  // These are libraries assigned to this organization by root admin
  const [assignedLibraries] = useState<AssignedJobLibrary[]>([
    {
      id: "1",
      name: "Data Processing Library",
      description: "Processes CSV files and transforms data",
      version: "1.2.0",
      functionName: "DataProcessor",
      type: "code",
      runtime: "dotnet"
    },
    {
      id: "2",
      name: "Reporting Engine",
      description: "Generates reports from processed data",
      version: "1.0.0",
      functionName: "ReportGenerator",
      type: "arm-template",
      runtime: "nodejs"
    },
    {
      id: "3",
      name: "Email Notification Service",
      description: "Sends email notifications",
      version: "1.1.0",
      functionName: "EmailNotifier",
      type: "pre-built",
      runtime: "python"
    }
  ]);

  const [jobDefinitions, setJobDefinitions] = useState<OrgJobDefinition[]>([
    {
      id: "1",
      name: "Daily Data Processing",
      description: "Process daily data files and generate reports",
      status: "active",
      triggerType: "scheduled",
      schedule: "0 2 * * *",
      steps: [
        {
          id: "step1",
          libraryId: "1",
          libraryName: "Data Processing Library",
          order: 1,
          parameters: { inputPath: "/data/daily", outputPath: "/data/processed" },
          onSuccess: "continue",
          onFailure: "stop"
        },
        {
          id: "step2",
          libraryId: "2",
          libraryName: "Reporting Engine",
          order: 2,
          parameters: { reportType: "daily", format: "pdf" },
          onSuccess: "continue",
          onFailure: "retry",
          retryCount: 3
        },
        {
          id: "step3",
          libraryId: "3",
          libraryName: "Email Notification Service",
          order: 3,
          parameters: { recipients: "<EMAIL>", subject: "Daily Report Generated" },
          onSuccess: "continue",
          onFailure: "continue"
        }
      ],
      createdAt: "2024-01-15",
      updatedAt: "2024-01-20",
      lastRun: "2024-06-18",
      createdBy: "John Smith",
      assignedProjects: 2
    },
    {
      id: "2",
      name: "Manual Data Export",
      description: "On-demand data export for analysis",
      status: "active",
      triggerType: "manual",
      steps: [
        {
          id: "step1",
          libraryId: "1",
          libraryName: "Data Processing Library",
          order: 1,
          parameters: { inputPath: "/data/current", outputPath: "/exports", format: "csv" },
          onSuccess: "continue",
          onFailure: "stop"
        }
      ],
      createdAt: "2024-02-01",
      updatedAt: "2024-02-01",
      lastRun: "2024-06-17",
      createdBy: "Sarah Johnson",
      assignedProjects: 1
    },
    {
      id: "3",
      name: "Error Alert System",
      description: "Monitor for errors and send alerts",
      status: "draft",
      triggerType: "event",
      eventTrigger: "error-detected",
      steps: [
        {
          id: "step1",
          libraryId: "3",
          libraryName: "Email Notification Service",
          order: 1,
          parameters: { recipients: "<EMAIL>", subject: "Error Alert", priority: "high" },
          onSuccess: "continue",
          onFailure: "retry",
          retryCount: 2
        }
      ],
      createdAt: "2024-03-01",
      updatedAt: "2024-03-05",
      createdBy: "Mike Davis",
      assignedProjects: 0
    }
  ]);

  const [selectedDefinition, setSelectedDefinition] = useState<OrgJobDefinition | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);

  const [formData, setFormData] = useState<Partial<OrgJobDefinition>>({
    name: "",
    description: "",
    status: "draft",
    triggerType: "manual",
    schedule: "",
    eventTrigger: "",
    steps: []
  });

  const [currentStep, setCurrentStep] = useState<Partial<JobStep>>({
    libraryId: "",
    parameters: {},
    onSuccess: "continue",
    onFailure: "stop"
  });

  const handleCreate = () => {
    const newDefinition: OrgJobDefinition = {
      id: Date.now().toString(),
      name: formData.name || "",
      description: formData.description || "",
      status: formData.status as OrgJobDefinition['status'],
      triggerType: formData.triggerType as OrgJobDefinition['triggerType'],
      schedule: formData.schedule,
      eventTrigger: formData.eventTrigger,
      steps: formData.steps || [],
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0],
      createdBy: "Current User",
      assignedProjects: 0
    };

    setJobDefinitions([...jobDefinitions, newDefinition]);
    resetForm();
    setIsCreateDialogOpen(false);
  };

  const handleEdit = () => {
    if (!selectedDefinition) return;

    const updatedDefs = jobDefinitions.map(def =>
      def.id === selectedDefinition.id
        ? {
            ...def,
            ...formData,
            updatedAt: new Date().toISOString().split('T')[0]
          }
        : def
    );

    setJobDefinitions(updatedDefs);
    setIsEditDialogOpen(false);
    setSelectedDefinition(null);
    resetForm();
  };

  const handleDelete = (defId: string) => {
    setJobDefinitions(jobDefinitions.filter(def => def.id !== defId));
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      status: "draft",
      triggerType: "manual",
      schedule: "",
      eventTrigger: "",
      steps: []
    });
    setCurrentStep({
      libraryId: "",
      parameters: {},
      onSuccess: "continue",
      onFailure: "stop"
    });
  };

  const addStep = () => {
    if (!currentStep.libraryId) return;

    const library = assignedLibraries.find(l => l.id === currentStep.libraryId);
    if (!library) return;

    const newStep: JobStep = {
      id: `step_${Date.now()}`,
      libraryId: currentStep.libraryId,
      libraryName: library.name,
      order: (formData.steps?.length || 0) + 1,
      parameters: currentStep.parameters || {},
      onSuccess: currentStep.onSuccess || "continue",
      onFailure: currentStep.onFailure || "stop",
      retryCount: currentStep.retryCount
    };

    setFormData({
      ...formData,
      steps: [...(formData.steps || []), newStep]
    });

    setCurrentStep({
      libraryId: "",
      parameters: {},
      onSuccess: "continue",
      onFailure: "stop"
    });
  };

  const removeStep = (stepId: string) => {
    setFormData({
      ...formData,
      steps: formData.steps?.filter(step => step.id !== stepId) || []
    });
  };

  const moveStep = (stepId: string, direction: 'up' | 'down') => {
    const steps = [...(formData.steps || [])];
    const index = steps.findIndex(s => s.id === stepId);
    
    if (direction === 'up' && index > 0) {
      [steps[index], steps[index - 1]] = [steps[index - 1], steps[index]];
    } else if (direction === 'down' && index < steps.length - 1) {
      [steps[index], steps[index + 1]] = [steps[index + 1], steps[index]];
    }

    steps.forEach((step, i) => step.order = i + 1);
    setFormData({ ...formData, steps });
  };

  const openEditDialog = (definition: OrgJobDefinition) => {
    setSelectedDefinition(definition);
    setFormData(definition);
    setIsEditDialogOpen(true);
  };

  const openViewDialog = (definition: OrgJobDefinition) => {
    setSelectedDefinition(definition);
    setIsViewDialogOpen(true);
  };

  const getStatusBadge = (status: OrgJobDefinition['status']) => {
    const variants: Record<OrgJobDefinition['status'], 'default' | 'destructive' | 'secondary'> = {
      active: 'default',
      inactive: 'secondary',
      draft: 'destructive'
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const getTriggerBadge = (triggerType: OrgJobDefinition['triggerType']) => {
    const colors: Record<OrgJobDefinition['triggerType'], string> = {
      manual: 'bg-gray-100 text-gray-800',
      scheduled: 'bg-blue-100 text-blue-800',
      event: 'bg-green-100 text-green-800'
    };
    return <Badge className={colors[triggerType]}>{triggerType}</Badge>;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Organization Job Definitions</h1>
          <p className="text-gray-600 mt-2">Create workflows using assigned pipeline libraries</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Job Definition
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Job Definition</DialogTitle>
              <DialogDescription>
                Define a workflow using your organization's assigned libraries
              </DialogDescription>
            </DialogHeader>
            
            {assignedLibraries.length === 0 && (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium">No Libraries Available</p>
                    <p>Your organization doesn't have any job libraries assigned. Contact your root administrator to assign libraries.</p>
                  </div>
                </div>
              </div>
            )}
            
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="trigger">Trigger Settings</TabsTrigger>
                <TabsTrigger value="steps" disabled={assignedLibraries.length === 0}>Workflow Steps</TabsTrigger>
              </TabsList>
              
              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Job Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter job definition name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value as OrgJobDefinition['status'] })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Enter job description"
                  />
                </div>
              </TabsContent>
              
              <TabsContent value="trigger" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="triggerType">Trigger Type</Label>
                  <Select value={formData.triggerType} onValueChange={(value) => setFormData({ ...formData, triggerType: value as OrgJobDefinition['triggerType'] })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="manual">Manual</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                      <SelectItem value="event">Event-based</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {formData.triggerType === 'scheduled' && (
                  <div className="space-y-2">
                    <Label htmlFor="schedule">Cron Schedule</Label>
                    <Input
                      id="schedule"
                      value={formData.schedule}
                      onChange={(e) => setFormData({ ...formData, schedule: e.target.value })}
                      placeholder="0 2 * * * (daily at 2 AM)"
                    />
                    <p className="text-xs text-gray-500">Use cron expression format</p>
                  </div>
                )}
                
                {formData.triggerType === 'event' && (
                  <div className="space-y-2">
                    <Label htmlFor="eventTrigger">Event Trigger</Label>
                    <Input
                      id="eventTrigger"
                      value={formData.eventTrigger}
                      onChange={(e) => setFormData({ ...formData, eventTrigger: e.target.value })}
                      placeholder="Enter event name or condition"
                    />
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="steps" className="space-y-4">
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-medium text-blue-900 mb-2">Available Libraries</h3>
                    <div className="text-sm text-blue-800">
                      <p>{assignedLibraries.length} libraries assigned to your organization by root admin</p>
                    </div>
                    <div className="mt-2 space-x-2">
                      {assignedLibraries.map((lib) => (
                        <Badge key={lib.id} variant="outline" className="text-xs">
                          {lib.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <h3 className="font-semibold">Add Workflow Step</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="stepLibrary">Select Library</Label>
                      <Select value={currentStep.libraryId} onValueChange={(value) => setCurrentStep({ ...currentStep, libraryId: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a library" />
                        </SelectTrigger>
                        <SelectContent>
                          {assignedLibraries.map((library) => (
                            <SelectItem key={library.id} value={library.id}>
                              {library.name} (v{library.version})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="onSuccess">On Success</Label>
                      <Select value={currentStep.onSuccess} onValueChange={(value) => setCurrentStep({ ...currentStep, onSuccess: value as JobStep['onSuccess'] })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="continue">Continue</SelectItem>
                          <SelectItem value="stop">Stop</SelectItem>
                          <SelectItem value="jump">Jump to Step</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="onFailure">On Failure</Label>
                      <Select value={currentStep.onFailure} onValueChange={(value) => setCurrentStep({ ...currentStep, onFailure: value as JobStep['onFailure'] })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="stop">Stop</SelectItem>
                          <SelectItem value="continue">Continue</SelectItem>
                          <SelectItem value="retry">Retry</SelectItem>
                          <SelectItem value="jump">Jump to Step</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {currentStep.onFailure === 'retry' && (
                      <div className="space-y-2">
                        <Label htmlFor="retryCount">Retry Count</Label>
                        <Input
                          id="retryCount"
                          type="number"
                          min="1"
                          max="10"
                          value={currentStep.retryCount || 1}
                          onChange={(e) => setCurrentStep({ ...currentStep, retryCount: parseInt(e.target.value) })}
                        />
                      </div>
                    )}
                  </div>
                  
                  <Button onClick={addStep} disabled={!currentStep.libraryId}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Step
                  </Button>
                </div>
                
                {formData.steps && formData.steps.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="font-semibold">Workflow Steps</h3>
                    <div className="space-y-2">
                      {formData.steps.map((step, index) => (
                        <div key={step.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline">{step.order}</Badge>
                              <span className="font-medium">{step.libraryName}</span>
                            </div>
                            <div className="text-sm text-gray-500 mt-1">
                              Success: {step.onSuccess} | Failure: {step.onFailure}
                              {step.retryCount && ` (${step.retryCount} retries)`}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm" onClick={() => moveStep(step.id, 'up')} disabled={index === 0}>
                              ↑
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => moveStep(step.id, 'down')} disabled={index === formData.steps!.length - 1}>
                              ↓
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => removeStep(step.id)}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreate}>Create Job Definition</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Available Libraries Info */}
      <Card>
        <CardHeader>
          <CardTitle>Available Libraries</CardTitle>
          <CardDescription>
            Pipeline libraries assigned to your organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          {assignedLibraries.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {assignedLibraries.map((library) => (
                <div key={library.id} className="p-3 border rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <Zap className="h-4 w-4 text-purple-600" />
                    <span className="font-medium">{library.name}</span>
                    <Badge variant="outline" className="text-xs">v{library.version}</Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{library.description}</p>
                  <div className="flex items-center space-x-2">
                    <Badge className="text-xs bg-blue-100 text-blue-800">{library.type}</Badge>
                    <Badge className="text-xs bg-green-100 text-green-800">{library.runtime}</Badge>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No libraries assigned to your organization</p>
              <p className="text-sm text-gray-400">Contact your root administrator to assign pipeline libraries</p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Job Definitions</CardTitle>
          <CardDescription>
            {jobDefinitions.length} job definition{jobDefinitions.length !== 1 ? 's' : ''} total
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Job Definition</TableHead>
                <TableHead>Trigger</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Steps</TableHead>
                <TableHead>Projects</TableHead>
                <TableHead>Last Run</TableHead>
                <TableHead>Created By</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {jobDefinitions.map((definition) => (
                <TableRow key={definition.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <Zap className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <div className="font-medium">{definition.name}</div>
                        <div className="text-sm text-gray-500">{definition.description}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {getTriggerBadge(definition.triggerType)}
                      {definition.triggerType === 'scheduled' && definition.schedule && (
                        <div className="text-xs text-gray-500">{definition.schedule}</div>
                      )}
                      {definition.triggerType === 'event' && definition.eventTrigger && (
                        <div className="text-xs text-gray-500">{definition.eventTrigger}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(definition.status)}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{definition.steps.length} steps</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{definition.assignedProjects} projects</Badge>
                  </TableCell>
                  <TableCell>
                    {definition.lastRun ? (
                      <div className="text-sm text-gray-600">{definition.lastRun}</div>
                    ) : (
                      <span className="text-sm text-gray-500">Never</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-600">{definition.createdBy}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" title="Run Job">
                        <Play className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openViewDialog(definition)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditDialog(definition)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Job Definition</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete "{definition.name}"? This will affect {definition.assignedProjects} project(s).
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDelete(definition.id)}>
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Job Definition Details</DialogTitle>
            <DialogDescription>
              Complete workflow information and step details
            </DialogDescription>
          </DialogHeader>
          {selectedDefinition && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-sm text-gray-700 mb-3">Basic Information</h3>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium">Name:</span>
                      <p className="text-sm text-gray-600">{selectedDefinition.name}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Description:</span>
                      <p className="text-sm text-gray-600">{selectedDefinition.description}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">Status:</span>
                      {getStatusBadge(selectedDefinition.status)}
                    </div>
                    <div>
                      <span className="text-sm font-medium">Created By:</span>
                      <p className="text-sm text-gray-600">{selectedDefinition.createdBy}</p>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-sm text-gray-700 mb-3">Trigger Configuration</h3>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">Type:</span>
                      {getTriggerBadge(selectedDefinition.triggerType)}
                    </div>
                    {selectedDefinition.triggerType === 'scheduled' && selectedDefinition.schedule && (
                      <div>
                        <span className="text-sm font-medium">Schedule:</span>
                        <p className="text-sm text-gray-600">{selectedDefinition.schedule}</p>
                      </div>
                    )}
                    <div>
                      <span className="text-sm font-medium">Assigned Projects:</span>
                      <p className="text-sm text-gray-600">{selectedDefinition.assignedProjects}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Last Run:</span>
                      <p className="text-sm text-gray-600">{selectedDefinition.lastRun || 'Never'}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold text-sm text-gray-700 mb-3">Workflow Steps</h3>
                <div className="space-y-3">
                  {selectedDefinition.steps.map((step, index) => (
                    <div key={step.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full text-sm font-medium">
                        {step.order}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{step.libraryName}</div>
                        <div className="text-sm text-gray-500">
                          Success: {step.onSuccess} | Failure: {step.onFailure}
                          {step.retryCount && ` | Retries: ${step.retryCount}`}
                        </div>
                        {Object.keys(step.parameters).length > 0 && (
                          <div className="text-xs text-gray-400 mt-1">
                            Parameters: {Object.entries(step.parameters).map(([k, v]) => `${k}=${v}`).join(', ')}
                          </div>
                        )}
                      </div>
                      {index < selectedDefinition.steps.length - 1 && (
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}